{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport warning from 'warning';\nimport Col from './Col';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormLabel = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'label',\n  bsPrefix,\n  column = false,\n  visuallyHidden = false,\n  className,\n  htmlFor,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-label');\n  let columnClass = 'col-form-label';\n  if (typeof column === 'string') columnClass = `${columnClass} ${columnClass}-${column}`;\n  const classes = classNames(className, bsPrefix, visuallyHidden && 'visually-hidden', column && columnClass);\n  process.env.NODE_ENV !== \"production\" ? warning(controlId == null || !htmlFor, '`controlId` is ignored on `<FormLabel>` when `htmlFor` is specified.') : void 0;\n  htmlFor = htmlFor || controlId;\n  if (column) return /*#__PURE__*/_jsx(Col, {\n    ref: ref,\n    as: \"label\",\n    className: classes,\n    htmlFor: htmlFor,\n    ...props\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classes,\n    htmlFor: htmlFor,\n    ...props\n  });\n});\nFormLabel.displayName = 'FormLabel';\nexport default FormLabel;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "warning", "Col", "FormContext", "useBootstrapPrefix", "jsx", "_jsx", "FormLabel", "forwardRef", "as", "Component", "bsPrefix", "column", "visuallyHidden", "className", "htmlFor", "props", "ref", "controlId", "columnClass", "classes", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/FormLabel.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport warning from 'warning';\nimport Col from './Col';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormLabel = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'label',\n  bsPrefix,\n  column = false,\n  visuallyHidden = false,\n  className,\n  htmlFor,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-label');\n  let columnClass = 'col-form-label';\n  if (typeof column === 'string') columnClass = `${columnClass} ${columnClass}-${column}`;\n  const classes = classNames(className, bsPrefix, visuallyHidden && 'visually-hidden', column && columnClass);\n  process.env.NODE_ENV !== \"production\" ? warning(controlId == null || !htmlFor, '`controlId` is ignored on `<FormLabel>` when `htmlFor` is specified.') : void 0;\n  htmlFor = htmlFor || controlId;\n  if (column) return /*#__PURE__*/_jsx(Col, {\n    ref: ref,\n    as: \"label\",\n    className: classes,\n    htmlFor: htmlFor,\n    ...props\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classes,\n    htmlFor: htmlFor,\n    ...props\n  });\n});\nFormLabel.displayName = 'FormLabel';\nexport default FormLabel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,OAAO,MAAM,SAAS;AAC7B,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAC;EAC/C;EACAC,EAAE,EAAEC,SAAS,GAAG,OAAO;EACvBC,QAAQ;EACRC,MAAM,GAAG,KAAK;EACdC,cAAc,GAAG,KAAK;EACtBC,SAAS;EACTC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC;EACF,CAAC,GAAGlB,UAAU,CAACG,WAAW,CAAC;EAC3BQ,QAAQ,GAAGP,kBAAkB,CAACO,QAAQ,EAAE,YAAY,CAAC;EACrD,IAAIQ,WAAW,GAAG,gBAAgB;EAClC,IAAI,OAAOP,MAAM,KAAK,QAAQ,EAAEO,WAAW,GAAG,GAAGA,WAAW,IAAIA,WAAW,IAAIP,MAAM,EAAE;EACvF,MAAMQ,OAAO,GAAGtB,UAAU,CAACgB,SAAS,EAAEH,QAAQ,EAAEE,cAAc,IAAI,iBAAiB,EAAED,MAAM,IAAIO,WAAW,CAAC;EAC3GE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtB,OAAO,CAACiB,SAAS,IAAI,IAAI,IAAI,CAACH,OAAO,EAAE,sEAAsE,CAAC,GAAG,KAAK,CAAC;EAC/JA,OAAO,GAAGA,OAAO,IAAIG,SAAS;EAC9B,IAAIN,MAAM,EAAE,OAAO,aAAaN,IAAI,CAACJ,GAAG,EAAE;IACxCe,GAAG,EAAEA,GAAG;IACRR,EAAE,EAAE,OAAO;IACXK,SAAS,EAAEM,OAAO;IAClBL,OAAO,EAAEA,OAAO;IAChB,GAAGC;EACL,CAAC,CAAC;EACF,OAAO,aAAaV,IAAI,CAACI,SAAS,EAAE;IAClCO,GAAG,EAAEA,GAAG;IACRH,SAAS,EAAEM,OAAO;IAClBL,OAAO,EAAEA,OAAO;IAChB,GAAGC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,SAAS,CAACiB,WAAW,GAAG,WAAW;AACnC,eAAejB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}