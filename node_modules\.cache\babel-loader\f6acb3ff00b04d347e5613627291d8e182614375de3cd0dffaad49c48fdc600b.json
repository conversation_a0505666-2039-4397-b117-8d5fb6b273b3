{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nfunction componentWillMount() {\n  // Call this.constructor.gDSFP to support sub-classes.\n  var state = this.constructor.getDerivedStateFromProps(this.props, this.state);\n  if (state !== null && state !== undefined) {\n    this.setState(state);\n  }\n}\nfunction componentWillReceiveProps(nextProps) {\n  // Call this.constructor.gDSFP to support sub-classes.\n  // Use the setState() updater to ensure state isn't stale in certain edge cases.\n  function updater(prevState) {\n    var state = this.constructor.getDerivedStateFromProps(nextProps, prevState);\n    return state !== null && state !== undefined ? state : null;\n  }\n  // Binding \"this\" is important for shallow renderer support.\n  this.setState(updater.bind(this));\n}\nfunction componentWillUpdate(nextProps, nextState) {\n  try {\n    var prevProps = this.props;\n    var prevState = this.state;\n    this.props = nextProps;\n    this.state = nextState;\n    this.__reactInternalSnapshotFlag = true;\n    this.__reactInternalSnapshot = this.getSnapshotBeforeUpdate(prevProps, prevState);\n  } finally {\n    this.props = prevProps;\n    this.state = prevState;\n  }\n}\n\n// React may warn about cWM/cWRP/cWU methods being deprecated.\n// Add a flag to suppress these warnings for this special case.\ncomponentWillMount.__suppressDeprecationWarning = true;\ncomponentWillReceiveProps.__suppressDeprecationWarning = true;\ncomponentWillUpdate.__suppressDeprecationWarning = true;\nfunction polyfill(Component) {\n  var prototype = Component.prototype;\n  if (!prototype || !prototype.isReactComponent) {\n    throw new Error('Can only polyfill class components');\n  }\n  if (typeof Component.getDerivedStateFromProps !== 'function' && typeof prototype.getSnapshotBeforeUpdate !== 'function') {\n    return Component;\n  }\n\n  // If new component APIs are defined, \"unsafe\" lifecycles won't be called.\n  // Error if any of these lifecycles are present,\n  // Because they would work differently between older and newer (16.3+) versions of React.\n  var foundWillMountName = null;\n  var foundWillReceivePropsName = null;\n  var foundWillUpdateName = null;\n  if (typeof prototype.componentWillMount === 'function') {\n    foundWillMountName = 'componentWillMount';\n  } else if (typeof prototype.UNSAFE_componentWillMount === 'function') {\n    foundWillMountName = 'UNSAFE_componentWillMount';\n  }\n  if (typeof prototype.componentWillReceiveProps === 'function') {\n    foundWillReceivePropsName = 'componentWillReceiveProps';\n  } else if (typeof prototype.UNSAFE_componentWillReceiveProps === 'function') {\n    foundWillReceivePropsName = 'UNSAFE_componentWillReceiveProps';\n  }\n  if (typeof prototype.componentWillUpdate === 'function') {\n    foundWillUpdateName = 'componentWillUpdate';\n  } else if (typeof prototype.UNSAFE_componentWillUpdate === 'function') {\n    foundWillUpdateName = 'UNSAFE_componentWillUpdate';\n  }\n  if (foundWillMountName !== null || foundWillReceivePropsName !== null || foundWillUpdateName !== null) {\n    var componentName = Component.displayName || Component.name;\n    var newApiName = typeof Component.getDerivedStateFromProps === 'function' ? 'getDerivedStateFromProps()' : 'getSnapshotBeforeUpdate()';\n    throw Error('Unsafe legacy lifecycles will not be called for components using new component APIs.\\n\\n' + componentName + ' uses ' + newApiName + ' but also contains the following legacy lifecycles:' + (foundWillMountName !== null ? '\\n  ' + foundWillMountName : '') + (foundWillReceivePropsName !== null ? '\\n  ' + foundWillReceivePropsName : '') + (foundWillUpdateName !== null ? '\\n  ' + foundWillUpdateName : '') + '\\n\\nThe above lifecycles should be removed. Learn more about this warning here:\\n' + 'https://fb.me/react-async-component-lifecycle-hooks');\n  }\n\n  // React <= 16.2 does not support static getDerivedStateFromProps.\n  // As a workaround, use cWM and cWRP to invoke the new static lifecycle.\n  // Newer versions of React will ignore these lifecycles if gDSFP exists.\n  if (typeof Component.getDerivedStateFromProps === 'function') {\n    prototype.componentWillMount = componentWillMount;\n    prototype.componentWillReceiveProps = componentWillReceiveProps;\n  }\n\n  // React <= 16.2 does not support getSnapshotBeforeUpdate.\n  // As a workaround, use cWU to invoke the new lifecycle.\n  // Newer versions of React will ignore that lifecycle if gSBU exists.\n  if (typeof prototype.getSnapshotBeforeUpdate === 'function') {\n    if (typeof prototype.componentDidUpdate !== 'function') {\n      throw new Error('Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype');\n    }\n    prototype.componentWillUpdate = componentWillUpdate;\n    var componentDidUpdate = prototype.componentDidUpdate;\n    prototype.componentDidUpdate = function componentDidUpdatePolyfill(prevProps, prevState, maybeSnapshot) {\n      // 16.3+ will not execute our will-update method;\n      // It will pass a snapshot value to did-update though.\n      // Older versions will require our polyfilled will-update value.\n      // We need to handle both cases, but can't just check for the presence of \"maybeSnapshot\",\n      // Because for <= 15.x versions this might be a \"prevContext\" object.\n      // We also can't just check \"__reactInternalSnapshot\",\n      // Because get-snapshot might return a falsy value.\n      // So check for the explicit __reactInternalSnapshotFlag flag to determine behavior.\n      var snapshot = this.__reactInternalSnapshotFlag ? this.__reactInternalSnapshot : maybeSnapshot;\n      componentDidUpdate.call(this, prevProps, prevState, snapshot);\n    };\n  }\n  return Component;\n}\nexport { polyfill };", "map": {"version": 3, "names": ["componentWillMount", "state", "constructor", "getDerivedStateFromProps", "props", "undefined", "setState", "componentWillReceiveProps", "nextProps", "updater", "prevState", "bind", "componentWillUpdate", "nextState", "prevProps", "__reactInternalSnapshotFlag", "__reactInternalSnapshot", "getSnapshotBeforeUpdate", "__suppressDeprecationWarning", "polyfill", "Component", "prototype", "isReactComponent", "Error", "foundWillMountName", "foundWillReceivePropsName", "foundWillUpdateName", "UNSAFE_componentWillMount", "UNSAFE_componentWillReceiveProps", "UNSAFE_componentWillUpdate", "componentName", "displayName", "name", "newApiName", "componentDidUpdate", "componentDidUpdatePolyfill", "maybeSnapshot", "snapshot", "call"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-lifecycles-compat/react-lifecycles-compat.es.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nfunction componentWillMount() {\n  // Call this.constructor.gDSFP to support sub-classes.\n  var state = this.constructor.getDerivedStateFromProps(this.props, this.state);\n  if (state !== null && state !== undefined) {\n    this.setState(state);\n  }\n}\n\nfunction componentWillReceiveProps(nextProps) {\n  // Call this.constructor.gDSFP to support sub-classes.\n  // Use the setState() updater to ensure state isn't stale in certain edge cases.\n  function updater(prevState) {\n    var state = this.constructor.getDerivedStateFromProps(nextProps, prevState);\n    return state !== null && state !== undefined ? state : null;\n  }\n  // Binding \"this\" is important for shallow renderer support.\n  this.setState(updater.bind(this));\n}\n\nfunction componentWillUpdate(nextProps, nextState) {\n  try {\n    var prevProps = this.props;\n    var prevState = this.state;\n    this.props = nextProps;\n    this.state = nextState;\n    this.__reactInternalSnapshotFlag = true;\n    this.__reactInternalSnapshot = this.getSnapshotBeforeUpdate(\n      prevProps,\n      prevState\n    );\n  } finally {\n    this.props = prevProps;\n    this.state = prevState;\n  }\n}\n\n// React may warn about cWM/cWRP/cWU methods being deprecated.\n// Add a flag to suppress these warnings for this special case.\ncomponentWillMount.__suppressDeprecationWarning = true;\ncomponentWillReceiveProps.__suppressDeprecationWarning = true;\ncomponentWillUpdate.__suppressDeprecationWarning = true;\n\nfunction polyfill(Component) {\n  var prototype = Component.prototype;\n\n  if (!prototype || !prototype.isReactComponent) {\n    throw new Error('Can only polyfill class components');\n  }\n\n  if (\n    typeof Component.getDerivedStateFromProps !== 'function' &&\n    typeof prototype.getSnapshotBeforeUpdate !== 'function'\n  ) {\n    return Component;\n  }\n\n  // If new component APIs are defined, \"unsafe\" lifecycles won't be called.\n  // Error if any of these lifecycles are present,\n  // Because they would work differently between older and newer (16.3+) versions of React.\n  var foundWillMountName = null;\n  var foundWillReceivePropsName = null;\n  var foundWillUpdateName = null;\n  if (typeof prototype.componentWillMount === 'function') {\n    foundWillMountName = 'componentWillMount';\n  } else if (typeof prototype.UNSAFE_componentWillMount === 'function') {\n    foundWillMountName = 'UNSAFE_componentWillMount';\n  }\n  if (typeof prototype.componentWillReceiveProps === 'function') {\n    foundWillReceivePropsName = 'componentWillReceiveProps';\n  } else if (typeof prototype.UNSAFE_componentWillReceiveProps === 'function') {\n    foundWillReceivePropsName = 'UNSAFE_componentWillReceiveProps';\n  }\n  if (typeof prototype.componentWillUpdate === 'function') {\n    foundWillUpdateName = 'componentWillUpdate';\n  } else if (typeof prototype.UNSAFE_componentWillUpdate === 'function') {\n    foundWillUpdateName = 'UNSAFE_componentWillUpdate';\n  }\n  if (\n    foundWillMountName !== null ||\n    foundWillReceivePropsName !== null ||\n    foundWillUpdateName !== null\n  ) {\n    var componentName = Component.displayName || Component.name;\n    var newApiName =\n      typeof Component.getDerivedStateFromProps === 'function'\n        ? 'getDerivedStateFromProps()'\n        : 'getSnapshotBeforeUpdate()';\n\n    throw Error(\n      'Unsafe legacy lifecycles will not be called for components using new component APIs.\\n\\n' +\n        componentName +\n        ' uses ' +\n        newApiName +\n        ' but also contains the following legacy lifecycles:' +\n        (foundWillMountName !== null ? '\\n  ' + foundWillMountName : '') +\n        (foundWillReceivePropsName !== null\n          ? '\\n  ' + foundWillReceivePropsName\n          : '') +\n        (foundWillUpdateName !== null ? '\\n  ' + foundWillUpdateName : '') +\n        '\\n\\nThe above lifecycles should be removed. Learn more about this warning here:\\n' +\n        'https://fb.me/react-async-component-lifecycle-hooks'\n    );\n  }\n\n  // React <= 16.2 does not support static getDerivedStateFromProps.\n  // As a workaround, use cWM and cWRP to invoke the new static lifecycle.\n  // Newer versions of React will ignore these lifecycles if gDSFP exists.\n  if (typeof Component.getDerivedStateFromProps === 'function') {\n    prototype.componentWillMount = componentWillMount;\n    prototype.componentWillReceiveProps = componentWillReceiveProps;\n  }\n\n  // React <= 16.2 does not support getSnapshotBeforeUpdate.\n  // As a workaround, use cWU to invoke the new lifecycle.\n  // Newer versions of React will ignore that lifecycle if gSBU exists.\n  if (typeof prototype.getSnapshotBeforeUpdate === 'function') {\n    if (typeof prototype.componentDidUpdate !== 'function') {\n      throw new Error(\n        'Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype'\n      );\n    }\n\n    prototype.componentWillUpdate = componentWillUpdate;\n\n    var componentDidUpdate = prototype.componentDidUpdate;\n\n    prototype.componentDidUpdate = function componentDidUpdatePolyfill(\n      prevProps,\n      prevState,\n      maybeSnapshot\n    ) {\n      // 16.3+ will not execute our will-update method;\n      // It will pass a snapshot value to did-update though.\n      // Older versions will require our polyfilled will-update value.\n      // We need to handle both cases, but can't just check for the presence of \"maybeSnapshot\",\n      // Because for <= 15.x versions this might be a \"prevContext\" object.\n      // We also can't just check \"__reactInternalSnapshot\",\n      // Because get-snapshot might return a falsy value.\n      // So check for the explicit __reactInternalSnapshotFlag flag to determine behavior.\n      var snapshot = this.__reactInternalSnapshotFlag\n        ? this.__reactInternalSnapshot\n        : maybeSnapshot;\n\n      componentDidUpdate.call(this, prevProps, prevState, snapshot);\n    };\n  }\n\n  return Component;\n}\n\nexport { polyfill };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,kBAAkBA,CAAA,EAAG;EAC5B;EACA,IAAIC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACC,wBAAwB,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAACH,KAAK,CAAC;EAC7E,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKI,SAAS,EAAE;IACzC,IAAI,CAACC,QAAQ,CAACL,KAAK,CAAC;EACtB;AACF;AAEA,SAASM,yBAAyBA,CAACC,SAAS,EAAE;EAC5C;EACA;EACA,SAASC,OAAOA,CAACC,SAAS,EAAE;IAC1B,IAAIT,KAAK,GAAG,IAAI,CAACC,WAAW,CAACC,wBAAwB,CAACK,SAAS,EAAEE,SAAS,CAAC;IAC3E,OAAOT,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKI,SAAS,GAAGJ,KAAK,GAAG,IAAI;EAC7D;EACA;EACA,IAAI,CAACK,QAAQ,CAACG,OAAO,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC;AAEA,SAASC,mBAAmBA,CAACJ,SAAS,EAAEK,SAAS,EAAE;EACjD,IAAI;IACF,IAAIC,SAAS,GAAG,IAAI,CAACV,KAAK;IAC1B,IAAIM,SAAS,GAAG,IAAI,CAACT,KAAK;IAC1B,IAAI,CAACG,KAAK,GAAGI,SAAS;IACtB,IAAI,CAACP,KAAK,GAAGY,SAAS;IACtB,IAAI,CAACE,2BAA2B,GAAG,IAAI;IACvC,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACC,uBAAuB,CACzDH,SAAS,EACTJ,SACF,CAAC;EACH,CAAC,SAAS;IACR,IAAI,CAACN,KAAK,GAAGU,SAAS;IACtB,IAAI,CAACb,KAAK,GAAGS,SAAS;EACxB;AACF;;AAEA;AACA;AACAV,kBAAkB,CAACkB,4BAA4B,GAAG,IAAI;AACtDX,yBAAyB,CAACW,4BAA4B,GAAG,IAAI;AAC7DN,mBAAmB,CAACM,4BAA4B,GAAG,IAAI;AAEvD,SAASC,QAAQA,CAACC,SAAS,EAAE;EAC3B,IAAIC,SAAS,GAAGD,SAAS,CAACC,SAAS;EAEnC,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAACC,gBAAgB,EAAE;IAC7C,MAAM,IAAIC,KAAK,CAAC,oCAAoC,CAAC;EACvD;EAEA,IACE,OAAOH,SAAS,CAACjB,wBAAwB,KAAK,UAAU,IACxD,OAAOkB,SAAS,CAACJ,uBAAuB,KAAK,UAAU,EACvD;IACA,OAAOG,SAAS;EAClB;;EAEA;EACA;EACA;EACA,IAAII,kBAAkB,GAAG,IAAI;EAC7B,IAAIC,yBAAyB,GAAG,IAAI;EACpC,IAAIC,mBAAmB,GAAG,IAAI;EAC9B,IAAI,OAAOL,SAAS,CAACrB,kBAAkB,KAAK,UAAU,EAAE;IACtDwB,kBAAkB,GAAG,oBAAoB;EAC3C,CAAC,MAAM,IAAI,OAAOH,SAAS,CAACM,yBAAyB,KAAK,UAAU,EAAE;IACpEH,kBAAkB,GAAG,2BAA2B;EAClD;EACA,IAAI,OAAOH,SAAS,CAACd,yBAAyB,KAAK,UAAU,EAAE;IAC7DkB,yBAAyB,GAAG,2BAA2B;EACzD,CAAC,MAAM,IAAI,OAAOJ,SAAS,CAACO,gCAAgC,KAAK,UAAU,EAAE;IAC3EH,yBAAyB,GAAG,kCAAkC;EAChE;EACA,IAAI,OAAOJ,SAAS,CAACT,mBAAmB,KAAK,UAAU,EAAE;IACvDc,mBAAmB,GAAG,qBAAqB;EAC7C,CAAC,MAAM,IAAI,OAAOL,SAAS,CAACQ,0BAA0B,KAAK,UAAU,EAAE;IACrEH,mBAAmB,GAAG,4BAA4B;EACpD;EACA,IACEF,kBAAkB,KAAK,IAAI,IAC3BC,yBAAyB,KAAK,IAAI,IAClCC,mBAAmB,KAAK,IAAI,EAC5B;IACA,IAAII,aAAa,GAAGV,SAAS,CAACW,WAAW,IAAIX,SAAS,CAACY,IAAI;IAC3D,IAAIC,UAAU,GACZ,OAAOb,SAAS,CAACjB,wBAAwB,KAAK,UAAU,GACpD,4BAA4B,GAC5B,2BAA2B;IAEjC,MAAMoB,KAAK,CACT,0FAA0F,GACxFO,aAAa,GACb,QAAQ,GACRG,UAAU,GACV,qDAAqD,IACpDT,kBAAkB,KAAK,IAAI,GAAG,MAAM,GAAGA,kBAAkB,GAAG,EAAE,CAAC,IAC/DC,yBAAyB,KAAK,IAAI,GAC/B,MAAM,GAAGA,yBAAyB,GAClC,EAAE,CAAC,IACNC,mBAAmB,KAAK,IAAI,GAAG,MAAM,GAAGA,mBAAmB,GAAG,EAAE,CAAC,GAClE,mFAAmF,GACnF,qDACJ,CAAC;EACH;;EAEA;EACA;EACA;EACA,IAAI,OAAON,SAAS,CAACjB,wBAAwB,KAAK,UAAU,EAAE;IAC5DkB,SAAS,CAACrB,kBAAkB,GAAGA,kBAAkB;IACjDqB,SAAS,CAACd,yBAAyB,GAAGA,yBAAyB;EACjE;;EAEA;EACA;EACA;EACA,IAAI,OAAOc,SAAS,CAACJ,uBAAuB,KAAK,UAAU,EAAE;IAC3D,IAAI,OAAOI,SAAS,CAACa,kBAAkB,KAAK,UAAU,EAAE;MACtD,MAAM,IAAIX,KAAK,CACb,mHACF,CAAC;IACH;IAEAF,SAAS,CAACT,mBAAmB,GAAGA,mBAAmB;IAEnD,IAAIsB,kBAAkB,GAAGb,SAAS,CAACa,kBAAkB;IAErDb,SAAS,CAACa,kBAAkB,GAAG,SAASC,0BAA0BA,CAChErB,SAAS,EACTJ,SAAS,EACT0B,aAAa,EACb;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIC,QAAQ,GAAG,IAAI,CAACtB,2BAA2B,GAC3C,IAAI,CAACC,uBAAuB,GAC5BoB,aAAa;MAEjBF,kBAAkB,CAACI,IAAI,CAAC,IAAI,EAAExB,SAAS,EAAEJ,SAAS,EAAE2B,QAAQ,CAAC;IAC/D,CAAC;EACH;EAEA,OAAOjB,SAAS;AAClB;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}