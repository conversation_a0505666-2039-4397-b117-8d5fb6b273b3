{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\LearningOutcomesPage.js\";\nimport React from 'react';\nimport learningOutcomes from '../data/learningOutcomes';\nimport './LearningOutcomesPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LearningOutcomesPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"learning-outcomes\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Daily Learning Outcomes\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: learningOutcomes.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [item.day, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 25\n        }, this), \" \", item.outcome]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 9\n  }, this);\n};\n_c = LearningOutcomesPage;\nexport default LearningOutcomesPage;\nvar _c;\n$RefreshReg$(_c, \"LearningOutcomesPage\");", "map": {"version": 3, "names": ["React", "learningOutcomes", "jsxDEV", "_jsxDEV", "LearningOutcomesPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "day", "outcome", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/LearningOutcomesPage.js"], "sourcesContent": ["import React from 'react';\nimport learningOutcomes from '../data/learningOutcomes';\nimport './LearningOutcomesPage.css';\n\nconst LearningOutcomesPage = () => {\n    return (\n        <div className=\"learning-outcomes\">\n            <h1>Daily Learning Outcomes</h1>\n            <ul>\n                {learningOutcomes.map((item, index) => (\n                    <li key={index}>\n                        <strong>{item.day}:</strong> {item.outcome}\n                    </li>\n                ))}\n            </ul>\n        </div>\n    );\n};\n\nexport default LearningOutcomesPage;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,oBACID,OAAA;IAAKE,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAC9BH,OAAA;MAAAG,QAAA,EAAI;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChCP,OAAA;MAAAG,QAAA,EACKL,gBAAgB,CAACU,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC9BV,OAAA;QAAAG,QAAA,gBACIH,OAAA;UAAAG,QAAA,GAASM,IAAI,CAACE,GAAG,EAAC,GAAC;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACE,IAAI,CAACG,OAAO;MAAA,GADrCF,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEd,CAAC;AAACM,EAAA,GAbIZ,oBAAoB;AAe1B,eAAeA,oBAAoB;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}