{"ast": null, "code": "/**\n * Copyright 2013-2014, Facebook, Inc.\n * All rights reserved.\n * https://github.com/facebook/react/blob/2aeb8a2a6beb00617a4217f7f8284924fa2ad819/src/vendor/core/hyphenateStyleName.js\n */\nimport hyphenate from './hyphenate';\nvar msPattern = /^ms-/;\nexport default function hyphenateStyleName(string) {\n  return hyphenate(string).replace(msPattern, '-ms-');\n}", "map": {"version": 3, "names": ["hyphenate", "msPattern", "hyphenateStyleName", "string", "replace"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/dom-helpers/esm/hyphenateStyle.js"], "sourcesContent": ["/**\n * Copyright 2013-2014, Facebook, Inc.\n * All rights reserved.\n * https://github.com/facebook/react/blob/2aeb8a2a6beb00617a4217f7f8284924fa2ad819/src/vendor/core/hyphenateStyleName.js\n */\nimport hyphenate from './hyphenate';\nvar msPattern = /^ms-/;\nexport default function hyphenateStyleName(string) {\n  return hyphenate(string).replace(msPattern, '-ms-');\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAOA,SAAS,MAAM,aAAa;AACnC,IAAIC,SAAS,GAAG,MAAM;AACtB,eAAe,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EACjD,OAAOH,SAAS,CAACG,MAAM,CAAC,CAACC,OAAO,CAACH,SAAS,EAAE,MAAM,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}