{"ast": null, "code": "const _excluded = [\"as\", \"disabled\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function isTrivialHref(href) {\n  return !href || href.trim() === '#';\n}\nexport function useButtonProps({\n  tagName,\n  disabled,\n  href,\n  target,\n  rel,\n  role,\n  onClick,\n  tabIndex = 0,\n  type\n}) {\n  if (!tagName) {\n    if (href != null || target != null || rel != null) {\n      tagName = 'a';\n    } else {\n      tagName = 'button';\n    }\n  }\n  const meta = {\n    tagName\n  };\n  if (tagName === 'button') {\n    return [{\n      type: type || 'button',\n      disabled\n    }, meta];\n  }\n  const handleClick = event => {\n    if (disabled || tagName === 'a' && isTrivialHref(href)) {\n      event.preventDefault();\n    }\n    if (disabled) {\n      event.stopPropagation();\n      return;\n    }\n    onClick == null ? void 0 : onClick(event);\n  };\n  const handleKeyDown = event => {\n    if (event.key === ' ') {\n      event.preventDefault();\n      handleClick(event);\n    }\n  };\n  if (tagName === 'a') {\n    // Ensure there's a href so Enter can trigger anchor button.\n    href || (href = '#');\n    if (disabled) {\n      href = undefined;\n    }\n  }\n  return [{\n    role: role != null ? role : 'button',\n    // explicitly undefined so that it overrides the props disabled in a spread\n    // e.g. <Tag {...props} {...hookProps} />\n    disabled: undefined,\n    tabIndex: disabled ? undefined : tabIndex,\n    href,\n    target: tagName === 'a' ? target : undefined,\n    'aria-disabled': !disabled ? undefined : disabled,\n    rel: tagName === 'a' ? rel : undefined,\n    onClick: handleClick,\n    onKeyDown: handleKeyDown\n  }, meta];\n}\nconst Button = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      as: asProp,\n      disabled\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [buttonProps, {\n    tagName: Component\n  }] = useButtonProps(Object.assign({\n    tagName: asProp,\n    disabled\n  }, props));\n  return /*#__PURE__*/_jsx(Component, Object.assign({}, props, buttonProps, {\n    ref: ref\n  }));\n});\nButton.displayName = 'Button';\nexport default Button;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "React", "jsx", "_jsx", "isTrivialHref", "href", "trim", "useButtonProps", "tagName", "disabled", "target", "rel", "role", "onClick", "tabIndex", "type", "meta", "handleClick", "event", "preventDefault", "stopPropagation", "handleKeyDown", "key", "undefined", "onKeyDown", "<PERSON><PERSON>", "forwardRef", "_ref", "ref", "as", "asProp", "props", "buttonProps", "Component", "Object", "assign", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/@restart/ui/esm/Button.js"], "sourcesContent": ["const _excluded = [\"as\", \"disabled\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function isTrivialHref(href) {\n  return !href || href.trim() === '#';\n}\nexport function useButtonProps({\n  tagName,\n  disabled,\n  href,\n  target,\n  rel,\n  role,\n  onClick,\n  tabIndex = 0,\n  type\n}) {\n  if (!tagName) {\n    if (href != null || target != null || rel != null) {\n      tagName = 'a';\n    } else {\n      tagName = 'button';\n    }\n  }\n  const meta = {\n    tagName\n  };\n  if (tagName === 'button') {\n    return [{\n      type: type || 'button',\n      disabled\n    }, meta];\n  }\n  const handleClick = event => {\n    if (disabled || tagName === 'a' && isTrivialHref(href)) {\n      event.preventDefault();\n    }\n    if (disabled) {\n      event.stopPropagation();\n      return;\n    }\n    onClick == null ? void 0 : onClick(event);\n  };\n  const handleKeyDown = event => {\n    if (event.key === ' ') {\n      event.preventDefault();\n      handleClick(event);\n    }\n  };\n  if (tagName === 'a') {\n    // Ensure there's a href so Enter can trigger anchor button.\n    href || (href = '#');\n    if (disabled) {\n      href = undefined;\n    }\n  }\n  return [{\n    role: role != null ? role : 'button',\n    // explicitly undefined so that it overrides the props disabled in a spread\n    // e.g. <Tag {...props} {...hookProps} />\n    disabled: undefined,\n    tabIndex: disabled ? undefined : tabIndex,\n    href,\n    target: tagName === 'a' ? target : undefined,\n    'aria-disabled': !disabled ? undefined : disabled,\n    rel: tagName === 'a' ? rel : undefined,\n    onClick: handleClick,\n    onKeyDown: handleKeyDown\n  }, meta];\n}\nconst Button = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      as: asProp,\n      disabled\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [buttonProps, {\n    tagName: Component\n  }] = useButtonProps(Object.assign({\n    tagName: asProp,\n    disabled\n  }, props));\n  return /*#__PURE__*/_jsx(Component, Object.assign({}, props, buttonProps, {\n    ref: ref\n  }));\n});\nButton.displayName = 'Button';\nexport default Button;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC;AACpC,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IAAE,IAAIF,CAAC,CAACK,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,EAAE;IAAUD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACpM,OAAO,KAAKK,KAAK,MAAM,OAAO;AAC9B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,CAACA,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,GAAG;AACrC;AACA,OAAO,SAASC,cAAcA,CAAC;EAC7BC,OAAO;EACPC,QAAQ;EACRJ,IAAI;EACJK,MAAM;EACNC,GAAG;EACHC,IAAI;EACJC,OAAO;EACPC,QAAQ,GAAG,CAAC;EACZC;AACF,CAAC,EAAE;EACD,IAAI,CAACP,OAAO,EAAE;IACZ,IAAIH,IAAI,IAAI,IAAI,IAAIK,MAAM,IAAI,IAAI,IAAIC,GAAG,IAAI,IAAI,EAAE;MACjDH,OAAO,GAAG,GAAG;IACf,CAAC,MAAM;MACLA,OAAO,GAAG,QAAQ;IACpB;EACF;EACA,MAAMQ,IAAI,GAAG;IACXR;EACF,CAAC;EACD,IAAIA,OAAO,KAAK,QAAQ,EAAE;IACxB,OAAO,CAAC;MACNO,IAAI,EAAEA,IAAI,IAAI,QAAQ;MACtBN;IACF,CAAC,EAAEO,IAAI,CAAC;EACV;EACA,MAAMC,WAAW,GAAGC,KAAK,IAAI;IAC3B,IAAIT,QAAQ,IAAID,OAAO,KAAK,GAAG,IAAIJ,aAAa,CAACC,IAAI,CAAC,EAAE;MACtDa,KAAK,CAACC,cAAc,CAAC,CAAC;IACxB;IACA,IAAIV,QAAQ,EAAE;MACZS,KAAK,CAACE,eAAe,CAAC,CAAC;MACvB;IACF;IACAP,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,KAAK,CAAC;EAC3C,CAAC;EACD,MAAMG,aAAa,GAAGH,KAAK,IAAI;IAC7B,IAAIA,KAAK,CAACI,GAAG,KAAK,GAAG,EAAE;MACrBJ,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBF,WAAW,CAACC,KAAK,CAAC;IACpB;EACF,CAAC;EACD,IAAIV,OAAO,KAAK,GAAG,EAAE;IACnB;IACAH,IAAI,KAAKA,IAAI,GAAG,GAAG,CAAC;IACpB,IAAII,QAAQ,EAAE;MACZJ,IAAI,GAAGkB,SAAS;IAClB;EACF;EACA,OAAO,CAAC;IACNX,IAAI,EAAEA,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,QAAQ;IACpC;IACA;IACAH,QAAQ,EAAEc,SAAS;IACnBT,QAAQ,EAAEL,QAAQ,GAAGc,SAAS,GAAGT,QAAQ;IACzCT,IAAI;IACJK,MAAM,EAAEF,OAAO,KAAK,GAAG,GAAGE,MAAM,GAAGa,SAAS;IAC5C,eAAe,EAAE,CAACd,QAAQ,GAAGc,SAAS,GAAGd,QAAQ;IACjDE,GAAG,EAAEH,OAAO,KAAK,GAAG,GAAGG,GAAG,GAAGY,SAAS;IACtCV,OAAO,EAAEI,WAAW;IACpBO,SAAS,EAAEH;EACb,CAAC,EAAEL,IAAI,CAAC;AACV;AACA,MAAMS,MAAM,GAAG,aAAaxB,KAAK,CAACyB,UAAU,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;EAC1D,IAAI;MACAC,EAAE,EAAEC,MAAM;MACVrB;IACF,CAAC,GAAGkB,IAAI;IACRI,KAAK,GAAGtC,6BAA6B,CAACkC,IAAI,EAAEnC,SAAS,CAAC;EACxD,MAAM,CAACwC,WAAW,EAAE;IAClBxB,OAAO,EAAEyB;EACX,CAAC,CAAC,GAAG1B,cAAc,CAAC2B,MAAM,CAACC,MAAM,CAAC;IAChC3B,OAAO,EAAEsB,MAAM;IACfrB;EACF,CAAC,EAAEsB,KAAK,CAAC,CAAC;EACV,OAAO,aAAa5B,IAAI,CAAC8B,SAAS,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,KAAK,EAAEC,WAAW,EAAE;IACxEJ,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFH,MAAM,CAACW,WAAW,GAAG,QAAQ;AAC7B,eAAeX,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}