{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_dropbox = register(\"dropbox\", {\n  \"color\": \"#1081DE\",\n  \"path\": \"M0 0v64h64V0zm41.5 41.2L32 46.9l-9.4-5.7v-2.1l2.8 1.8 6.6-5.5 6.6 5.5 2.8-1.8v2.1zm6.5-7.5-9.4 6.1-6.6-5.5-6.6 5.5-9.4-6.1 6.5-5.2-6.5-5.2 9.4-6.1 6.6 5.5 6.6-5.5 9.4 6.1-6.5 5.2zm-25.5-5.2 9.5 5.9 9.5-5.9-9.5-5.9z\"\n});\nexport { _socialIcons_dropbox as default };", "map": {"version": 3, "names": ["register", "_socialIcons_dropbox", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/dropbox.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_dropbox = register(\"dropbox\", {\"color\":\"#1081DE\",\"path\":\"M0 0v64h64V0zm41.5 41.2L32 46.9l-9.4-5.7v-2.1l2.8 1.8 6.6-5.5 6.6 5.5 2.8-1.8v2.1zm6.5-7.5-9.4 6.1-6.6-5.5-6.6 5.5-9.4-6.1 6.5-5.2-6.5-5.2 9.4-6.1 6.6 5.5 6.6-5.5 9.4 6.1-6.5 5.2zm-25.5-5.2 9.5 5.9 9.5-5.9-9.5-5.9z\"});\n\nexport { _socialIcons_dropbox as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,oBAAoB,GAAGD,QAAQ,CAAC,SAAS,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAwN,CAAC,CAAC;AAEnS,SAASC,oBAAoB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}