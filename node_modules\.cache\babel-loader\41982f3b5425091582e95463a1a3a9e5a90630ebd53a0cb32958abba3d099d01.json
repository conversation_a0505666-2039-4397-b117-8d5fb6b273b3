{"ast": null, "code": "import useMergedRefs from '@restart/hooks/useMergedRefs';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useIsomorphicEffect from '@restart/hooks/useIsomorphicEffect';\nimport React, { useRef, cloneElement, useState } from 'react';\nimport NoopTransition from './NoopTransition';\nimport RTGTransition from './RTGTransition';\nimport { getChildRef } from './utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useTransition({\n  in: inProp,\n  onTransition\n}) {\n  const ref = useRef(null);\n  const isInitialRef = useRef(true);\n  const handleTransition = useEventCallback(onTransition);\n  useIsomorphicEffect(() => {\n    if (!ref.current) {\n      return undefined;\n    }\n    let stale = false;\n    handleTransition({\n      in: inProp,\n      element: ref.current,\n      initial: isInitialRef.current,\n      isStale: () => stale\n    });\n    return () => {\n      stale = true;\n    };\n  }, [inProp, handleTransition]);\n  useIsomorphicEffect(() => {\n    isInitialRef.current = false;\n    // this is for strict mode\n    return () => {\n      isInitialRef.current = true;\n    };\n  }, []);\n  return ref;\n}\n/**\n * Adapts an imperative transition function to a subset of the RTG `<Transition>` component API.\n *\n * ImperativeTransition does not support mounting options or `appear` at the moment, meaning\n * that it always acts like: `mountOnEnter={true} unmountOnExit={true} appear={true}`\n */\nexport default function ImperativeTransition({\n  children,\n  in: inProp,\n  onExited,\n  onEntered,\n  transition\n}) {\n  const [exited, setExited] = useState(!inProp);\n\n  // TODO: I think this needs to be in an effect\n  if (inProp && exited) {\n    setExited(false);\n  }\n  const ref = useTransition({\n    in: !!inProp,\n    onTransition: options => {\n      const onFinish = () => {\n        if (options.isStale()) return;\n        if (options.in) {\n          onEntered == null ? void 0 : onEntered(options.element, options.initial);\n        } else {\n          setExited(true);\n          onExited == null ? void 0 : onExited(options.element);\n        }\n      };\n      Promise.resolve(transition(options)).then(onFinish, error => {\n        if (!options.in) setExited(true);\n        throw error;\n      });\n    }\n  });\n  const combinedRef = useMergedRefs(ref, getChildRef(children));\n  return exited && !inProp ? null : /*#__PURE__*/cloneElement(children, {\n    ref: combinedRef\n  });\n}\nexport function renderTransition(component, runTransition, props) {\n  if (component) {\n    return /*#__PURE__*/_jsx(RTGTransition, Object.assign({}, props, {\n      component: component\n    }));\n  }\n  if (runTransition) {\n    return /*#__PURE__*/_jsx(ImperativeTransition, Object.assign({}, props, {\n      transition: runTransition\n    }));\n  }\n  return /*#__PURE__*/_jsx(NoopTransition, Object.assign({}, props));\n}", "map": {"version": 3, "names": ["useMergedRefs", "useEventCallback", "useIsomorphicEffect", "React", "useRef", "cloneElement", "useState", "NoopTransition", "RTGTransition", "getChildRef", "jsx", "_jsx", "useTransition", "in", "inProp", "onTransition", "ref", "isInitialRef", "handleTransition", "current", "undefined", "stale", "element", "initial", "isStale", "ImperativeTransition", "children", "onExited", "onEntered", "transition", "exited", "setExited", "options", "onFinish", "Promise", "resolve", "then", "error", "combinedRef", "renderTransition", "component", "runTransition", "props", "Object", "assign"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/@restart/ui/esm/ImperativeTransition.js"], "sourcesContent": ["import useMergedRefs from '@restart/hooks/useMergedRefs';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useIsomorphicEffect from '@restart/hooks/useIsomorphicEffect';\nimport React, { useRef, cloneElement, useState } from 'react';\nimport NoopTransition from './NoopTransition';\nimport RTGTransition from './RTGTransition';\nimport { getChildRef } from './utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useTransition({\n  in: inProp,\n  onTransition\n}) {\n  const ref = useRef(null);\n  const isInitialRef = useRef(true);\n  const handleTransition = useEventCallback(onTransition);\n  useIsomorphicEffect(() => {\n    if (!ref.current) {\n      return undefined;\n    }\n    let stale = false;\n    handleTransition({\n      in: inProp,\n      element: ref.current,\n      initial: isInitialRef.current,\n      isStale: () => stale\n    });\n    return () => {\n      stale = true;\n    };\n  }, [inProp, handleTransition]);\n  useIsomorphicEffect(() => {\n    isInitialRef.current = false;\n    // this is for strict mode\n    return () => {\n      isInitialRef.current = true;\n    };\n  }, []);\n  return ref;\n}\n/**\n * Adapts an imperative transition function to a subset of the RTG `<Transition>` component API.\n *\n * ImperativeTransition does not support mounting options or `appear` at the moment, meaning\n * that it always acts like: `mountOnEnter={true} unmountOnExit={true} appear={true}`\n */\nexport default function ImperativeTransition({\n  children,\n  in: inProp,\n  onExited,\n  onEntered,\n  transition\n}) {\n  const [exited, setExited] = useState(!inProp);\n\n  // TODO: I think this needs to be in an effect\n  if (inProp && exited) {\n    setExited(false);\n  }\n  const ref = useTransition({\n    in: !!inProp,\n    onTransition: options => {\n      const onFinish = () => {\n        if (options.isStale()) return;\n        if (options.in) {\n          onEntered == null ? void 0 : onEntered(options.element, options.initial);\n        } else {\n          setExited(true);\n          onExited == null ? void 0 : onExited(options.element);\n        }\n      };\n      Promise.resolve(transition(options)).then(onFinish, error => {\n        if (!options.in) setExited(true);\n        throw error;\n      });\n    }\n  });\n  const combinedRef = useMergedRefs(ref, getChildRef(children));\n  return exited && !inProp ? null : /*#__PURE__*/cloneElement(children, {\n    ref: combinedRef\n  });\n}\nexport function renderTransition(component, runTransition, props) {\n  if (component) {\n    return /*#__PURE__*/_jsx(RTGTransition, Object.assign({}, props, {\n      component: component\n    }));\n  }\n  if (runTransition) {\n    return /*#__PURE__*/_jsx(ImperativeTransition, Object.assign({}, props, {\n      transition: runTransition\n    }));\n  }\n  return /*#__PURE__*/_jsx(NoopTransition, Object.assign({}, props));\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,8BAA8B;AACxD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,KAAK,IAAIC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,OAAO;AAC7D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,WAAW,QAAQ,SAAS;AACrC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,aAAaA,CAAC;EAC5BC,EAAE,EAAEC,MAAM;EACVC;AACF,CAAC,EAAE;EACD,MAAMC,GAAG,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACxB,MAAMa,YAAY,GAAGb,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMc,gBAAgB,GAAGjB,gBAAgB,CAACc,YAAY,CAAC;EACvDb,mBAAmB,CAAC,MAAM;IACxB,IAAI,CAACc,GAAG,CAACG,OAAO,EAAE;MAChB,OAAOC,SAAS;IAClB;IACA,IAAIC,KAAK,GAAG,KAAK;IACjBH,gBAAgB,CAAC;MACfL,EAAE,EAAEC,MAAM;MACVQ,OAAO,EAAEN,GAAG,CAACG,OAAO;MACpBI,OAAO,EAAEN,YAAY,CAACE,OAAO;MAC7BK,OAAO,EAAEA,CAAA,KAAMH;IACjB,CAAC,CAAC;IACF,OAAO,MAAM;MACXA,KAAK,GAAG,IAAI;IACd,CAAC;EACH,CAAC,EAAE,CAACP,MAAM,EAAEI,gBAAgB,CAAC,CAAC;EAC9BhB,mBAAmB,CAAC,MAAM;IACxBe,YAAY,CAACE,OAAO,GAAG,KAAK;IAC5B;IACA,OAAO,MAAM;MACXF,YAAY,CAACE,OAAO,GAAG,IAAI;IAC7B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOH,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASS,oBAAoBA,CAAC;EAC3CC,QAAQ;EACRb,EAAE,EAAEC,MAAM;EACVa,QAAQ;EACRC,SAAS;EACTC;AACF,CAAC,EAAE;EACD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,CAACQ,MAAM,CAAC;;EAE7C;EACA,IAAIA,MAAM,IAAIgB,MAAM,EAAE;IACpBC,SAAS,CAAC,KAAK,CAAC;EAClB;EACA,MAAMf,GAAG,GAAGJ,aAAa,CAAC;IACxBC,EAAE,EAAE,CAAC,CAACC,MAAM;IACZC,YAAY,EAAEiB,OAAO,IAAI;MACvB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;QACrB,IAAID,OAAO,CAACR,OAAO,CAAC,CAAC,EAAE;QACvB,IAAIQ,OAAO,CAACnB,EAAE,EAAE;UACde,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,OAAO,CAACV,OAAO,EAAEU,OAAO,CAACT,OAAO,CAAC;QAC1E,CAAC,MAAM;UACLQ,SAAS,CAAC,IAAI,CAAC;UACfJ,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACK,OAAO,CAACV,OAAO,CAAC;QACvD;MACF,CAAC;MACDY,OAAO,CAACC,OAAO,CAACN,UAAU,CAACG,OAAO,CAAC,CAAC,CAACI,IAAI,CAACH,QAAQ,EAAEI,KAAK,IAAI;QAC3D,IAAI,CAACL,OAAO,CAACnB,EAAE,EAAEkB,SAAS,CAAC,IAAI,CAAC;QAChC,MAAMM,KAAK;MACb,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAMC,WAAW,GAAGtC,aAAa,CAACgB,GAAG,EAAEP,WAAW,CAACiB,QAAQ,CAAC,CAAC;EAC7D,OAAOI,MAAM,IAAI,CAAChB,MAAM,GAAG,IAAI,GAAG,aAAaT,YAAY,CAACqB,QAAQ,EAAE;IACpEV,GAAG,EAAEsB;EACP,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,aAAa,EAAEC,KAAK,EAAE;EAChE,IAAIF,SAAS,EAAE;IACb,OAAO,aAAa7B,IAAI,CAACH,aAAa,EAAEmC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,EAAE;MAC/DF,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL;EACA,IAAIC,aAAa,EAAE;IACjB,OAAO,aAAa9B,IAAI,CAACc,oBAAoB,EAAEkB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,EAAE;MACtEb,UAAU,EAAEY;IACd,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAa9B,IAAI,CAACJ,cAAc,EAAEoC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}