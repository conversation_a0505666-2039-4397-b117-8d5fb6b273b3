{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_pixiv = register(\"pixiv\", {\n  \"color\": \"#0097fa\",\n  \"path\": \"M0 0v64h64V0zm33.553 16.469c-11.844 0-19.903 9.146-19.903 9.146l2.27 3.606s1.26.106.592-2.018c.573-1.086 1.698-2.545 3.892-4.232v24.008c-.946.268-2.194.768-1.34 1.623h6.518c.86-.861-.493-1.38-1.32-1.623v-5.663s4.469 1.756 9.29 1.756c4.234 0 8.088-1.26 10.954-3.537 2.869-2.264 4.712-5.642 4.703-9.506a12.75 12.75 0 0 0-4.41-9.709c-2.793-2.438-6.705-3.847-11.246-3.847zm-.397 2.027c3.601.003 6.425 1.36 8.338 3.43 1.907 2.075 2.948 4.83 2.957 8.04-.012 3.126-1.124 5.698-3.107 7.673-1.98 1.959-4.864 3.195-8.188 3.195h-.021c-3.699 0-6.816-.72-8.873-1.732V21.088c2.261-1.605 5.928-2.598 8.894-2.592\"\n});\nexport { _socialIcons_pixiv as default };", "map": {"version": 3, "names": ["register", "_socialIcons_pixiv", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/pixiv.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_pixiv = register(\"pixiv\", {\"color\":\"#0097fa\",\"path\":\"M0 0v64h64V0zm33.553 16.469c-11.844 0-19.903 9.146-19.903 9.146l2.27 3.606s1.26.106.592-2.018c.573-1.086 1.698-2.545 3.892-4.232v24.008c-.946.268-2.194.768-1.34 1.623h6.518c.86-.861-.493-1.38-1.32-1.623v-5.663s4.469 1.756 9.29 1.756c4.234 0 8.088-1.26 10.954-3.537 2.869-2.264 4.712-5.642 4.703-9.506a12.75 12.75 0 0 0-4.41-9.709c-2.793-2.438-6.705-3.847-11.246-3.847zm-.397 2.027c3.601.003 6.425 1.36 8.338 3.43 1.907 2.075 2.948 4.83 2.957 8.04-.012 3.126-1.124 5.698-3.107 7.673-1.98 1.959-4.864 3.195-8.188 3.195h-.021c-3.699 0-6.816-.72-8.873-1.732V21.088c2.261-1.605 5.928-2.598 8.894-2.592\"});\n\nexport { _socialIcons_pixiv as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,kBAAkB,GAAGD,QAAQ,CAAC,OAAO,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAslB,CAAC,CAAC;AAE7pB,SAASC,kBAAkB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}