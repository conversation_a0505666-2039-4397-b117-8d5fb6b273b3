{"ast": null, "code": "import PropTypes from 'prop-types';\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst propTypes = {\n  /** An accessible label indicating the relevant information about the Close Button. */\n  'aria-label': PropTypes.string,\n  /** A callback fired after the Close Button is clicked. */\n  onClick: PropTypes.func,\n  /**\n   * Render different color variant for the button.\n   *\n   * Omitting this will render the default dark color.\n   */\n  variant: PropTypes.oneOf(['white'])\n};\nconst CloseButton = /*#__PURE__*/React.forwardRef(({\n  className,\n  variant,\n  'aria-label': ariaLabel = 'Close',\n  ...props\n}, ref) => /*#__PURE__*/_jsx(\"button\", {\n  ref: ref,\n  type: \"button\",\n  className: classNames('btn-close', variant && `btn-close-${variant}`, className),\n  \"aria-label\": ariaLabel,\n  ...props\n}));\nCloseButton.displayName = 'CloseButton';\nCloseButton.propTypes = propTypes;\nexport default CloseButton;", "map": {"version": 3, "names": ["PropTypes", "React", "classNames", "jsx", "_jsx", "propTypes", "string", "onClick", "func", "variant", "oneOf", "CloseButton", "forwardRef", "className", "aria<PERSON><PERSON><PERSON>", "props", "ref", "type", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/CloseButton.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst propTypes = {\n  /** An accessible label indicating the relevant information about the Close Button. */\n  'aria-label': PropTypes.string,\n  /** A callback fired after the Close Button is clicked. */\n  onClick: PropTypes.func,\n  /**\n   * Render different color variant for the button.\n   *\n   * Omitting this will render the default dark color.\n   */\n  variant: PropTypes.oneOf(['white'])\n};\nconst CloseButton = /*#__PURE__*/React.forwardRef(({\n  className,\n  variant,\n  'aria-label': ariaLabel = 'Close',\n  ...props\n}, ref) => /*#__PURE__*/_jsx(\"button\", {\n  ref: ref,\n  type: \"button\",\n  className: classNames('btn-close', variant && `btn-close-${variant}`, className),\n  \"aria-label\": ariaLabel,\n  ...props\n}));\nCloseButton.displayName = 'CloseButton';\nCloseButton.propTypes = propTypes;\nexport default CloseButton;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG;EAChB;EACA,YAAY,EAAEL,SAAS,CAACM,MAAM;EAC9B;EACAC,OAAO,EAAEP,SAAS,CAACQ,IAAI;EACvB;AACF;AACA;AACA;AACA;EACEC,OAAO,EAAET,SAAS,CAACU,KAAK,CAAC,CAAC,OAAO,CAAC;AACpC,CAAC;AACD,MAAMC,WAAW,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,CAAC;EACjDC,SAAS;EACTJ,OAAO;EACP,YAAY,EAAEK,SAAS,GAAG,OAAO;EACjC,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK,aAAaZ,IAAI,CAAC,QAAQ,EAAE;EACrCY,GAAG,EAAEA,GAAG;EACRC,IAAI,EAAE,QAAQ;EACdJ,SAAS,EAAEX,UAAU,CAAC,WAAW,EAAEO,OAAO,IAAI,aAAaA,OAAO,EAAE,EAAEI,SAAS,CAAC;EAChF,YAAY,EAAEC,SAAS;EACvB,GAAGC;AACL,CAAC,CAAC,CAAC;AACHJ,WAAW,CAACO,WAAW,GAAG,aAAa;AACvCP,WAAW,CAACN,SAAS,GAAGA,SAAS;AACjC,eAAeM,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}