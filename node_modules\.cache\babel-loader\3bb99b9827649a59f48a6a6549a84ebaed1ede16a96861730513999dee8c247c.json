{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { useNavItem } from '@restart/ui/NavItem';\nimport { makeEventKey } from '@restart/ui/SelectableContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavLink = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  as: Component = Anchor,\n  active,\n  eventKey,\n  disabled = false,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'nav-link');\n  const [navItemProps, meta] = useNavItem({\n    key: makeEventKey(eventKey, props.href),\n    active,\n    disabled,\n    ...props\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ...navItemProps,\n    ref: ref,\n    disabled: disabled,\n    className: classNames(className, bsPrefix, disabled && 'disabled', meta.isActive && 'active')\n  });\n});\nNavLink.displayName = 'NavLink';\nexport default NavLink;", "map": {"version": 3, "names": ["classNames", "React", "<PERSON><PERSON>", "useNavItem", "makeEventKey", "useBootstrapPrefix", "jsx", "_jsx", "NavLink", "forwardRef", "bsPrefix", "className", "as", "Component", "active", "eventKey", "disabled", "props", "ref", "navItemProps", "meta", "key", "href", "isActive", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/NavLink.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { useNavItem } from '@restart/ui/NavItem';\nimport { makeEventKey } from '@restart/ui/SelectableContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavLink = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  as: Component = Anchor,\n  active,\n  eventKey,\n  disabled = false,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'nav-link');\n  const [navItemProps, meta] = useNavItem({\n    key: makeEventKey(eventKey, props.href),\n    active,\n    disabled,\n    ...props\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ...navItemProps,\n    ref: ref,\n    disabled: disabled,\n    className: classNames(className, bsPrefix, disabled && 'disabled', meta.isActive && 'active')\n  });\n});\nNavLink.displayName = 'NavLink';\nexport default NavLink;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,OAAO,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EAC7CC,QAAQ;EACRC,SAAS;EACTC,EAAE,EAAEC,SAAS,GAAGX,MAAM;EACtBY,MAAM;EACNC,QAAQ;EACRC,QAAQ,GAAG,KAAK;EAChB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTR,QAAQ,GAAGL,kBAAkB,CAACK,QAAQ,EAAE,UAAU,CAAC;EACnD,MAAM,CAACS,YAAY,EAAEC,IAAI,CAAC,GAAGjB,UAAU,CAAC;IACtCkB,GAAG,EAAEjB,YAAY,CAACW,QAAQ,EAAEE,KAAK,CAACK,IAAI,CAAC;IACvCR,MAAM;IACNE,QAAQ;IACR,GAAGC;EACL,CAAC,CAAC;EACF,OAAO,aAAaV,IAAI,CAACM,SAAS,EAAE;IAClC,GAAGI,KAAK;IACR,GAAGE,YAAY;IACfD,GAAG,EAAEA,GAAG;IACRF,QAAQ,EAAEA,QAAQ;IAClBL,SAAS,EAAEX,UAAU,CAACW,SAAS,EAAED,QAAQ,EAAEM,QAAQ,IAAI,UAAU,EAAEI,IAAI,CAACG,QAAQ,IAAI,QAAQ;EAC9F,CAAC,CAAC;AACJ,CAAC,CAAC;AACFf,OAAO,CAACgB,WAAW,GAAG,SAAS;AAC/B,eAAehB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}