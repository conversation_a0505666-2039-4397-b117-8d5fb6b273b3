{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_leetcode = register(\"leetcode\", {\n  \"color\": \"#E7A41F\",\n  \"path\": \"M0 0h64v64H0zm42.05 42.07a2.12 2.12 0 0 0-3.069-.005l-3.77 3.885a4.65 4.65 0 0 1-6.616.06l-6.758-6.84c-1.824-1.846-2.143-4.74-.56-6.49l6.21-6.625c1.664-1.84 5.04-2.061 6.97-.45l5.508 4.596c.932.779 2.299.628 3.052-.336s.607-2.377-.326-3.156l-5.507-4.596c-1.204-1.005-2.649-1.641-4.161-1.917L36.5 16.5c.85-.91.947-2.264.04-3.116a2.245 2.245 0 0 0-3.181.107l-9.043 9.499-6.063 6.7c-3.252 3.594-2.908 9.19.548 12.688l6.79 6.871a8.887 8.887 0 0 0 12.685-.12l3.77-3.886a2.3 2.3 0 0 0 .005-3.173m-14.396-6.016c0 1.24.971 2.244 2.17 2.244H45.83c1.198 0 2.17-1.005 2.17-2.244s-.972-2.244-2.17-2.244H29.824c-1.199 0-2.17 1.005-2.17 2.244\"\n});\nexport { _socialIcons_leetcode as default };", "map": {"version": 3, "names": ["register", "_socialIcons_leetcode", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/leetcode.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_leetcode = register(\"leetcode\", {\"color\":\"#E7A41F\",\"path\":\"M0 0h64v64H0zm42.05 42.07a2.12 2.12 0 0 0-3.069-.005l-3.77 3.885a4.65 4.65 0 0 1-6.616.06l-6.758-6.84c-1.824-1.846-2.143-4.74-.56-6.49l6.21-6.625c1.664-1.84 5.04-2.061 6.97-.45l5.508 4.596c.932.779 2.299.628 3.052-.336s.607-2.377-.326-3.156l-5.507-4.596c-1.204-1.005-2.649-1.641-4.161-1.917L36.5 16.5c.85-.91.947-2.264.04-3.116a2.245 2.245 0 0 0-3.181.107l-9.043 9.499-6.063 6.7c-3.252 3.594-2.908 9.19.548 12.688l6.79 6.871a8.887 8.887 0 0 0 12.685-.12l3.77-3.886a2.3 2.3 0 0 0 .005-3.173m-14.396-6.016c0 1.24.971 2.244 2.17 2.244H45.83c1.198 0 2.17-1.005 2.17-2.244s-.972-2.244-2.17-2.244H29.824c-1.199 0-2.17 1.005-2.17 2.244\"});\n\nexport { _socialIcons_leetcode as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,qBAAqB,GAAGD,QAAQ,CAAC,UAAU,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAsnB,CAAC,CAAC;AAEnsB,SAASC,qBAAqB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}