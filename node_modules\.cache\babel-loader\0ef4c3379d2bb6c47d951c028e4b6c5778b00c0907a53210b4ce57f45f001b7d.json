{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\Education.js\";\nimport React from 'react';\nimport { Container } from 'react-bootstrap';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EducationSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({\n  theme\n}) => theme.background};\n  padding: 100px 0;\n`;\n_c = EducationSection;\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({\n  theme\n}) => theme.color};\n  \n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({\n  theme\n}) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n_c2 = SectionTitle;\nconst PlaceholderText = styled.p`\n  font-size: 1.2rem;\n  text-align: center;\n  color: ${({\n  theme\n}) => theme.color};\n  opacity: 0.7;\n`;\n_c3 = PlaceholderText;\nconst Education = () => {\n  return /*#__PURE__*/_jsxDEV(EducationSection, {\n    id: \"education\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"Education\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PlaceholderText, {\n        children: \"Education timeline will be implemented here with react-chrono component\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_c4 = Education;\nexport default Education;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"EducationSection\");\n$RefreshReg$(_c2, \"SectionTitle\");\n$RefreshReg$(_c3, \"PlaceholderText\");\n$RefreshReg$(_c4, \"Education\");", "map": {"version": 3, "names": ["React", "Container", "styled", "jsxDEV", "_jsxDEV", "EducationSection", "section", "theme", "background", "_c", "SectionTitle", "h2", "color", "accentColor", "_c2", "PlaceholderText", "p", "_c3", "Education", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/Education.js"], "sourcesContent": ["import React from 'react';\nimport { Container } from 'react-bootstrap';\nimport styled from 'styled-components';\n\nconst EducationSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({ theme }) => theme.background};\n  padding: 100px 0;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({ theme }) => theme.color};\n  \n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({ theme }) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n\nconst PlaceholderText = styled.p`\n  font-size: 1.2rem;\n  text-align: center;\n  color: ${({ theme }) => theme.color};\n  opacity: 0.7;\n`;\n\nconst Education = () => {\n  return (\n    <EducationSection id=\"education\">\n      <Container>\n        <SectionTitle>Education</SectionTitle>\n        <PlaceholderText>\n          Education timeline will be implemented here with react-chrono component\n        </PlaceholderText>\n      </Container>\n    </EducationSection>\n  );\n};\n\nexport default Education;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,gBAAgB,GAAGH,MAAM,CAACI,OAAO;AACvC;AACA;AACA;AACA,gBAAgB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,UAAU;AAC/C;AACA,CAAC;AAACC,EAAA,GANIJ,gBAAgB;AAQtB,MAAMK,YAAY,GAAGR,MAAM,CAACS,EAAE;AAC9B;AACA;AACA;AACA;AACA,WAAW,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,WAAW;AAClD;AACA;AACA,CAAC;AAACC,GAAA,GAfIJ,YAAY;AAiBlB,MAAMK,eAAe,GAAGb,MAAM,CAACc,CAAC;AAChC;AACA;AACA,WAAW,CAAC;EAAET;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA,CAAC;AAACK,GAAA,GALIF,eAAe;AAOrB,MAAMG,SAAS,GAAGA,CAAA,KAAM;EACtB,oBACEd,OAAA,CAACC,gBAAgB;IAACc,EAAE,EAAC,WAAW;IAAAC,QAAA,eAC9BhB,OAAA,CAACH,SAAS;MAAAmB,QAAA,gBACRhB,OAAA,CAACM,YAAY;QAAAU,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACtCpB,OAAA,CAACW,eAAe;QAAAK,QAAA,EAAC;MAEjB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEvB,CAAC;AAACC,GAAA,GAXIP,SAAS;AAaf,eAAeA,SAAS;AAAC,IAAAT,EAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}