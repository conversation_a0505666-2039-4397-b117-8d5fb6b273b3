{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_yandex = register(\"yandex\", {\n  \"color\": \"#fc3f1d\",\n  \"path\": \"M0 0v64h64V0Zm18.656 16h5.91l7.248 15.793c2.124 4.604 3.05 6.998 3.05 12.293V48H29.46v-3.223c0-4.374-.507-6.539-2.262-10.314zm21.008 0h5.68L38.51 31.47h-5.586z\"\n});\nexport { _socialIcons_yandex as default };", "map": {"version": 3, "names": ["register", "_socialIcons_yandex", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/yandex.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_yandex = register(\"yandex\", {\"color\":\"#fc3f1d\",\"path\":\"M0 0v64h64V0Zm18.656 16h5.91l7.248 15.793c2.124 4.604 3.05 6.998 3.05 12.293V48H29.46v-3.223c0-4.374-.507-6.539-2.262-10.314zm21.008 0h5.68L38.51 31.47h-5.586z\"});\n\nexport { _socialIcons_yandex as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,mBAAmB,GAAGD,QAAQ,CAAC,QAAQ,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAiK,CAAC,CAAC;AAE1O,SAASC,mBAAmB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}