{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_linktree = register(\"linktree\", {\n  \"color\": \"#39e09b\",\n  \"path\": \"M0 0v64h64V0zm27.436 19.386c-.6-.948-2.162-.948-2.762 0L14.344 35.83c-.48.843.24 1.792 1.322 1.792h6.966v6.218c0 .633.6 1.16 1.321 1.16h4.083c.721 0 1.321-.527 1.321-1.16v-6.218h-1.921c-.84 0-1.441-.527-1.562-1.16 0-.21 0-.421.12-.635l5.766-9.17zm9.128 0c.6-.948 2.162-.948 2.762 0l10.33 16.444c.48.843-.24 1.792-1.322 1.792h-6.846v6.218c0 .633-.6 1.16-1.322 1.16h-4.323c-.72 0-1.32-.527-1.32-1.16v-6.218h1.921c.84 0 1.441-.527 1.561-1.16 0-.21 0-.421-.12-.635L32.12 26.66z\"\n});\nexport { _socialIcons_linktree as default };", "map": {"version": 3, "names": ["register", "_socialIcons_linktree", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/linktree.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_linktree = register(\"linktree\", {\"color\":\"#39e09b\",\"path\":\"M0 0v64h64V0zm27.436 19.386c-.6-.948-2.162-.948-2.762 0L14.344 35.83c-.48.843.24 1.792 1.322 1.792h6.966v6.218c0 .633.6 1.16 1.321 1.16h4.083c.721 0 1.321-.527 1.321-1.16v-6.218h-1.921c-.84 0-1.441-.527-1.562-1.16 0-.21 0-.421.12-.635l5.766-9.17zm9.128 0c.6-.948 2.162-.948 2.762 0l10.33 16.444c.48.843-.24 1.792-1.322 1.792h-6.846v6.218c0 .633-.6 1.16-1.322 1.16h-4.323c-.72 0-1.32-.527-1.32-1.16v-6.218h1.921c.84 0 1.441-.527 1.561-1.16 0-.21 0-.421-.12-.635L32.12 26.66z\"});\n\nexport { _socialIcons_linktree as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,qBAAqB,GAAGD,QAAQ,CAAC,UAAU,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAA2d,CAAC,CAAC;AAExiB,SAASC,qBAAqB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}