{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\Education.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card } from 'react-bootstrap';\nimport Fade from 'react-reveal/Fade';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EducationSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({\n  theme\n}) => theme.background};\n  padding: 100px 0;\n`;\n_c = EducationSection;\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({\n  theme\n}) => theme.color};\n\n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({\n  theme\n}) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n_c2 = SectionTitle;\nconst EducationCard = styled(Card)`\n  background: ${({\n  theme\n}) => theme.cardBackground};\n  border: 1px solid ${({\n  theme\n}) => theme.cardBorderColor};\n  border-radius: 15px;\n  margin-bottom: 30px;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  height: 100%;\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  }\n`;\n_c3 = EducationCard;\nconst EducationTitle = styled(Card.Title)`\n  color: ${({\n  theme\n}) => theme.accentColor};\n  font-weight: 600;\n  font-size: 1.3rem;\n`;\n_c4 = EducationTitle;\nconst Institution = styled.h6`\n  color: ${({\n  theme\n}) => theme.color};\n  font-weight: 500;\n  margin-bottom: 10px;\n`;\n_c5 = Institution;\nconst Duration = styled.span`\n  color: ${({\n  theme\n}) => theme.accentColor};\n  font-weight: 500;\n  font-size: 0.9rem;\n`;\n_c6 = Duration;\nconst Grade = styled.span`\n  color: ${({\n  theme\n}) => theme.color};\n  font-weight: 600;\n  background: ${({\n  theme\n}) => theme.accentColor}20;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  margin-left: 10px;\n`;\n_c7 = Grade;\nconst Description = styled(Card.Text)`\n  color: ${({\n  theme\n}) => theme.color};\n  opacity: 0.8;\n  line-height: 1.6;\n`;\n_c8 = Description;\nconst Education = () => {\n  _s();\n  const [educationData, setEducationData] = useState(null);\n  useEffect(() => {\n    fetch('/personal-e-portfolio/profile/education.json').then(response => response.json()).then(data => setEducationData(data)).catch(error => console.error('Error loading education data:', error));\n  }, []);\n  if (!educationData) return null;\n  return /*#__PURE__*/_jsxDEV(EducationSection, {\n    id: \"education\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"Education\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: educationData.education.map((edu, index) => /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          md: 6,\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Fade, {\n            bottom: true,\n            delay: index * 200,\n            children: /*#__PURE__*/_jsxDEV(EducationCard, {\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                children: [/*#__PURE__*/_jsxDEV(EducationTitle, {\n                  children: edu.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Institution, {\n                  children: edu.institution\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Duration, {\n                    children: edu.duration\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grade, {\n                    children: edu.grade\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Description, {\n                  children: edu.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(Education, \"sdxDHLqnDs6kKLD+HkWk26quz/s=\");\n_c9 = Education;\nexport default Education;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"EducationSection\");\n$RefreshReg$(_c2, \"SectionTitle\");\n$RefreshReg$(_c3, \"EducationCard\");\n$RefreshReg$(_c4, \"EducationTitle\");\n$RefreshReg$(_c5, \"Institution\");\n$RefreshReg$(_c6, \"Duration\");\n$RefreshReg$(_c7, \"Grade\");\n$RefreshReg$(_c8, \"Description\");\n$RefreshReg$(_c9, \"Education\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Fade", "styled", "jsxDEV", "_jsxDEV", "EducationSection", "section", "theme", "background", "_c", "SectionTitle", "h2", "color", "accentColor", "_c2", "EducationCard", "cardBackground", "cardBorderColor", "_c3", "EducationTitle", "Title", "_c4", "Institution", "h6", "_c5", "Duration", "span", "_c6", "Grade", "_c7", "Description", "Text", "_c8", "Education", "_s", "educationData", "setEducationData", "fetch", "then", "response", "json", "data", "catch", "error", "console", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "education", "map", "edu", "index", "lg", "md", "className", "bottom", "delay", "Body", "title", "institution", "duration", "grade", "description", "_c9", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/Education.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card } from 'react-bootstrap';\nimport Fade from 'react-reveal/Fade';\nimport styled from 'styled-components';\n\nconst EducationSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({ theme }) => theme.background};\n  padding: 100px 0;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({ theme }) => theme.color};\n\n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({ theme }) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n\nconst EducationCard = styled(Card)`\n  background: ${({ theme }) => theme.cardBackground};\n  border: 1px solid ${({ theme }) => theme.cardBorderColor};\n  border-radius: 15px;\n  margin-bottom: 30px;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  height: 100%;\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  }\n`;\n\nconst EducationTitle = styled(Card.Title)`\n  color: ${({ theme }) => theme.accentColor};\n  font-weight: 600;\n  font-size: 1.3rem;\n`;\n\nconst Institution = styled.h6`\n  color: ${({ theme }) => theme.color};\n  font-weight: 500;\n  margin-bottom: 10px;\n`;\n\nconst Duration = styled.span`\n  color: ${({ theme }) => theme.accentColor};\n  font-weight: 500;\n  font-size: 0.9rem;\n`;\n\nconst Grade = styled.span`\n  color: ${({ theme }) => theme.color};\n  font-weight: 600;\n  background: ${({ theme }) => theme.accentColor}20;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  margin-left: 10px;\n`;\n\nconst Description = styled(Card.Text)`\n  color: ${({ theme }) => theme.color};\n  opacity: 0.8;\n  line-height: 1.6;\n`;\n\nconst Education = () => {\n  const [educationData, setEducationData] = useState(null);\n\n  useEffect(() => {\n    fetch('/personal-e-portfolio/profile/education.json')\n      .then(response => response.json())\n      .then(data => setEducationData(data))\n      .catch(error => console.error('Error loading education data:', error));\n  }, []);\n\n  if (!educationData) return null;\n\n  return (\n    <EducationSection id=\"education\">\n      <Container>\n        <SectionTitle>Education</SectionTitle>\n        <Row>\n          {educationData.education.map((edu, index) => (\n            <Col lg={4} md={6} key={index} className=\"mb-4\">\n              <Fade bottom delay={index * 200}>\n                <EducationCard>\n                  <Card.Body>\n                    <EducationTitle>{edu.title}</EducationTitle>\n                    <Institution>{edu.institution}</Institution>\n                    <div className=\"mb-3\">\n                      <Duration>{edu.duration}</Duration>\n                      <Grade>{edu.grade}</Grade>\n                    </div>\n                    <Description>{edu.description}</Description>\n                  </Card.Body>\n                </EducationCard>\n              </Fade>\n            </Col>\n          ))}\n        </Row>\n      </Container>\n    </EducationSection>\n  );\n};\n\nexport default Education;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,QAAQ,iBAAiB;AAC3D,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,gBAAgB,GAAGH,MAAM,CAACI,OAAO;AACvC;AACA;AACA;AACA,gBAAgB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,UAAU;AAC/C;AACA,CAAC;AAACC,EAAA,GANIJ,gBAAgB;AAQtB,MAAMK,YAAY,GAAGR,MAAM,CAACS,EAAE;AAC9B;AACA;AACA;AACA;AACA,WAAW,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,WAAW;AAClD;AACA;AACA,CAAC;AAACC,GAAA,GAfIJ,YAAY;AAiBlB,MAAMK,aAAa,GAAGb,MAAM,CAACF,IAAI,CAAC;AAClC,gBAAgB,CAAC;EAAEO;AAAM,CAAC,KAAKA,KAAK,CAACS,cAAc;AACnD,sBAAsB,CAAC;EAAET;AAAM,CAAC,KAAKA,KAAK,CAACU,eAAe;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIH,aAAa;AAcnB,MAAMI,cAAc,GAAGjB,MAAM,CAACF,IAAI,CAACoB,KAAK,CAAC;AACzC,WAAW,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACM,WAAW;AAC3C;AACA;AACA,CAAC;AAACQ,GAAA,GAJIF,cAAc;AAMpB,MAAMG,WAAW,GAAGpB,MAAM,CAACqB,EAAE;AAC7B,WAAW,CAAC;EAAEhB;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA;AACA,CAAC;AAACY,GAAA,GAJIF,WAAW;AAMjB,MAAMG,QAAQ,GAAGvB,MAAM,CAACwB,IAAI;AAC5B,WAAW,CAAC;EAAEnB;AAAM,CAAC,KAAKA,KAAK,CAACM,WAAW;AAC3C;AACA;AACA,CAAC;AAACc,GAAA,GAJIF,QAAQ;AAMd,MAAMG,KAAK,GAAG1B,MAAM,CAACwB,IAAI;AACzB,WAAW,CAAC;EAAEnB;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA,gBAAgB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,WAAW;AAChD;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GARID,KAAK;AAUX,MAAME,WAAW,GAAG5B,MAAM,CAACF,IAAI,CAAC+B,IAAI,CAAC;AACrC,WAAW,CAAC;EAAExB;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA;AACA,CAAC;AAACoB,GAAA,GAJIF,WAAW;AAMjB,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACdyC,KAAK,CAAC,8CAA8C,CAAC,CAClDC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAIL,gBAAgB,CAACK,IAAI,CAAC,CAAC,CACpCC,KAAK,CAACC,KAAK,IAAIC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC,CAAC;EAC1E,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACR,aAAa,EAAE,OAAO,IAAI;EAE/B,oBACE/B,OAAA,CAACC,gBAAgB;IAACwC,EAAE,EAAC,WAAW;IAAAC,QAAA,eAC9B1C,OAAA,CAACP,SAAS;MAAAiD,QAAA,gBACR1C,OAAA,CAACM,YAAY;QAAAoC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACtC9C,OAAA,CAACN,GAAG;QAAAgD,QAAA,EACDX,aAAa,CAACgB,SAAS,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACtClD,OAAA,CAACL,GAAG;UAACwD,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAaC,SAAS,EAAC,MAAM;UAAAX,QAAA,eAC7C1C,OAAA,CAACH,IAAI;YAACyD,MAAM;YAACC,KAAK,EAAEL,KAAK,GAAG,GAAI;YAAAR,QAAA,eAC9B1C,OAAA,CAACW,aAAa;cAAA+B,QAAA,eACZ1C,OAAA,CAACJ,IAAI,CAAC4D,IAAI;gBAAAd,QAAA,gBACR1C,OAAA,CAACe,cAAc;kBAAA2B,QAAA,EAAEO,GAAG,CAACQ;gBAAK;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CAAC,eAC5C9C,OAAA,CAACkB,WAAW;kBAAAwB,QAAA,EAAEO,GAAG,CAACS;gBAAW;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAAC,eAC5C9C,OAAA;kBAAKqD,SAAS,EAAC,MAAM;kBAAAX,QAAA,gBACnB1C,OAAA,CAACqB,QAAQ;oBAAAqB,QAAA,EAAEO,GAAG,CAACU;kBAAQ;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnC9C,OAAA,CAACwB,KAAK;oBAAAkB,QAAA,EAAEO,GAAG,CAACW;kBAAK;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACN9C,OAAA,CAAC0B,WAAW;kBAAAgB,QAAA,EAAEO,GAAG,CAACY;gBAAW;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC,GAbeI,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcxB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEvB,CAAC;AAAChB,EAAA,CAtCID,SAAS;AAAAiC,GAAA,GAATjC,SAAS;AAwCf,eAAeA,SAAS;AAAC,IAAAxB,EAAA,EAAAK,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAkC,GAAA;AAAAC,YAAA,CAAA1D,EAAA;AAAA0D,YAAA,CAAArD,GAAA;AAAAqD,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}