{"ast": null, "code": "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBindBasic = require('call-bind-apply-helpers');\n\n/** @type {(thisArg: string, searchString: string, position?: number) => number} */\nvar $indexOf = callBindBasic([GetIntrinsic('%String.prototype.indexOf%')]);\n\n/** @type {import('.')} */\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n  /* eslint no-extra-parens: 0 */\n\n  var intrinsic = /** @type {(this: unknown, ...args: unknown[]) => unknown} */GetIntrinsic(name, !!allowMissing);\n  if (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n    return callBindBasic(/** @type {const} */[intrinsic]);\n  }\n  return intrinsic;\n};", "map": {"version": 3, "names": ["GetIntrinsic", "require", "callBindBasic", "$indexOf", "module", "exports", "callBoundIntrinsic", "name", "allowMissing", "intrinsic"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/call-bound/index.js"], "sourcesContent": ["'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar callBindBasic = require('call-bind-apply-helpers');\n\n/** @type {(thisArg: string, searchString: string, position?: number) => number} */\nvar $indexOf = callBindBasic([GetIntrinsic('%String.prototype.indexOf%')]);\n\n/** @type {import('.')} */\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n\t/* eslint no-extra-parens: 0 */\n\n\tvar intrinsic = /** @type {(this: unknown, ...args: unknown[]) => unknown} */ (GetIntrinsic(name, !!allowMissing));\n\tif (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n\t\treturn callBindBasic(/** @type {const} */ ([intrinsic]));\n\t}\n\treturn intrinsic;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE3C,IAAIC,aAAa,GAAGD,OAAO,CAAC,yBAAyB,CAAC;;AAEtD;AACA,IAAIE,QAAQ,GAAGD,aAAa,CAAC,CAACF,YAAY,CAAC,4BAA4B,CAAC,CAAC,CAAC;;AAE1E;AACAI,MAAM,CAACC,OAAO,GAAG,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,YAAY,EAAE;EAChE;;EAEA,IAAIC,SAAS,GAAG,6DAA+DT,YAAY,CAACO,IAAI,EAAE,CAAC,CAACC,YAAY,CAAE;EAClH,IAAI,OAAOC,SAAS,KAAK,UAAU,IAAIN,QAAQ,CAACI,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;IAC1E,OAAOL,aAAa,CAAC,oBAAsB,CAACO,SAAS,CAAE,CAAC;EACzD;EACA,OAAOA,SAAS;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}