{"ast": null, "code": "\"use client\";\n\nimport { useMemo, useRef } from 'react';\nimport hasClass from 'dom-helpers/hasClass';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Popover from './Popover';\nimport Tooltip from './Tooltip';\n\n// This is meant for internal use.\n// This applies a custom offset to the overlay if it's a popover or tooltip.\nexport default function useOverlayOffset(customOffset) {\n  const overlayRef = useRef(null);\n  const popoverClass = useBootstrapPrefix(undefined, 'popover');\n  const tooltipClass = useBootstrapPrefix(undefined, 'tooltip');\n  const offset = useMemo(() => ({\n    name: 'offset',\n    options: {\n      offset: () => {\n        if (customOffset) {\n          return customOffset;\n        }\n        if (overlayRef.current) {\n          if (hasClass(overlayRef.current, popoverClass)) {\n            return Popover.POPPER_OFFSET;\n          }\n          if (hasClass(overlayRef.current, tooltipClass)) {\n            return Tooltip.TOOLTIP_OFFSET;\n          }\n        }\n        return [0, 0];\n      }\n    }\n  }), [customOffset, popoverClass, tooltipClass]);\n  return [overlayRef, [offset]];\n}", "map": {"version": 3, "names": ["useMemo", "useRef", "hasClass", "useBootstrapPrefix", "Popover", "<PERSON><PERSON><PERSON>", "useOverlayOffset", "customOffset", "overlayRef", "popoverClass", "undefined", "tooltipClass", "offset", "name", "options", "current", "POPPER_OFFSET", "TOOLTIP_OFFSET"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/useOverlayOffset.js"], "sourcesContent": ["\"use client\";\n\nimport { useMemo, useRef } from 'react';\nimport hasClass from 'dom-helpers/hasClass';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Popover from './Popover';\nimport Tooltip from './Tooltip';\n\n// This is meant for internal use.\n// This applies a custom offset to the overlay if it's a popover or tooltip.\nexport default function useOverlayOffset(customOffset) {\n  const overlayRef = useRef(null);\n  const popoverClass = useBootstrapPrefix(undefined, 'popover');\n  const tooltipClass = useBootstrapPrefix(undefined, 'tooltip');\n  const offset = useMemo(() => ({\n    name: 'offset',\n    options: {\n      offset: () => {\n        if (customOffset) {\n          return customOffset;\n        }\n        if (overlayRef.current) {\n          if (hasClass(overlayRef.current, popoverClass)) {\n            return Popover.POPPER_OFFSET;\n          }\n          if (hasClass(overlayRef.current, tooltipClass)) {\n            return Tooltip.TOOLTIP_OFFSET;\n          }\n        }\n        return [0, 0];\n      }\n    }\n  }), [customOffset, popoverClass, tooltipClass]);\n  return [overlayRef, [offset]];\n}"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,EAAEC,MAAM,QAAQ,OAAO;AACvC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,YAAY,EAAE;EACrD,MAAMC,UAAU,GAAGP,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMQ,YAAY,GAAGN,kBAAkB,CAACO,SAAS,EAAE,SAAS,CAAC;EAC7D,MAAMC,YAAY,GAAGR,kBAAkB,CAACO,SAAS,EAAE,SAAS,CAAC;EAC7D,MAAME,MAAM,GAAGZ,OAAO,CAAC,OAAO;IAC5Ba,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE;MACPF,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAIL,YAAY,EAAE;UAChB,OAAOA,YAAY;QACrB;QACA,IAAIC,UAAU,CAACO,OAAO,EAAE;UACtB,IAAIb,QAAQ,CAACM,UAAU,CAACO,OAAO,EAAEN,YAAY,CAAC,EAAE;YAC9C,OAAOL,OAAO,CAACY,aAAa;UAC9B;UACA,IAAId,QAAQ,CAACM,UAAU,CAACO,OAAO,EAAEJ,YAAY,CAAC,EAAE;YAC9C,OAAON,OAAO,CAACY,cAAc;UAC/B;QACF;QACA,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;MACf;IACF;EACF,CAAC,CAAC,EAAE,CAACV,YAAY,EAAEE,YAAY,EAAEE,YAAY,CAAC,CAAC;EAC/C,OAAO,CAACH,UAAU,EAAE,CAACI,MAAM,CAAC,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}