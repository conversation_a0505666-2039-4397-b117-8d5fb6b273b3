{"ast": null, "code": "const _excluded = [\"children\", \"usePopper\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport { useContext, useRef } from 'react';\nimport * as React from 'react';\nimport useCallbackRef from '@restart/hooks/useCallbackRef';\nimport DropdownContext from './DropdownContext';\nimport usePopper from './usePopper';\nimport useClickOutside from './useClickOutside';\nimport mergeOptionsWithPopperConfig from './mergeOptionsWithPopperConfig';\nimport { Fragment as _Fragment, jsx as _jsx } from \"react/jsx-runtime\";\nconst noop = () => {};\n\n/**\n * @memberOf Dropdown\n * @param {object}  options\n * @param {boolean} options.flip Automatically adjust the menu `drop` position based on viewport edge detection\n * @param {[number, number]} options.offset Define an offset distance between the Menu and the Toggle\n * @param {boolean} options.show Display the menu manually, ignored in the context of a `Dropdown`\n * @param {boolean} options.usePopper opt in/out of using PopperJS to position menus. When disabled you must position it yourself.\n * @param {string}  options.rootCloseEvent The pointer event to listen for when determining \"clicks outside\" the menu for triggering a close.\n * @param {object}  options.popperConfig Options passed to the [`usePopper`](/api/usePopper) hook.\n */\nexport function useDropdownMenu(options = {}) {\n  const context = useContext(DropdownContext);\n  const [arrowElement, attachArrowRef] = useCallbackRef();\n  const hasShownRef = useRef(false);\n  const {\n    flip,\n    offset,\n    rootCloseEvent,\n    fixed = false,\n    placement: placementOverride,\n    popperConfig = {},\n    enableEventListeners = true,\n    usePopper: shouldUsePopper = !!context\n  } = options;\n  const show = (context == null ? void 0 : context.show) == null ? !!options.show : context.show;\n  if (show && !hasShownRef.current) {\n    hasShownRef.current = true;\n  }\n  const handleClose = e => {\n    context == null ? void 0 : context.toggle(false, e);\n  };\n  const {\n    placement,\n    setMenu,\n    menuElement,\n    toggleElement\n  } = context || {};\n  const popper = usePopper(toggleElement, menuElement, mergeOptionsWithPopperConfig({\n    placement: placementOverride || placement || 'bottom-start',\n    enabled: shouldUsePopper,\n    enableEvents: enableEventListeners == null ? show : enableEventListeners,\n    offset,\n    flip,\n    fixed,\n    arrowElement,\n    popperConfig\n  }));\n  const menuProps = Object.assign({\n    ref: setMenu || noop,\n    'aria-labelledby': toggleElement == null ? void 0 : toggleElement.id\n  }, popper.attributes.popper, {\n    style: popper.styles.popper\n  });\n  const metadata = {\n    show,\n    placement,\n    hasShown: hasShownRef.current,\n    toggle: context == null ? void 0 : context.toggle,\n    popper: shouldUsePopper ? popper : null,\n    arrowProps: shouldUsePopper ? Object.assign({\n      ref: attachArrowRef\n    }, popper.attributes.arrow, {\n      style: popper.styles.arrow\n    }) : {}\n  };\n  useClickOutside(menuElement, handleClose, {\n    clickTrigger: rootCloseEvent,\n    disabled: !show\n  });\n  return [menuProps, metadata];\n}\n/**\n * Also exported as `<Dropdown.Menu>` from `Dropdown`.\n *\n * @displayName DropdownMenu\n * @memberOf Dropdown\n */\nfunction DropdownMenu(_ref) {\n  let {\n      children,\n      usePopper: usePopperProp = true\n    } = _ref,\n    options = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [props, meta] = useDropdownMenu(Object.assign({}, options, {\n    usePopper: usePopperProp\n  }));\n  return /*#__PURE__*/_jsx(_Fragment, {\n    children: children(props, meta)\n  });\n}\nDropdownMenu.displayName = 'DropdownMenu';\n\n/** @component */\nexport default DropdownMenu;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "useContext", "useRef", "React", "useCallbackRef", "DropdownContext", "usePopper", "useClickOutside", "mergeOptionsWithPopperConfig", "Fragment", "_Fragment", "jsx", "_jsx", "noop", "useDropdownMenu", "options", "context", "arrowElement", "attachArrowRef", "hasShownRef", "flip", "offset", "rootCloseEvent", "fixed", "placement", "placementOverride", "popperConfig", "enableEventListeners", "shouldUsePopper", "show", "current", "handleClose", "toggle", "setMenu", "menuElement", "toggleElement", "popper", "enabled", "enableEvents", "menuProps", "Object", "assign", "ref", "id", "attributes", "style", "styles", "metadata", "hasShown", "arrowProps", "arrow", "clickTrigger", "disabled", "DropdownMenu", "_ref", "children", "usePopperProp", "props", "meta", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/@restart/ui/esm/DropdownMenu.js"], "sourcesContent": ["const _excluded = [\"children\", \"usePopper\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nimport { useContext, useRef } from 'react';\nimport * as React from 'react';\nimport useCallbackRef from '@restart/hooks/useCallbackRef';\nimport DropdownContext from './DropdownContext';\nimport usePopper from './usePopper';\nimport useClickOutside from './useClickOutside';\nimport mergeOptionsWithPopperConfig from './mergeOptionsWithPopperConfig';\nimport { Fragment as _Fragment, jsx as _jsx } from \"react/jsx-runtime\";\nconst noop = () => {};\n\n/**\n * @memberOf Dropdown\n * @param {object}  options\n * @param {boolean} options.flip Automatically adjust the menu `drop` position based on viewport edge detection\n * @param {[number, number]} options.offset Define an offset distance between the Menu and the Toggle\n * @param {boolean} options.show Display the menu manually, ignored in the context of a `Dropdown`\n * @param {boolean} options.usePopper opt in/out of using PopperJS to position menus. When disabled you must position it yourself.\n * @param {string}  options.rootCloseEvent The pointer event to listen for when determining \"clicks outside\" the menu for triggering a close.\n * @param {object}  options.popperConfig Options passed to the [`usePopper`](/api/usePopper) hook.\n */\nexport function useDropdownMenu(options = {}) {\n  const context = useContext(DropdownContext);\n  const [arrowElement, attachArrowRef] = useCallbackRef();\n  const hasShownRef = useRef(false);\n  const {\n    flip,\n    offset,\n    rootCloseEvent,\n    fixed = false,\n    placement: placementOverride,\n    popperConfig = {},\n    enableEventListeners = true,\n    usePopper: shouldUsePopper = !!context\n  } = options;\n  const show = (context == null ? void 0 : context.show) == null ? !!options.show : context.show;\n  if (show && !hasShownRef.current) {\n    hasShownRef.current = true;\n  }\n  const handleClose = e => {\n    context == null ? void 0 : context.toggle(false, e);\n  };\n  const {\n    placement,\n    setMenu,\n    menuElement,\n    toggleElement\n  } = context || {};\n  const popper = usePopper(toggleElement, menuElement, mergeOptionsWithPopperConfig({\n    placement: placementOverride || placement || 'bottom-start',\n    enabled: shouldUsePopper,\n    enableEvents: enableEventListeners == null ? show : enableEventListeners,\n    offset,\n    flip,\n    fixed,\n    arrowElement,\n    popperConfig\n  }));\n  const menuProps = Object.assign({\n    ref: setMenu || noop,\n    'aria-labelledby': toggleElement == null ? void 0 : toggleElement.id\n  }, popper.attributes.popper, {\n    style: popper.styles.popper\n  });\n  const metadata = {\n    show,\n    placement,\n    hasShown: hasShownRef.current,\n    toggle: context == null ? void 0 : context.toggle,\n    popper: shouldUsePopper ? popper : null,\n    arrowProps: shouldUsePopper ? Object.assign({\n      ref: attachArrowRef\n    }, popper.attributes.arrow, {\n      style: popper.styles.arrow\n    }) : {}\n  };\n  useClickOutside(menuElement, handleClose, {\n    clickTrigger: rootCloseEvent,\n    disabled: !show\n  });\n  return [menuProps, metadata];\n}\n/**\n * Also exported as `<Dropdown.Menu>` from `Dropdown`.\n *\n * @displayName DropdownMenu\n * @memberOf Dropdown\n */\nfunction DropdownMenu(_ref) {\n  let {\n      children,\n      usePopper: usePopperProp = true\n    } = _ref,\n    options = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [props, meta] = useDropdownMenu(Object.assign({}, options, {\n    usePopper: usePopperProp\n  }));\n  return /*#__PURE__*/_jsx(_Fragment, {\n    children: children(props, meta)\n  });\n}\nDropdownMenu.displayName = 'DropdownMenu';\n\n/** @component */\nexport default DropdownMenu;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC;AAC3C,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IAAE,IAAIF,CAAC,CAACK,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,EAAE;IAAUD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACpM,SAASK,UAAU,EAAEC,MAAM,QAAQ,OAAO;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,4BAA4B,MAAM,gCAAgC;AACzE,SAASC,QAAQ,IAAIC,SAAS,EAAEC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AACtE,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC5C,MAAMC,OAAO,GAAGf,UAAU,CAACI,eAAe,CAAC;EAC3C,MAAM,CAACY,YAAY,EAAEC,cAAc,CAAC,GAAGd,cAAc,CAAC,CAAC;EACvD,MAAMe,WAAW,GAAGjB,MAAM,CAAC,KAAK,CAAC;EACjC,MAAM;IACJkB,IAAI;IACJC,MAAM;IACNC,cAAc;IACdC,KAAK,GAAG,KAAK;IACbC,SAAS,EAAEC,iBAAiB;IAC5BC,YAAY,GAAG,CAAC,CAAC;IACjBC,oBAAoB,GAAG,IAAI;IAC3BrB,SAAS,EAAEsB,eAAe,GAAG,CAAC,CAACZ;EACjC,CAAC,GAAGD,OAAO;EACX,MAAMc,IAAI,GAAG,CAACb,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACa,IAAI,KAAK,IAAI,GAAG,CAAC,CAACd,OAAO,CAACc,IAAI,GAAGb,OAAO,CAACa,IAAI;EAC9F,IAAIA,IAAI,IAAI,CAACV,WAAW,CAACW,OAAO,EAAE;IAChCX,WAAW,CAACW,OAAO,GAAG,IAAI;EAC5B;EACA,MAAMC,WAAW,GAAGpC,CAAC,IAAI;IACvBqB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgB,MAAM,CAAC,KAAK,EAAErC,CAAC,CAAC;EACrD,CAAC;EACD,MAAM;IACJ6B,SAAS;IACTS,OAAO;IACPC,WAAW;IACXC;EACF,CAAC,GAAGnB,OAAO,IAAI,CAAC,CAAC;EACjB,MAAMoB,MAAM,GAAG9B,SAAS,CAAC6B,aAAa,EAAED,WAAW,EAAE1B,4BAA4B,CAAC;IAChFgB,SAAS,EAAEC,iBAAiB,IAAID,SAAS,IAAI,cAAc;IAC3Da,OAAO,EAAET,eAAe;IACxBU,YAAY,EAAEX,oBAAoB,IAAI,IAAI,GAAGE,IAAI,GAAGF,oBAAoB;IACxEN,MAAM;IACND,IAAI;IACJG,KAAK;IACLN,YAAY;IACZS;EACF,CAAC,CAAC,CAAC;EACH,MAAMa,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC;IAC9BC,GAAG,EAAET,OAAO,IAAIpB,IAAI;IACpB,iBAAiB,EAAEsB,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACQ;EACpE,CAAC,EAAEP,MAAM,CAACQ,UAAU,CAACR,MAAM,EAAE;IAC3BS,KAAK,EAAET,MAAM,CAACU,MAAM,CAACV;EACvB,CAAC,CAAC;EACF,MAAMW,QAAQ,GAAG;IACflB,IAAI;IACJL,SAAS;IACTwB,QAAQ,EAAE7B,WAAW,CAACW,OAAO;IAC7BE,MAAM,EAAEhB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgB,MAAM;IACjDI,MAAM,EAAER,eAAe,GAAGQ,MAAM,GAAG,IAAI;IACvCa,UAAU,EAAErB,eAAe,GAAGY,MAAM,CAACC,MAAM,CAAC;MAC1CC,GAAG,EAAExB;IACP,CAAC,EAAEkB,MAAM,CAACQ,UAAU,CAACM,KAAK,EAAE;MAC1BL,KAAK,EAAET,MAAM,CAACU,MAAM,CAACI;IACvB,CAAC,CAAC,GAAG,CAAC;EACR,CAAC;EACD3C,eAAe,CAAC2B,WAAW,EAAEH,WAAW,EAAE;IACxCoB,YAAY,EAAE7B,cAAc;IAC5B8B,QAAQ,EAAE,CAACvB;EACb,CAAC,CAAC;EACF,OAAO,CAACU,SAAS,EAAEQ,QAAQ,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,YAAYA,CAACC,IAAI,EAAE;EAC1B,IAAI;MACAC,QAAQ;MACRjD,SAAS,EAAEkD,aAAa,GAAG;IAC7B,CAAC,GAAGF,IAAI;IACRvC,OAAO,GAAGtB,6BAA6B,CAAC6D,IAAI,EAAE9D,SAAS,CAAC;EAC1D,MAAM,CAACiE,KAAK,EAAEC,IAAI,CAAC,GAAG5C,eAAe,CAAC0B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1B,OAAO,EAAE;IAC/DT,SAAS,EAAEkD;EACb,CAAC,CAAC,CAAC;EACH,OAAO,aAAa5C,IAAI,CAACF,SAAS,EAAE;IAClC6C,QAAQ,EAAEA,QAAQ,CAACE,KAAK,EAAEC,IAAI;EAChC,CAAC,CAAC;AACJ;AACAL,YAAY,CAACM,WAAW,GAAG,cAAc;;AAEzC;AACA,eAAeN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}