{"ast": null, "code": "/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n// This alternative WebpackDevServer combines the functionality of:\n// https://github.com/webpack/webpack-dev-server/blob/webpack-1/client/index.js\n// https://github.com/webpack/webpack/blob/webpack-1/hot/dev-server.js\n\n// It only supports their simplest configuration (hot updates on same server).\n// It makes some opinionated choices on top, like adding a syntax error overlay\n// that looks similar to our console output. The error overlay is inspired by:\n// https://github.com/glenjamin/webpack-hot-middleware\nvar stripAnsi = require('strip-ansi');\nvar url = require('url');\nvar launchEditorEndpoint = require('./launchEditorEndpoint');\nvar formatWebpackMessages = require('./formatWebpackMessages');\nvar ErrorOverlay = require('react-error-overlay');\nErrorOverlay.setEditorHandler(function editorHandler(errorLocation) {\n  // Keep this sync with errorOverlayMiddleware.js\n  fetch(launchEditorEndpoint + '?fileName=' + window.encodeURIComponent(errorLocation.fileName) + '&lineNumber=' + window.encodeURIComponent(errorLocation.lineNumber || 1) + '&colNumber=' + window.encodeURIComponent(errorLocation.colNumber || 1));\n});\n\n// We need to keep track of if there has been a runtime error.\n// Essentially, we cannot guarantee application state was not corrupted by the\n// runtime error. To prevent confusing behavior, we forcibly reload the entire\n// application. This is handled below when we are notified of a compile (code\n// change).\n// See https://github.com/facebook/create-react-app/issues/3096\nvar hadRuntimeError = false;\nErrorOverlay.startReportingRuntimeErrors({\n  onError: function () {\n    hadRuntimeError = true;\n  },\n  filename: '/static/js/bundle.js'\n});\nif (module.hot && typeof module.hot.dispose === 'function') {\n  module.hot.dispose(function () {\n    // TODO: why do we need this?\n    ErrorOverlay.stopReportingRuntimeErrors();\n  });\n}\n\n// Connect to WebpackDevServer via a socket.\nvar connection = new WebSocket(url.format({\n  protocol: window.location.protocol === 'https:' ? 'wss' : 'ws',\n  hostname: process.env.WDS_SOCKET_HOST || window.location.hostname,\n  port: process.env.WDS_SOCKET_PORT || window.location.port,\n  // Hardcoded in WebpackDevServer\n  pathname: process.env.WDS_SOCKET_PATH || '/sockjs-node',\n  slashes: true\n}));\n\n// Unlike WebpackDevServer client, we won't try to reconnect\n// to avoid spamming the console. Disconnect usually happens\n// when developer stops the server.\nconnection.onclose = function () {\n  if (typeof console !== 'undefined' && typeof console.info === 'function') {\n    console.info('The development server has disconnected.\\nRefresh the page if necessary.');\n  }\n};\n\n// Remember some state related to hot module replacement.\nvar isFirstCompilation = true;\nvar mostRecentCompilationHash = null;\nvar hasCompileErrors = false;\nfunction clearOutdatedErrors() {\n  // Clean up outdated compile errors, if any.\n  if (typeof console !== 'undefined' && typeof console.clear === 'function') {\n    if (hasCompileErrors) {\n      console.clear();\n    }\n  }\n}\n\n// Successful compilation.\nfunction handleSuccess() {\n  clearOutdatedErrors();\n  var isHotUpdate = !isFirstCompilation;\n  isFirstCompilation = false;\n  hasCompileErrors = false;\n\n  // Attempt to apply hot updates or reload.\n  if (isHotUpdate) {\n    tryApplyUpdates(function onHotUpdateSuccess() {\n      // Only dismiss it when we're sure it's a hot update.\n      // Otherwise it would flicker right before the reload.\n      tryDismissErrorOverlay();\n    });\n  }\n}\n\n// Compilation with warnings (e.g. ESLint).\nfunction handleWarnings(warnings) {\n  clearOutdatedErrors();\n  var isHotUpdate = !isFirstCompilation;\n  isFirstCompilation = false;\n  hasCompileErrors = false;\n  function printWarnings() {\n    // Print warnings to the console.\n    var formatted = formatWebpackMessages({\n      warnings: warnings,\n      errors: []\n    });\n    if (typeof console !== 'undefined' && typeof console.warn === 'function') {\n      for (var i = 0; i < formatted.warnings.length; i++) {\n        if (i === 5) {\n          console.warn('There were more warnings in other files.\\n' + 'You can find a complete log in the terminal.');\n          break;\n        }\n        console.warn(stripAnsi(formatted.warnings[i]));\n      }\n    }\n  }\n  printWarnings();\n\n  // Attempt to apply hot updates or reload.\n  if (isHotUpdate) {\n    tryApplyUpdates(function onSuccessfulHotUpdate() {\n      // Only dismiss it when we're sure it's a hot update.\n      // Otherwise it would flicker right before the reload.\n      tryDismissErrorOverlay();\n    });\n  }\n}\n\n// Compilation with errors (e.g. syntax error or missing modules).\nfunction handleErrors(errors) {\n  clearOutdatedErrors();\n  isFirstCompilation = false;\n  hasCompileErrors = true;\n\n  // \"Massage\" webpack messages.\n  var formatted = formatWebpackMessages({\n    errors: errors,\n    warnings: []\n  });\n\n  // Only show the first error.\n  ErrorOverlay.reportBuildError(formatted.errors[0]);\n\n  // Also log them to the console.\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    for (var i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]));\n    }\n  }\n\n  // Do not attempt to reload now.\n  // We will reload on next success instead.\n}\nfunction tryDismissErrorOverlay() {\n  if (!hasCompileErrors) {\n    ErrorOverlay.dismissBuildError();\n  }\n}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash;\n}\n\n// Handle messages from the server.\nconnection.onmessage = function (e) {\n  var message = JSON.parse(e.data);\n  switch (message.type) {\n    case 'hash':\n      handleAvailableHash(message.data);\n      break;\n    case 'still-ok':\n    case 'ok':\n      handleSuccess();\n      break;\n    case 'content-changed':\n      // Triggered when a file from `contentBase` changed.\n      window.location.reload();\n      break;\n    case 'warnings':\n      handleWarnings(message.data);\n      break;\n    case 'errors':\n      handleErrors(message.data);\n      break;\n    default:\n    // Do nothing.\n  }\n};\n\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by webpack.\n  return mostRecentCompilationHash !== __webpack_hash__;\n}\n\n// webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  return module.hot.status() === 'idle';\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(onHotUpdateSuccess) {\n  if (!module.hot) {\n    // HotModuleReplacementPlugin is not in webpack configuration.\n    window.location.reload();\n    return;\n  }\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    return;\n  }\n  function handleApplyUpdates(err, updatedModules) {\n    // NOTE: This var is injected by Webpack's DefinePlugin, and is a boolean instead of string.\n    const hasReactRefresh = process.env.FAST_REFRESH;\n    const wantsForcedReload = err || !updatedModules || hadRuntimeError;\n    // React refresh can handle hot-reloading over errors.\n    if (!hasReactRefresh && wantsForcedReload) {\n      window.location.reload();\n      return;\n    }\n    if (typeof onHotUpdateSuccess === 'function') {\n      // Maybe we want to do something.\n      onHotUpdateSuccess();\n    }\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdates();\n    }\n  }\n\n  // https://webpack.github.io/docs/hot-module-replacement.html#check\n  var result = module.hot.check(/* autoApply */true, handleApplyUpdates);\n\n  // // webpack 2 returns a Promise instead of invoking a callback\n  if (result && result.then) {\n    result.then(function (updatedModules) {\n      handleApplyUpdates(null, updatedModules);\n    }, function (err) {\n      handleApplyUpdates(err, null);\n    });\n  }\n}", "map": {"version": 3, "names": ["stripAnsi", "require", "url", "launchEditorEndpoint", "formatWebpackMessages", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setEditorHandler", "<PERSON><PERSON><PERSON><PERSON>", "errorLocation", "fetch", "window", "encodeURIComponent", "fileName", "lineNumber", "colNumber", "hadRuntimeError", "startReportingRuntimeErrors", "onError", "filename", "module", "hot", "dispose", "stopReportingRuntimeErrors", "connection", "WebSocket", "format", "protocol", "location", "hostname", "process", "env", "WDS_SOCKET_HOST", "port", "WDS_SOCKET_PORT", "pathname", "WDS_SOCKET_PATH", "slashes", "onclose", "console", "info", "isFirstCompilation", "mostRecentCompilationHash", "hasCompileErrors", "clearOutdatedErrors", "clear", "handleSuccess", "isHotUpdate", "tryApplyUpdates", "onHotUpdateSuccess", "tryDismissError<PERSON><PERSON>lay", "handleWarnings", "warnings", "printWarnings", "formatted", "errors", "warn", "i", "length", "onSuccessfulHotUpdate", "handleErrors", "reportBuildError", "error", "dismissBuildError", "handleAvailableHash", "hash", "onmessage", "e", "message", "JSON", "parse", "data", "type", "reload", "isUpdateAvailable", "__webpack_hash__", "canApplyUpdates", "status", "handleApplyUpdates", "err", "updatedModules", "hasReactRefresh", "FAST_REFRESH", "wantsForcedReload", "result", "check", "then"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-dev-utils/webpackHotDevClient.js"], "sourcesContent": ["/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n// This alternative WebpackDevServer combines the functionality of:\n// https://github.com/webpack/webpack-dev-server/blob/webpack-1/client/index.js\n// https://github.com/webpack/webpack/blob/webpack-1/hot/dev-server.js\n\n// It only supports their simplest configuration (hot updates on same server).\n// It makes some opinionated choices on top, like adding a syntax error overlay\n// that looks similar to our console output. The error overlay is inspired by:\n// https://github.com/glenjamin/webpack-hot-middleware\n\nvar stripAnsi = require('strip-ansi');\nvar url = require('url');\nvar launchEditorEndpoint = require('./launchEditorEndpoint');\nvar formatWebpackMessages = require('./formatWebpackMessages');\nvar ErrorOverlay = require('react-error-overlay');\n\nErrorOverlay.setEditorHandler(function editorHandler(errorLocation) {\n  // Keep this sync with errorOverlayMiddleware.js\n  fetch(\n    launchEditorEndpoint +\n      '?fileName=' +\n      window.encodeURIComponent(errorLocation.fileName) +\n      '&lineNumber=' +\n      window.encodeURIComponent(errorLocation.lineNumber || 1) +\n      '&colNumber=' +\n      window.encodeURIComponent(errorLocation.colNumber || 1)\n  );\n});\n\n// We need to keep track of if there has been a runtime error.\n// Essentially, we cannot guarantee application state was not corrupted by the\n// runtime error. To prevent confusing behavior, we forcibly reload the entire\n// application. This is handled below when we are notified of a compile (code\n// change).\n// See https://github.com/facebook/create-react-app/issues/3096\nvar hadRuntimeError = false;\nErrorOverlay.startReportingRuntimeErrors({\n  onError: function () {\n    hadRuntimeError = true;\n  },\n  filename: '/static/js/bundle.js',\n});\n\nif (module.hot && typeof module.hot.dispose === 'function') {\n  module.hot.dispose(function () {\n    // TODO: why do we need this?\n    ErrorOverlay.stopReportingRuntimeErrors();\n  });\n}\n\n// Connect to WebpackDevServer via a socket.\nvar connection = new WebSocket(\n  url.format({\n    protocol: window.location.protocol === 'https:' ? 'wss' : 'ws',\n    hostname: process.env.WDS_SOCKET_HOST || window.location.hostname,\n    port: process.env.WDS_SOCKET_PORT || window.location.port,\n    // Hardcoded in WebpackDevServer\n    pathname: process.env.WDS_SOCKET_PATH || '/sockjs-node',\n    slashes: true,\n  })\n);\n\n// Unlike WebpackDevServer client, we won't try to reconnect\n// to avoid spamming the console. Disconnect usually happens\n// when developer stops the server.\nconnection.onclose = function () {\n  if (typeof console !== 'undefined' && typeof console.info === 'function') {\n    console.info(\n      'The development server has disconnected.\\nRefresh the page if necessary.'\n    );\n  }\n};\n\n// Remember some state related to hot module replacement.\nvar isFirstCompilation = true;\nvar mostRecentCompilationHash = null;\nvar hasCompileErrors = false;\n\nfunction clearOutdatedErrors() {\n  // Clean up outdated compile errors, if any.\n  if (typeof console !== 'undefined' && typeof console.clear === 'function') {\n    if (hasCompileErrors) {\n      console.clear();\n    }\n  }\n}\n\n// Successful compilation.\nfunction handleSuccess() {\n  clearOutdatedErrors();\n\n  var isHotUpdate = !isFirstCompilation;\n  isFirstCompilation = false;\n  hasCompileErrors = false;\n\n  // Attempt to apply hot updates or reload.\n  if (isHotUpdate) {\n    tryApplyUpdates(function onHotUpdateSuccess() {\n      // Only dismiss it when we're sure it's a hot update.\n      // Otherwise it would flicker right before the reload.\n      tryDismissErrorOverlay();\n    });\n  }\n}\n\n// Compilation with warnings (e.g. ESLint).\nfunction handleWarnings(warnings) {\n  clearOutdatedErrors();\n\n  var isHotUpdate = !isFirstCompilation;\n  isFirstCompilation = false;\n  hasCompileErrors = false;\n\n  function printWarnings() {\n    // Print warnings to the console.\n    var formatted = formatWebpackMessages({\n      warnings: warnings,\n      errors: [],\n    });\n\n    if (typeof console !== 'undefined' && typeof console.warn === 'function') {\n      for (var i = 0; i < formatted.warnings.length; i++) {\n        if (i === 5) {\n          console.warn(\n            'There were more warnings in other files.\\n' +\n              'You can find a complete log in the terminal.'\n          );\n          break;\n        }\n        console.warn(stripAnsi(formatted.warnings[i]));\n      }\n    }\n  }\n\n  printWarnings();\n\n  // Attempt to apply hot updates or reload.\n  if (isHotUpdate) {\n    tryApplyUpdates(function onSuccessfulHotUpdate() {\n      // Only dismiss it when we're sure it's a hot update.\n      // Otherwise it would flicker right before the reload.\n      tryDismissErrorOverlay();\n    });\n  }\n}\n\n// Compilation with errors (e.g. syntax error or missing modules).\nfunction handleErrors(errors) {\n  clearOutdatedErrors();\n\n  isFirstCompilation = false;\n  hasCompileErrors = true;\n\n  // \"Massage\" webpack messages.\n  var formatted = formatWebpackMessages({\n    errors: errors,\n    warnings: [],\n  });\n\n  // Only show the first error.\n  ErrorOverlay.reportBuildError(formatted.errors[0]);\n\n  // Also log them to the console.\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    for (var i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]));\n    }\n  }\n\n  // Do not attempt to reload now.\n  // We will reload on next success instead.\n}\n\nfunction tryDismissErrorOverlay() {\n  if (!hasCompileErrors) {\n    ErrorOverlay.dismissBuildError();\n  }\n}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash;\n}\n\n// Handle messages from the server.\nconnection.onmessage = function (e) {\n  var message = JSON.parse(e.data);\n  switch (message.type) {\n    case 'hash':\n      handleAvailableHash(message.data);\n      break;\n    case 'still-ok':\n    case 'ok':\n      handleSuccess();\n      break;\n    case 'content-changed':\n      // Triggered when a file from `contentBase` changed.\n      window.location.reload();\n      break;\n    case 'warnings':\n      handleWarnings(message.data);\n      break;\n    case 'errors':\n      handleErrors(message.data);\n      break;\n    default:\n    // Do nothing.\n  }\n};\n\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by webpack.\n  return mostRecentCompilationHash !== __webpack_hash__;\n}\n\n// webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  return module.hot.status() === 'idle';\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(onHotUpdateSuccess) {\n  if (!module.hot) {\n    // HotModuleReplacementPlugin is not in webpack configuration.\n    window.location.reload();\n    return;\n  }\n\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    return;\n  }\n\n  function handleApplyUpdates(err, updatedModules) {\n    // NOTE: This var is injected by Webpack's DefinePlugin, and is a boolean instead of string.\n    const hasReactRefresh = process.env.FAST_REFRESH;\n    const wantsForcedReload = err || !updatedModules || hadRuntimeError;\n    // React refresh can handle hot-reloading over errors.\n    if (!hasReactRefresh && wantsForcedReload) {\n      window.location.reload();\n      return;\n    }\n\n    if (typeof onHotUpdateSuccess === 'function') {\n      // Maybe we want to do something.\n      onHotUpdateSuccess();\n    }\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdates();\n    }\n  }\n\n  // https://webpack.github.io/docs/hot-module-replacement.html#check\n  var result = module.hot.check(/* autoApply */ true, handleApplyUpdates);\n\n  // // webpack 2 returns a Promise instead of invoking a callback\n  if (result && result.then) {\n    result.then(\n      function (updatedModules) {\n        handleApplyUpdates(null, updatedModules);\n      },\n      function (err) {\n        handleApplyUpdates(err, null);\n      }\n    );\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;;AAEA;AACA;AACA;AACA;AAEA,IAAIA,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIC,GAAG,GAAGD,OAAO,CAAC,KAAK,CAAC;AACxB,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAC5D,IAAIG,qBAAqB,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AAC9D,IAAII,YAAY,GAAGJ,OAAO,CAAC,qBAAqB,CAAC;AAEjDI,YAAY,CAACC,gBAAgB,CAAC,SAASC,aAAaA,CAACC,aAAa,EAAE;EAClE;EACAC,KAAK,CACHN,oBAAoB,GAClB,YAAY,GACZO,MAAM,CAACC,kBAAkB,CAACH,aAAa,CAACI,QAAQ,CAAC,GACjD,cAAc,GACdF,MAAM,CAACC,kBAAkB,CAACH,aAAa,CAACK,UAAU,IAAI,CAAC,CAAC,GACxD,aAAa,GACbH,MAAM,CAACC,kBAAkB,CAACH,aAAa,CAACM,SAAS,IAAI,CAAC,CAC1D,CAAC;AACH,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,KAAK;AAC3BV,YAAY,CAACW,2BAA2B,CAAC;EACvCC,OAAO,EAAE,SAAAA,CAAA,EAAY;IACnBF,eAAe,GAAG,IAAI;EACxB,CAAC;EACDG,QAAQ,EAAE;AACZ,CAAC,CAAC;AAEF,IAAIC,MAAM,CAACC,GAAG,IAAI,OAAOD,MAAM,CAACC,GAAG,CAACC,OAAO,KAAK,UAAU,EAAE;EAC1DF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,YAAY;IAC7B;IACAhB,YAAY,CAACiB,0BAA0B,CAAC,CAAC;EAC3C,CAAC,CAAC;AACJ;;AAEA;AACA,IAAIC,UAAU,GAAG,IAAIC,SAAS,CAC5BtB,GAAG,CAACuB,MAAM,CAAC;EACTC,QAAQ,EAAEhB,MAAM,CAACiB,QAAQ,CAACD,QAAQ,KAAK,QAAQ,GAAG,KAAK,GAAG,IAAI;EAC9DE,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAIrB,MAAM,CAACiB,QAAQ,CAACC,QAAQ;EACjEI,IAAI,EAAEH,OAAO,CAACC,GAAG,CAACG,eAAe,IAAIvB,MAAM,CAACiB,QAAQ,CAACK,IAAI;EACzD;EACAE,QAAQ,EAAEL,OAAO,CAACC,GAAG,CAACK,eAAe,IAAI,cAAc;EACvDC,OAAO,EAAE;AACX,CAAC,CACH,CAAC;;AAED;AACA;AACA;AACAb,UAAU,CAACc,OAAO,GAAG,YAAY;EAC/B,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACC,IAAI,KAAK,UAAU,EAAE;IACxED,OAAO,CAACC,IAAI,CACV,0EACF,CAAC;EACH;AACF,CAAC;;AAED;AACA,IAAIC,kBAAkB,GAAG,IAAI;AAC7B,IAAIC,yBAAyB,GAAG,IAAI;AACpC,IAAIC,gBAAgB,GAAG,KAAK;AAE5B,SAASC,mBAAmBA,CAAA,EAAG;EAC7B;EACA,IAAI,OAAOL,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACM,KAAK,KAAK,UAAU,EAAE;IACzE,IAAIF,gBAAgB,EAAE;MACpBJ,OAAO,CAACM,KAAK,CAAC,CAAC;IACjB;EACF;AACF;;AAEA;AACA,SAASC,aAAaA,CAAA,EAAG;EACvBF,mBAAmB,CAAC,CAAC;EAErB,IAAIG,WAAW,GAAG,CAACN,kBAAkB;EACrCA,kBAAkB,GAAG,KAAK;EAC1BE,gBAAgB,GAAG,KAAK;;EAExB;EACA,IAAII,WAAW,EAAE;IACfC,eAAe,CAAC,SAASC,kBAAkBA,CAAA,EAAG;MAC5C;MACA;MACAC,sBAAsB,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAChCR,mBAAmB,CAAC,CAAC;EAErB,IAAIG,WAAW,GAAG,CAACN,kBAAkB;EACrCA,kBAAkB,GAAG,KAAK;EAC1BE,gBAAgB,GAAG,KAAK;EAExB,SAASU,aAAaA,CAAA,EAAG;IACvB;IACA,IAAIC,SAAS,GAAGjD,qBAAqB,CAAC;MACpC+C,QAAQ,EAAEA,QAAQ;MAClBG,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,IAAI,OAAOhB,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACiB,IAAI,KAAK,UAAU,EAAE;MACxE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACF,QAAQ,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;QAClD,IAAIA,CAAC,KAAK,CAAC,EAAE;UACXlB,OAAO,CAACiB,IAAI,CACV,4CAA4C,GAC1C,8CACJ,CAAC;UACD;QACF;QACAjB,OAAO,CAACiB,IAAI,CAACvD,SAAS,CAACqD,SAAS,CAACF,QAAQ,CAACK,CAAC,CAAC,CAAC,CAAC;MAChD;IACF;EACF;EAEAJ,aAAa,CAAC,CAAC;;EAEf;EACA,IAAIN,WAAW,EAAE;IACfC,eAAe,CAAC,SAASW,qBAAqBA,CAAA,EAAG;MAC/C;MACA;MACAT,sBAAsB,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAASU,YAAYA,CAACL,MAAM,EAAE;EAC5BX,mBAAmB,CAAC,CAAC;EAErBH,kBAAkB,GAAG,KAAK;EAC1BE,gBAAgB,GAAG,IAAI;;EAEvB;EACA,IAAIW,SAAS,GAAGjD,qBAAqB,CAAC;IACpCkD,MAAM,EAAEA,MAAM;IACdH,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA9C,YAAY,CAACuD,gBAAgB,CAACP,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;;EAElD;EACA,IAAI,OAAOhB,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACuB,KAAK,KAAK,UAAU,EAAE;IACzE,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAChDlB,OAAO,CAACuB,KAAK,CAAC7D,SAAS,CAACqD,SAAS,CAACC,MAAM,CAACE,CAAC,CAAC,CAAC,CAAC;IAC/C;EACF;;EAEA;EACA;AACF;AAEA,SAASP,sBAAsBA,CAAA,EAAG;EAChC,IAAI,CAACP,gBAAgB,EAAE;IACrBrC,YAAY,CAACyD,iBAAiB,CAAC,CAAC;EAClC;AACF;;AAEA;AACA,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACjC;EACAvB,yBAAyB,GAAGuB,IAAI;AAClC;;AAEA;AACAzC,UAAU,CAAC0C,SAAS,GAAG,UAAUC,CAAC,EAAE;EAClC,IAAIC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,CAACI,IAAI,CAAC;EAChC,QAAQH,OAAO,CAACI,IAAI;IAClB,KAAK,MAAM;MACTR,mBAAmB,CAACI,OAAO,CAACG,IAAI,CAAC;MACjC;IACF,KAAK,UAAU;IACf,KAAK,IAAI;MACPzB,aAAa,CAAC,CAAC;MACf;IACF,KAAK,iBAAiB;MACpB;MACAnC,MAAM,CAACiB,QAAQ,CAAC6C,MAAM,CAAC,CAAC;MACxB;IACF,KAAK,UAAU;MACbtB,cAAc,CAACiB,OAAO,CAACG,IAAI,CAAC;MAC5B;IACF,KAAK,QAAQ;MACXX,YAAY,CAACQ,OAAO,CAACG,IAAI,CAAC;MAC1B;IACF;IACA;EACF;AACF,CAAC;;AAED;AACA,SAASG,iBAAiBA,CAAA,EAAG;EAC3B;EACA;EACA;EACA,OAAOhC,yBAAyB,KAAKiC,gBAAgB;AACvD;;AAEA;AACA,SAASC,eAAeA,CAAA,EAAG;EACzB,OAAOxD,MAAM,CAACC,GAAG,CAACwD,MAAM,CAAC,CAAC,KAAK,MAAM;AACvC;;AAEA;AACA,SAAS7B,eAAeA,CAACC,kBAAkB,EAAE;EAC3C,IAAI,CAAC7B,MAAM,CAACC,GAAG,EAAE;IACf;IACAV,MAAM,CAACiB,QAAQ,CAAC6C,MAAM,CAAC,CAAC;IACxB;EACF;EAEA,IAAI,CAACC,iBAAiB,CAAC,CAAC,IAAI,CAACE,eAAe,CAAC,CAAC,EAAE;IAC9C;EACF;EAEA,SAASE,kBAAkBA,CAACC,GAAG,EAAEC,cAAc,EAAE;IAC/C;IACA,MAAMC,eAAe,GAAGnD,OAAO,CAACC,GAAG,CAACmD,YAAY;IAChD,MAAMC,iBAAiB,GAAGJ,GAAG,IAAI,CAACC,cAAc,IAAIhE,eAAe;IACnE;IACA,IAAI,CAACiE,eAAe,IAAIE,iBAAiB,EAAE;MACzCxE,MAAM,CAACiB,QAAQ,CAAC6C,MAAM,CAAC,CAAC;MACxB;IACF;IAEA,IAAI,OAAOxB,kBAAkB,KAAK,UAAU,EAAE;MAC5C;MACAA,kBAAkB,CAAC,CAAC;IACtB;IAEA,IAAIyB,iBAAiB,CAAC,CAAC,EAAE;MACvB;MACA1B,eAAe,CAAC,CAAC;IACnB;EACF;;EAEA;EACA,IAAIoC,MAAM,GAAGhE,MAAM,CAACC,GAAG,CAACgE,KAAK,CAAC,eAAgB,IAAI,EAAEP,kBAAkB,CAAC;;EAEvE;EACA,IAAIM,MAAM,IAAIA,MAAM,CAACE,IAAI,EAAE;IACzBF,MAAM,CAACE,IAAI,CACT,UAAUN,cAAc,EAAE;MACxBF,kBAAkB,CAAC,IAAI,EAAEE,cAAc,CAAC;IAC1C,CAAC,EACD,UAAUD,GAAG,EAAE;MACbD,kBAAkB,CAACC,GAAG,EAAE,IAAI,CAAC;IAC/B,CACF,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script"}