{"ast": null, "code": "const _excluded = [\"as\", \"active\", \"eventKey\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport NavContext from './NavContext';\nimport SelectableContext, { makeEventKey } from './SelectableContext';\nimport Button from './Button';\nimport { dataAttr } from './DataKey';\nimport TabContext from './TabContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useNavItem({\n  key,\n  onClick,\n  active,\n  id,\n  role,\n  disabled\n}) {\n  const parentOnSelect = useContext(SelectableContext);\n  const navContext = useContext(NavContext);\n  const tabContext = useContext(TabContext);\n  let isActive = active;\n  const props = {\n    role\n  };\n  if (navContext) {\n    if (!role && navContext.role === 'tablist') props.role = 'tab';\n    const contextControllerId = navContext.getControllerId(key != null ? key : null);\n    const contextControlledId = navContext.getControlledId(key != null ? key : null);\n\n    // @ts-ignore\n    props[dataAttr('event-key')] = key;\n    props.id = contextControllerId || id;\n    isActive = active == null && key != null ? navContext.activeKey === key : active;\n\n    /**\n     * Simplified scenario for `mountOnEnter`.\n     *\n     * While it would make sense to keep 'aria-controls' for tabs that have been mounted at least\n     * once, it would also complicate the code quite a bit, for very little gain.\n     * The following implementation is probably good enough.\n     *\n     * @see https://github.com/react-restart/ui/pull/40#issuecomment-1009971561\n     */\n    if (isActive || !(tabContext != null && tabContext.unmountOnExit) && !(tabContext != null && tabContext.mountOnEnter)) props['aria-controls'] = contextControlledId;\n  }\n  if (props.role === 'tab') {\n    props['aria-selected'] = isActive;\n    if (!isActive) {\n      props.tabIndex = -1;\n    }\n    if (disabled) {\n      props.tabIndex = -1;\n      props['aria-disabled'] = true;\n    }\n  }\n  props.onClick = useEventCallback(e => {\n    if (disabled) return;\n    onClick == null ? void 0 : onClick(e);\n    if (key == null) {\n      return;\n    }\n    if (parentOnSelect && !e.isPropagationStopped()) {\n      parentOnSelect(key, e);\n    }\n  });\n  return [props, {\n    isActive\n  }];\n}\nconst NavItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      as: Component = Button,\n      active,\n      eventKey\n    } = _ref,\n    options = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [props, meta] = useNavItem(Object.assign({\n    key: makeEventKey(eventKey, options.href),\n    active\n  }, options));\n\n  // @ts-ignore\n  props[dataAttr('active')] = meta.isActive;\n  return /*#__PURE__*/_jsx(Component, Object.assign({}, options, props, {\n    ref: ref\n  }));\n});\nNavItem.displayName = 'NavItem';\nexport default NavItem;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "React", "useContext", "useEventCallback", "NavContext", "SelectableContext", "makeEventKey", "<PERSON><PERSON>", "dataAttr", "TabContext", "jsx", "_jsx", "useNavItem", "key", "onClick", "active", "id", "role", "disabled", "parentOnSelect", "navContext", "tabContext", "isActive", "props", "contextControllerId", "getControllerId", "contextControlledId", "getControlledId", "active<PERSON><PERSON>", "unmountOnExit", "mountOnEnter", "tabIndex", "isPropagationStopped", "NavItem", "forwardRef", "_ref", "ref", "as", "Component", "eventKey", "options", "meta", "Object", "assign", "href", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/@restart/ui/esm/NavItem.js"], "sourcesContent": ["const _excluded = [\"as\", \"active\", \"eventKey\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport NavContext from './NavContext';\nimport SelectableContext, { makeEventKey } from './SelectableContext';\nimport Button from './Button';\nimport { dataAttr } from './DataKey';\nimport TabContext from './TabContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useNavItem({\n  key,\n  onClick,\n  active,\n  id,\n  role,\n  disabled\n}) {\n  const parentOnSelect = useContext(SelectableContext);\n  const navContext = useContext(NavContext);\n  const tabContext = useContext(TabContext);\n  let isActive = active;\n  const props = {\n    role\n  };\n  if (navContext) {\n    if (!role && navContext.role === 'tablist') props.role = 'tab';\n    const contextControllerId = navContext.getControllerId(key != null ? key : null);\n    const contextControlledId = navContext.getControlledId(key != null ? key : null);\n\n    // @ts-ignore\n    props[dataAttr('event-key')] = key;\n    props.id = contextControllerId || id;\n    isActive = active == null && key != null ? navContext.activeKey === key : active;\n\n    /**\n     * Simplified scenario for `mountOnEnter`.\n     *\n     * While it would make sense to keep 'aria-controls' for tabs that have been mounted at least\n     * once, it would also complicate the code quite a bit, for very little gain.\n     * The following implementation is probably good enough.\n     *\n     * @see https://github.com/react-restart/ui/pull/40#issuecomment-1009971561\n     */\n    if (isActive || !(tabContext != null && tabContext.unmountOnExit) && !(tabContext != null && tabContext.mountOnEnter)) props['aria-controls'] = contextControlledId;\n  }\n  if (props.role === 'tab') {\n    props['aria-selected'] = isActive;\n    if (!isActive) {\n      props.tabIndex = -1;\n    }\n    if (disabled) {\n      props.tabIndex = -1;\n      props['aria-disabled'] = true;\n    }\n  }\n  props.onClick = useEventCallback(e => {\n    if (disabled) return;\n    onClick == null ? void 0 : onClick(e);\n    if (key == null) {\n      return;\n    }\n    if (parentOnSelect && !e.isPropagationStopped()) {\n      parentOnSelect(key, e);\n    }\n  });\n  return [props, {\n    isActive\n  }];\n}\nconst NavItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      as: Component = Button,\n      active,\n      eventKey\n    } = _ref,\n    options = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [props, meta] = useNavItem(Object.assign({\n    key: makeEventKey(eventKey, options.href),\n    active\n  }, options));\n\n  // @ts-ignore\n  props[dataAttr('active')] = meta.isActive;\n  return /*#__PURE__*/_jsx(Component, Object.assign({}, options, props, {\n    ref: ref\n  }));\n});\nNavItem.displayName = 'NavItem';\nexport default NavItem;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC;AAC9C,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IAAE,IAAIF,CAAC,CAACK,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,EAAE;IAAUD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACpM,OAAO,KAAKK,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,iBAAiB,IAAIC,YAAY,QAAQ,qBAAqB;AACrE,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,UAAUA,CAAC;EACzBC,GAAG;EACHC,OAAO;EACPC,MAAM;EACNC,EAAE;EACFC,IAAI;EACJC;AACF,CAAC,EAAE;EACD,MAAMC,cAAc,GAAGjB,UAAU,CAACG,iBAAiB,CAAC;EACpD,MAAMe,UAAU,GAAGlB,UAAU,CAACE,UAAU,CAAC;EACzC,MAAMiB,UAAU,GAAGnB,UAAU,CAACO,UAAU,CAAC;EACzC,IAAIa,QAAQ,GAAGP,MAAM;EACrB,MAAMQ,KAAK,GAAG;IACZN;EACF,CAAC;EACD,IAAIG,UAAU,EAAE;IACd,IAAI,CAACH,IAAI,IAAIG,UAAU,CAACH,IAAI,KAAK,SAAS,EAAEM,KAAK,CAACN,IAAI,GAAG,KAAK;IAC9D,MAAMO,mBAAmB,GAAGJ,UAAU,CAACK,eAAe,CAACZ,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,IAAI,CAAC;IAChF,MAAMa,mBAAmB,GAAGN,UAAU,CAACO,eAAe,CAACd,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,IAAI,CAAC;;IAEhF;IACAU,KAAK,CAACf,QAAQ,CAAC,WAAW,CAAC,CAAC,GAAGK,GAAG;IAClCU,KAAK,CAACP,EAAE,GAAGQ,mBAAmB,IAAIR,EAAE;IACpCM,QAAQ,GAAGP,MAAM,IAAI,IAAI,IAAIF,GAAG,IAAI,IAAI,GAAGO,UAAU,CAACQ,SAAS,KAAKf,GAAG,GAAGE,MAAM;;IAEhF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIO,QAAQ,IAAI,EAAED,UAAU,IAAI,IAAI,IAAIA,UAAU,CAACQ,aAAa,CAAC,IAAI,EAAER,UAAU,IAAI,IAAI,IAAIA,UAAU,CAACS,YAAY,CAAC,EAAEP,KAAK,CAAC,eAAe,CAAC,GAAGG,mBAAmB;EACrK;EACA,IAAIH,KAAK,CAACN,IAAI,KAAK,KAAK,EAAE;IACxBM,KAAK,CAAC,eAAe,CAAC,GAAGD,QAAQ;IACjC,IAAI,CAACA,QAAQ,EAAE;MACbC,KAAK,CAACQ,QAAQ,GAAG,CAAC,CAAC;IACrB;IACA,IAAIb,QAAQ,EAAE;MACZK,KAAK,CAACQ,QAAQ,GAAG,CAAC,CAAC;MACnBR,KAAK,CAAC,eAAe,CAAC,GAAG,IAAI;IAC/B;EACF;EACAA,KAAK,CAACT,OAAO,GAAGX,gBAAgB,CAACR,CAAC,IAAI;IACpC,IAAIuB,QAAQ,EAAE;IACdJ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACnB,CAAC,CAAC;IACrC,IAAIkB,GAAG,IAAI,IAAI,EAAE;MACf;IACF;IACA,IAAIM,cAAc,IAAI,CAACxB,CAAC,CAACqC,oBAAoB,CAAC,CAAC,EAAE;MAC/Cb,cAAc,CAACN,GAAG,EAAElB,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;EACF,OAAO,CAAC4B,KAAK,EAAE;IACbD;EACF,CAAC,CAAC;AACJ;AACA,MAAMW,OAAO,GAAG,aAAahC,KAAK,CAACiC,UAAU,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;EAC3D,IAAI;MACAC,EAAE,EAAEC,SAAS,GAAG/B,MAAM;MACtBQ,MAAM;MACNwB;IACF,CAAC,GAAGJ,IAAI;IACRK,OAAO,GAAG/C,6BAA6B,CAAC0C,IAAI,EAAE3C,SAAS,CAAC;EAC1D,MAAM,CAAC+B,KAAK,EAAEkB,IAAI,CAAC,GAAG7B,UAAU,CAAC8B,MAAM,CAACC,MAAM,CAAC;IAC7C9B,GAAG,EAAEP,YAAY,CAACiC,QAAQ,EAAEC,OAAO,CAACI,IAAI,CAAC;IACzC7B;EACF,CAAC,EAAEyB,OAAO,CAAC,CAAC;;EAEZ;EACAjB,KAAK,CAACf,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAGiC,IAAI,CAACnB,QAAQ;EACzC,OAAO,aAAaX,IAAI,CAAC2B,SAAS,EAAEI,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,OAAO,EAAEjB,KAAK,EAAE;IACpEa,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFH,OAAO,CAACY,WAAW,GAAG,SAAS;AAC/B,eAAeZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}