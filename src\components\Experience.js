import React from 'react';
import { Container } from 'react-bootstrap';
import styled from 'styled-components';

const ExperienceSection = styled.section`
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: ${({ theme }) => theme.background};
  padding: 100px 0;
`;

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 50px;
  color: ${({ theme }) => theme.color};
  
  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background: ${({ theme }) => theme.accentColor};
    margin: 20px auto;
  }
`;

const PlaceholderText = styled.p`
  font-size: 1.2rem;
  text-align: center;
  color: ${({ theme }) => theme.color};
  opacity: 0.7;
`;

const Experience = () => {
  return (
    <ExperienceSection id="experience">
      <Container>
        <SectionTitle>Experience</SectionTitle>
        <PlaceholderText>
          Experience timeline will be implemented here with professional work history
        </PlaceholderText>
      </Container>
    </ExperienceSection>
  );
};

export default Experience;
