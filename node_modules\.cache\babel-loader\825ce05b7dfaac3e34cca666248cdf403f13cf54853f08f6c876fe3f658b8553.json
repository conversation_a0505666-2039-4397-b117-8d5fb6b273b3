{"ast": null, "code": "import classNames from 'classnames';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport Form<PERSON>heck from './FormCheck';\nimport FormControl from './FormControl';\nimport FormFloating from './FormFloating';\nimport FormGroup from './FormGroup';\nimport FormLabel from './FormLabel';\nimport FormRange from './FormRange';\nimport FormSelect from './FormSelect';\nimport FormText from './FormText';\nimport Switch from './Switch';\nimport FloatingLabel from './FloatingLabel';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * The Form `ref` will be forwarded to the underlying element,\n   * which means, unless it's rendered `as` a composite component,\n   * it will be a DOM node, when resolved.\n   *\n   * @type {ReactRef}\n   * @alias ref\n   */\n  _ref: PropTypes.any,\n  /**\n   * Mark a form as having been validated. Setting it to `true` will\n   * toggle any validation styles on the forms elements.\n   */\n  validated: PropTypes.bool,\n  as: PropTypes.elementType\n};\nconst Form = /*#__PURE__*/React.forwardRef(({\n  className,\n  validated,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'form',\n  ...props\n}, ref) => /*#__PURE__*/_jsx(Component, {\n  ...props,\n  ref: ref,\n  className: classNames(className, validated && 'was-validated')\n}));\nForm.displayName = 'Form';\nForm.propTypes = propTypes;\nexport default Object.assign(Form, {\n  Group: FormGroup,\n  Control: FormControl,\n  Floating: FormFloating,\n  Check: FormCheck,\n  Switch,\n  Label: FormLabel,\n  Text: FormText,\n  Range: FormRange,\n  Select: FormSelect,\n  FloatingLabel\n});", "map": {"version": 3, "names": ["classNames", "PropTypes", "React", "FormCheck", "FormControl", "FormFloating", "FormGroup", "FormLabel", "FormRange", "FormSelect", "FormText", "Switch", "FloatingLabel", "jsx", "_jsx", "propTypes", "_ref", "any", "validated", "bool", "as", "elementType", "Form", "forwardRef", "className", "Component", "props", "ref", "displayName", "Object", "assign", "Group", "Control", "Floating", "Check", "Label", "Text", "Range", "Select"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/Form.js"], "sourcesContent": ["import classNames from 'classnames';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport Form<PERSON>heck from './FormCheck';\nimport FormControl from './FormControl';\nimport FormFloating from './FormFloating';\nimport FormGroup from './FormGroup';\nimport FormLabel from './FormLabel';\nimport FormRange from './FormRange';\nimport FormSelect from './FormSelect';\nimport FormText from './FormText';\nimport Switch from './Switch';\nimport FloatingLabel from './FloatingLabel';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * The Form `ref` will be forwarded to the underlying element,\n   * which means, unless it's rendered `as` a composite component,\n   * it will be a DOM node, when resolved.\n   *\n   * @type {ReactRef}\n   * @alias ref\n   */\n  _ref: PropTypes.any,\n  /**\n   * Mark a form as having been validated. Setting it to `true` will\n   * toggle any validation styles on the forms elements.\n   */\n  validated: PropTypes.bool,\n  as: PropTypes.elementType\n};\nconst Form = /*#__PURE__*/React.forwardRef(({\n  className,\n  validated,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'form',\n  ...props\n}, ref) => /*#__PURE__*/_jsx(Component, {\n  ...props,\n  ref: ref,\n  className: classNames(className, validated && 'was-validated')\n}));\nForm.displayName = 'Form';\nForm.propTypes = propTypes;\nexport default Object.assign(Form, {\n  Group: FormGroup,\n  Control: FormControl,\n  Floating: FormFloating,\n  Check: FormCheck,\n  Switch,\n  Label: FormLabel,\n  Text: FormText,\n  Range: FormRange,\n  Select: FormSelect,\n  FloatingLabel\n});"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG;EAChB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,IAAI,EAAEf,SAAS,CAACgB,GAAG;EACnB;AACF;AACA;AACA;EACEC,SAAS,EAAEjB,SAAS,CAACkB,IAAI;EACzBC,EAAE,EAAEnB,SAAS,CAACoB;AAChB,CAAC;AACD,MAAMC,IAAI,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAAC,CAAC;EAC1CC,SAAS;EACTN,SAAS;EACT;EACAE,EAAE,EAAEK,SAAS,GAAG,MAAM;EACtB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK,aAAab,IAAI,CAACW,SAAS,EAAE;EACtC,GAAGC,KAAK;EACRC,GAAG,EAAEA,GAAG;EACRH,SAAS,EAAExB,UAAU,CAACwB,SAAS,EAAEN,SAAS,IAAI,eAAe;AAC/D,CAAC,CAAC,CAAC;AACHI,IAAI,CAACM,WAAW,GAAG,MAAM;AACzBN,IAAI,CAACP,SAAS,GAAGA,SAAS;AAC1B,eAAec,MAAM,CAACC,MAAM,CAACR,IAAI,EAAE;EACjCS,KAAK,EAAEzB,SAAS;EAChB0B,OAAO,EAAE5B,WAAW;EACpB6B,QAAQ,EAAE5B,YAAY;EACtB6B,KAAK,EAAE/B,SAAS;EAChBQ,MAAM;EACNwB,KAAK,EAAE5B,SAAS;EAChB6B,IAAI,EAAE1B,QAAQ;EACd2B,KAAK,EAAE7B,SAAS;EAChB8B,MAAM,EAAE7B,UAAU;EAClBG;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}