{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\Experience.js\";\nimport React from 'react';\nimport { Container } from 'react-bootstrap';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExperienceSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({\n  theme\n}) => theme.background};\n  padding: 100px 0;\n`;\n_c = ExperienceSection;\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({\n  theme\n}) => theme.color};\n  \n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({\n  theme\n}) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n_c2 = SectionTitle;\nconst PlaceholderText = styled.p`\n  font-size: 1.2rem;\n  text-align: center;\n  color: ${({\n  theme\n}) => theme.color};\n  opacity: 0.7;\n`;\n_c3 = PlaceholderText;\nconst Experience = () => {\n  return /*#__PURE__*/_jsxDEV(ExperienceSection, {\n    id: \"experience\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"Experience\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PlaceholderText, {\n        children: \"Experience timeline will be implemented here with professional work history\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_c4 = Experience;\nexport default Experience;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ExperienceSection\");\n$RefreshReg$(_c2, \"SectionTitle\");\n$RefreshReg$(_c3, \"PlaceholderText\");\n$RefreshReg$(_c4, \"Experience\");", "map": {"version": 3, "names": ["React", "Container", "styled", "jsxDEV", "_jsxDEV", "ExperienceSection", "section", "theme", "background", "_c", "SectionTitle", "h2", "color", "accentColor", "_c2", "PlaceholderText", "p", "_c3", "Experience", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/Experience.js"], "sourcesContent": ["import React from 'react';\nimport { Container } from 'react-bootstrap';\nimport styled from 'styled-components';\n\nconst ExperienceSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({ theme }) => theme.background};\n  padding: 100px 0;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({ theme }) => theme.color};\n  \n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({ theme }) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n\nconst PlaceholderText = styled.p`\n  font-size: 1.2rem;\n  text-align: center;\n  color: ${({ theme }) => theme.color};\n  opacity: 0.7;\n`;\n\nconst Experience = () => {\n  return (\n    <ExperienceSection id=\"experience\">\n      <Container>\n        <SectionTitle>Experience</SectionTitle>\n        <PlaceholderText>\n          Experience timeline will be implemented here with professional work history\n        </PlaceholderText>\n      </Container>\n    </ExperienceSection>\n  );\n};\n\nexport default Experience;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,iBAAiB,GAAGH,MAAM,CAACI,OAAO;AACxC;AACA;AACA;AACA,gBAAgB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,UAAU;AAC/C;AACA,CAAC;AAACC,EAAA,GANIJ,iBAAiB;AAQvB,MAAMK,YAAY,GAAGR,MAAM,CAACS,EAAE;AAC9B;AACA;AACA;AACA;AACA,WAAW,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,WAAW;AAClD;AACA;AACA,CAAC;AAACC,GAAA,GAfIJ,YAAY;AAiBlB,MAAMK,eAAe,GAAGb,MAAM,CAACc,CAAC;AAChC;AACA;AACA,WAAW,CAAC;EAAET;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA,CAAC;AAACK,GAAA,GALIF,eAAe;AAOrB,MAAMG,UAAU,GAAGA,CAAA,KAAM;EACvB,oBACEd,OAAA,CAACC,iBAAiB;IAACc,EAAE,EAAC,YAAY;IAAAC,QAAA,eAChChB,OAAA,CAACH,SAAS;MAAAmB,QAAA,gBACRhB,OAAA,CAACM,YAAY;QAAAU,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACvCpB,OAAA,CAACW,eAAe;QAAAK,QAAA,EAAC;MAEjB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAExB,CAAC;AAACC,GAAA,GAXIP,UAAU;AAahB,eAAeA,UAAU;AAAC,IAAAT,EAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}