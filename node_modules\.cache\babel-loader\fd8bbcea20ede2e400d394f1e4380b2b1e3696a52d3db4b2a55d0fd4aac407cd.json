{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_auth0 = register(\"auth0\", {\n  \"color\": \"#191919\",\n  \"path\": \"M0 0v64h64V0Zm34.088 16.287c3.965.307 7.705 1.604 9.787 2.45a2.73 2.73 0 0 1 1.707 2.523v8.164a.79.79 0 0 1-.92.776l-.77-.124c-5.246-.858-9.356-5.162-10.22-10.37l-.004-.003-.29-2.504c-.06-.393.202-.95.71-.912m-3.363.006c.507-.038.777.515.707.913l-.291 2.503c-.865 5.205-4.974 9.51-10.221 10.369v.004l-.77.124a.79.79 0 0 1-.92-.776v-8.164c0-1.107.676-2.104 1.707-2.522 2.086-.845 5.823-2.145 9.788-2.45m-10.82 15.92c.346 0 .762.12 1.019.149 7.168 1.403 10.496 6.133 10.496 15.089 0 .45-.45.758-.826.51-3.297-2.207-10.55-7.967-11.3-15.175-.014-.454.264-.574.61-.574m25.001 0c.347-.001.626.119.612.573-.75 7.208-8.005 12.968-11.301 15.175-.376.248-.826-.06-.826-.51 0-8.956 3.33-13.686 10.498-15.09.257-.028.67-.148 1.017-.149\"\n});\nexport { _socialIcons_auth0 as default };", "map": {"version": 3, "names": ["register", "_socialIcons_auth0", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/auth0.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_auth0 = register(\"auth0\", {\"color\":\"#191919\",\"path\":\"M0 0v64h64V0Zm34.088 16.287c3.965.307 7.705 1.604 9.787 2.45a2.73 2.73 0 0 1 1.707 2.523v8.164a.79.79 0 0 1-.92.776l-.77-.124c-5.246-.858-9.356-5.162-10.22-10.37l-.004-.003-.29-2.504c-.06-.393.202-.95.71-.912m-3.363.006c.507-.038.777.515.707.913l-.291 2.503c-.865 5.205-4.974 9.51-10.221 10.369v.004l-.77.124a.79.79 0 0 1-.92-.776v-8.164c0-1.107.676-2.104 1.707-2.522 2.086-.845 5.823-2.145 9.788-2.45m-10.82 15.92c.346 0 .762.12 1.019.149 7.168 1.403 10.496 6.133 10.496 15.089 0 .45-.45.758-.826.51-3.297-2.207-10.55-7.967-11.3-15.175-.014-.454.264-.574.61-.574m25.001 0c.347-.001.626.119.612.573-.75 7.208-8.005 12.968-11.301 15.175-.376.248-.826-.06-.826-.51 0-8.956 3.33-13.686 10.498-15.09.257-.028.67-.148 1.017-.149\"});\n\nexport { _socialIcons_auth0 as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,kBAAkB,GAAGD,QAAQ,CAAC,OAAO,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAqtB,CAAC,CAAC;AAE5xB,SAASC,kBAAkB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}