{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport App from './App';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nReactDOM.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 7,\n  columnNumber: 3\n}, this), document.getElementById('root'));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsxDEV", "_jsxDEV", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "document", "getElementById"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom';\nimport App from './App';\nimport './index.css';\n\nReactDOM.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>,\n  document.getElementById('root')\n);"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErBH,QAAQ,CAACI,MAAM,cACbD,OAAA,CAACJ,KAAK,CAACM,UAAU;EAAAC,QAAA,eACfH,OAAA,CAACF,GAAG;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CAAC,EACnBC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}