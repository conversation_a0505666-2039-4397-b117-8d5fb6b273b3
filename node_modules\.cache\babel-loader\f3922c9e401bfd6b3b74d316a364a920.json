{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Route, Switch } from 'react-router-dom';\nimport HomePage from './components/HomePage';\nimport AboutPage from './components/AboutPage';\nimport ResumePage from './components/ResumePage';\nimport LearningOutcomesPage from './components/LearningOutcomesPage';\nimport './styles/HomePage.css';\nimport './styles/AboutPage.css';\nimport './styles/ResumePage.css';\nimport './styles/LearningOutcomesPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Switch, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        exact: true,\n        component: HomePage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/about\",\n        component: AboutPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/resume\",\n        component: ResumePage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/learning-outcomes\",\n        component: LearningOutcomesPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Switch", "HomePage", "AboutPage", "ResumePage", "LearningOutcomesPage", "jsxDEV", "_jsxDEV", "App", "children", "path", "exact", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Route, Switch } from 'react-router-dom';\nimport HomePage from './components/HomePage';\nimport AboutPage from './components/AboutPage';\nimport ResumePage from './components/ResumePage';\nimport LearningOutcomesPage from './components/LearningOutcomesPage';\nimport './styles/HomePage.css';\nimport './styles/AboutPage.css';\nimport './styles/ResumePage.css';\nimport './styles/LearningOutcomesPage.css';\n\nfunction App() {\n  return (\n    <Router>\n      <Switch>\n        <Route path=\"/\" exact component={HomePage} />\n        <Route path=\"/about\" component={AboutPage} />\n        <Route path=\"/resume\" component={ResumePage} />\n        <Route path=\"/learning-outcomes\" component={LearningOutcomesPage} />\n      </Switch>\n    </Router>\n  );\n}\n\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AACzE,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAO,uBAAuB;AAC9B,OAAO,wBAAwB;AAC/B,OAAO,yBAAyB;AAChC,OAAO,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACR,MAAM;IAAAU,QAAA,eACLF,OAAA,CAACN,MAAM;MAAAQ,QAAA,gBACLF,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,GAAG;QAACC,KAAK;QAACC,SAAS,EAAEV;MAAS;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CT,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,QAAQ;QAACE,SAAS,EAAET;MAAU;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CT,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,SAAS;QAACE,SAAS,EAAER;MAAW;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CT,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,oBAAoB;QAACE,SAAS,EAAEP;MAAqB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACC,EAAA,GAXQT,GAAG;AAaZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}