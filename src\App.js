import React from 'react';
import { BrowserRouter as Router, Route, Switch } from 'react-router-dom';
import HomePage from './components/HomePage';
import AboutPage from './components/AboutPage';
import ResumePage from './components/ResumePage';
import LearningOutcomesPage from './components/LearningOutcomesPage';

function App() {
  return (
    <Router basename="/personal-e-portfolio">
      <Switch>
        <Route path="/" exact component={HomePage} />
        <Route path="/about" component={AboutPage} />
        <Route path="/resume" component={ResumePage} />
        <Route path="/learning-outcomes" component={LearningOutcomesPage} />
      </Switch>
    </Router>
  );
}

export default App;