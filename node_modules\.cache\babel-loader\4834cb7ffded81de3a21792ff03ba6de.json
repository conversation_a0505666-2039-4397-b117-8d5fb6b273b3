{"ast": null, "code": "// @remove-on-eject-begin\n/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n// @remove-on-eject-end\n'use strict';\n\nconst {\n  dismissRuntimeErrors,\n  reportRuntimeError\n} = require('react-error-overlay');\nmodule.exports = {\n  clearRuntimeErrors: dismissRuntimeErrors,\n  handleRuntimeError: reportRuntimeError\n};", "map": {"version": 3, "names": ["dismissRuntimeErrors", "reportRuntimeError", "require", "module", "exports", "clearRuntimeErrors", "handleRuntimeError"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-dev-utils/refreshOverlayInterop.js"], "sourcesContent": ["// @remove-on-eject-begin\n/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n// @remove-on-eject-end\n'use strict';\n\nconst {\n  dismissRuntimeErrors,\n  reportRuntimeError,\n} = require('react-error-overlay');\n\nmodule.exports = {\n  clearRuntimeErrors: dismissRuntimeErrors,\n  handleRuntimeError: reportRuntimeError,\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAEZ,MAAM;EACJA,oBAAoB;EACpBC;AACF,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAElCC,MAAM,CAACC,OAAO,GAAG;EACfC,kBAAkB,EAAEL,oBAAoB;EACxCM,kBAAkB,EAAEL;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}