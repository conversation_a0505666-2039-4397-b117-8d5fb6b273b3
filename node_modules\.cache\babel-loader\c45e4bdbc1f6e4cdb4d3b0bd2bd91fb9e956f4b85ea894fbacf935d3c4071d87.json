{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_xing = register(\"xing\", {\n  \"color\": \"#0698A0\",\n  \"path\": \"M1.008 0C.45 0 0 .45 0 1.01v62.11c0 .56.45 1.01 1.008 1.01h62.02c.56 0 1.009-.45 1.009-1.01V1.01c0-.56-.45-1.01-1.009-1.01ZM41.72 16.032h5.482c.327 0 .585.106.723.296.143.197.139.459-.012.714L35.898 35.145a.025.025 0 0 0 0 .032l7.65 11.91c.152.257.156.517.012.715-.138.19-.394.295-.721.295h-5.42c-.83 0-1.247-.47-1.516-.88l-7.71-12.056c.386-.58 12.074-18.248 12.074-18.248.291-.446.642-.88 1.452-.88m-22.794 6.334h5.425c.832 0 1.24.456 1.51.867l3.731 5.544-5.857 8.828c-.277.427-.668.893-1.48.893h-5.426c-.326 0-.571-.125-.71-.315-.142-.198-.15-.453 0-.709l5.766-8.672c.006-.01.006-.015 0-.025l-3.668-5.413c-.152-.258-.175-.513-.032-.71.138-.192.414-.288.74-.288\"\n});\nexport { _socialIcons_xing as default };", "map": {"version": 3, "names": ["register", "_socialIcons_xing", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/xing.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_xing = register(\"xing\", {\"color\":\"#0698A0\",\"path\":\"M1.008 0C.45 0 0 .45 0 1.01v62.11c0 .56.45 1.01 1.008 1.01h62.02c.56 0 1.009-.45 1.009-1.01V1.01c0-.56-.45-1.01-1.009-1.01ZM41.72 16.032h5.482c.327 0 .585.106.723.296.143.197.139.459-.012.714L35.898 35.145a.025.025 0 0 0 0 .032l7.65 11.91c.152.257.156.517.012.715-.138.19-.394.295-.721.295h-5.42c-.83 0-1.247-.47-1.516-.88l-7.71-12.056c.386-.58 12.074-18.248 12.074-18.248.291-.446.642-.88 1.452-.88m-22.794 6.334h5.425c.832 0 1.24.456 1.51.867l3.731 5.544-5.857 8.828c-.277.427-.668.893-1.48.893h-5.426c-.326 0-.571-.125-.71-.315-.142-.198-.15-.453 0-.709l5.766-8.672c.006-.01.006-.015 0-.025l-3.668-5.413c-.152-.258-.175-.513-.032-.71.138-.192.414-.288.74-.288\"});\n\nexport { _socialIcons_xing as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,iBAAiB,GAAGD,QAAQ,CAAC,MAAM,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAwpB,CAAC,CAAC;AAE7tB,SAASC,iBAAiB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}