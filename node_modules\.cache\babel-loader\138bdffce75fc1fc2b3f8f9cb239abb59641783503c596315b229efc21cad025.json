{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Collapse from './Collapse';\nimport AccordionContext, { isAccordionItemSelected } from './AccordionContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * This component accepts all of [`Collapse`'s props](/docs/utilities/transitions#collapse-1).\n */\nconst AccordionCollapse = /*#__PURE__*/React.forwardRef(({\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  children,\n  eventKey,\n  ...props\n}, ref) => {\n  const {\n    activeEventKey\n  } = useContext(AccordionContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-collapse');\n  return /*#__PURE__*/_jsx(Collapse, {\n    ref: ref,\n    in: isAccordionItemSelected(activeEventKey, eventKey),\n    ...props,\n    className: classNames(className, bsPrefix),\n    children: /*#__PURE__*/_jsx(Component, {\n      children: React.Children.only(children)\n    })\n  });\n});\nAccordionCollapse.displayName = 'AccordionCollapse';\nexport default AccordionCollapse;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useBootstrapPrefix", "Collapse", "AccordionContext", "isAccordionItemSelected", "jsx", "_jsx", "AccordionCollapse", "forwardRef", "as", "Component", "bsPrefix", "className", "children", "eventKey", "props", "ref", "activeEventKey", "in", "Children", "only", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/AccordionCollapse.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Collapse from './Collapse';\nimport AccordionContext, { isAccordionItemSelected } from './AccordionContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * This component accepts all of [`Collapse`'s props](/docs/utilities/transitions#collapse-1).\n */\nconst AccordionCollapse = /*#__PURE__*/React.forwardRef(({\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  children,\n  eventKey,\n  ...props\n}, ref) => {\n  const {\n    activeEventKey\n  } = useContext(AccordionContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-collapse');\n  return /*#__PURE__*/_jsx(Collapse, {\n    ref: ref,\n    in: isAccordionItemSelected(activeEventKey, eventKey),\n    ...props,\n    className: classNames(className, bsPrefix),\n    children: /*#__PURE__*/_jsx(Component, {\n      children: React.Children.only(children)\n    })\n  });\n});\nAccordionCollapse.displayName = 'AccordionCollapse';\nexport default AccordionCollapse;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,gBAAgB,IAAIC,uBAAuB,QAAQ,oBAAoB;AAC9E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAC;EACvDC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrBC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACRC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC;EACF,CAAC,GAAGjB,UAAU,CAACG,gBAAgB,CAAC;EAChCQ,QAAQ,GAAGV,kBAAkB,CAACU,QAAQ,EAAE,oBAAoB,CAAC;EAC7D,OAAO,aAAaL,IAAI,CAACJ,QAAQ,EAAE;IACjCc,GAAG,EAAEA,GAAG;IACRE,EAAE,EAAEd,uBAAuB,CAACa,cAAc,EAAEH,QAAQ,CAAC;IACrD,GAAGC,KAAK;IACRH,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAED,QAAQ,CAAC;IAC1CE,QAAQ,EAAE,aAAaP,IAAI,CAACI,SAAS,EAAE;MACrCG,QAAQ,EAAEd,KAAK,CAACoB,QAAQ,CAACC,IAAI,CAACP,QAAQ;IACxC,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,iBAAiB,CAACc,WAAW,GAAG,mBAAmB;AACnD,eAAed,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}