{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\About.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Image } from 'react-bootstrap';\nimport Fade from 'react-reveal/Fade';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({\n  theme\n}) => theme.background};\n  padding: 100px 0;\n`;\n_c = AboutSection;\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({\n  theme\n}) => theme.color};\n  \n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({\n  theme\n}) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n_c2 = SectionTitle;\nconst AboutText = styled.p`\n  font-size: 1.1rem;\n  line-height: 1.8;\n  color: ${({\n  theme\n}) => theme.color};\n  text-align: justify;\n`;\n_c3 = AboutText;\nconst ProfileImage = styled(Image)`\n  border-radius: 15px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  transition: transform 0.3s ease;\n  \n  &:hover {\n    transform: scale(1.05);\n  }\n`;\n_c4 = ProfileImage;\nconst About = () => {\n  _s();\n  const [aboutData, setAboutData] = useState(null);\n  useEffect(() => {\n    fetch('/personal-e-portfolio/profile/about.json').then(response => response.json()).then(data => setAboutData(data)).catch(error => console.error('Error loading about data:', error));\n  }, []);\n  if (!aboutData) return null;\n  return /*#__PURE__*/_jsxDEV(AboutSection, {\n    id: \"about\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"About Me\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          lg: 6,\n          className: \"mb-4 mb-lg-0\",\n          children: /*#__PURE__*/_jsxDEV(Fade, {\n            left: true,\n            children: /*#__PURE__*/_jsxDEV(ProfileImage, {\n              src: `/personal-e-portfolio/${aboutData.imageSource}`,\n              alt: \"Profile\",\n              fluid: true,\n              onError: e => {\n                e.target.src = 'https://via.placeholder.com/400x450/3D84C6/ffffff?text=Profile';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Fade, {\n            right: true,\n            children: /*#__PURE__*/_jsxDEV(AboutText, {\n              children: aboutData.about\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(About, \"sjM3uVVUh0G1+yUweBsxpHSjhFk=\");\n_c5 = About;\nexport default About;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AboutSection\");\n$RefreshReg$(_c2, \"SectionTitle\");\n$RefreshReg$(_c3, \"AboutText\");\n$RefreshReg$(_c4, \"ProfileImage\");\n$RefreshReg$(_c5, \"About\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Image", "Fade", "styled", "jsxDEV", "_jsxDEV", "AboutSection", "section", "theme", "background", "_c", "SectionTitle", "h2", "color", "accentColor", "_c2", "AboutText", "p", "_c3", "ProfileImage", "_c4", "About", "_s", "aboutData", "setAboutData", "fetch", "then", "response", "json", "data", "catch", "error", "console", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "lg", "left", "src", "imageSource", "alt", "fluid", "onError", "e", "target", "right", "about", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/About.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Image } from 'react-bootstrap';\nimport Fade from 'react-reveal/Fade';\nimport styled from 'styled-components';\n\nconst AboutSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({ theme }) => theme.background};\n  padding: 100px 0;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({ theme }) => theme.color};\n  \n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({ theme }) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n\nconst AboutText = styled.p`\n  font-size: 1.1rem;\n  line-height: 1.8;\n  color: ${({ theme }) => theme.color};\n  text-align: justify;\n`;\n\nconst ProfileImage = styled(Image)`\n  border-radius: 15px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  transition: transform 0.3s ease;\n  \n  &:hover {\n    transform: scale(1.05);\n  }\n`;\n\nconst About = () => {\n  const [aboutData, setAboutData] = useState(null);\n\n  useEffect(() => {\n    fetch('/personal-e-portfolio/profile/about.json')\n      .then(response => response.json())\n      .then(data => setAboutData(data))\n      .catch(error => console.error('Error loading about data:', error));\n  }, []);\n\n  if (!aboutData) return null;\n\n  return (\n    <AboutSection id=\"about\">\n      <Container>\n        <SectionTitle>About Me</SectionTitle>\n        <Row className=\"align-items-center\">\n          <Col lg={6} className=\"mb-4 mb-lg-0\">\n            <Fade left>\n              <ProfileImage\n                src={`/personal-e-portfolio/${aboutData.imageSource}`}\n                alt=\"Profile\"\n                fluid\n                onError={(e) => {\n                  e.target.src = 'https://via.placeholder.com/400x450/3D84C6/ffffff?text=Profile';\n                }}\n              />\n            </Fade>\n          </Col>\n          <Col lg={6}>\n            <Fade right>\n              <AboutText>\n                {aboutData.about}\n              </AboutText>\n            </Fade>\n          </Col>\n        </Row>\n      </Container>\n    </AboutSection>\n  );\n};\n\nexport default About;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,QAAQ,iBAAiB;AAC5D,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGH,MAAM,CAACI,OAAO;AACnC;AACA;AACA;AACA,gBAAgB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,UAAU;AAC/C;AACA,CAAC;AAACC,EAAA,GANIJ,YAAY;AAQlB,MAAMK,YAAY,GAAGR,MAAM,CAACS,EAAE;AAC9B;AACA;AACA;AACA;AACA,WAAW,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,WAAW;AAClD;AACA;AACA,CAAC;AAACC,GAAA,GAfIJ,YAAY;AAiBlB,MAAMK,SAAS,GAAGb,MAAM,CAACc,CAAC;AAC1B;AACA;AACA,WAAW,CAAC;EAAET;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA,CAAC;AAACK,GAAA,GALIF,SAAS;AAOf,MAAMG,YAAY,GAAGhB,MAAM,CAACF,KAAK,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GARID,YAAY;AAUlB,MAAME,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd4B,KAAK,CAAC,0CAA0C,CAAC,CAC9CC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAIL,YAAY,CAACK,IAAI,CAAC,CAAC,CAChCC,KAAK,CAACC,KAAK,IAAIC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACR,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACElB,OAAA,CAACC,YAAY;IAAC2B,EAAE,EAAC,OAAO;IAAAC,QAAA,eACtB7B,OAAA,CAACP,SAAS;MAAAoC,QAAA,gBACR7B,OAAA,CAACM,YAAY;QAAAuB,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACrCjC,OAAA,CAACN,GAAG;QAACwC,SAAS,EAAC,oBAAoB;QAAAL,QAAA,gBACjC7B,OAAA,CAACL,GAAG;UAACwC,EAAE,EAAE,CAAE;UAACD,SAAS,EAAC,cAAc;UAAAL,QAAA,eAClC7B,OAAA,CAACH,IAAI;YAACuC,IAAI;YAAAP,QAAA,eACR7B,OAAA,CAACc,YAAY;cACXuB,GAAG,EAAE,yBAAyBnB,SAAS,CAACoB,WAAW,EAAG;cACtDC,GAAG,EAAC,SAAS;cACbC,KAAK;cACLC,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,gEAAgE;cACjF;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNjC,OAAA,CAACL,GAAG;UAACwC,EAAE,EAAE,CAAE;UAAAN,QAAA,eACT7B,OAAA,CAACH,IAAI;YAAC+C,KAAK;YAAAf,QAAA,eACT7B,OAAA,CAACW,SAAS;cAAAkB,QAAA,EACPX,SAAS,CAAC2B;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEnB,CAAC;AAAChB,EAAA,CAxCID,KAAK;AAAA8B,GAAA,GAAL9B,KAAK;AA0CX,eAAeA,KAAK;AAAC,IAAAX,EAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAA+B,GAAA;AAAAC,YAAA,CAAA1C,EAAA;AAAA0C,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}