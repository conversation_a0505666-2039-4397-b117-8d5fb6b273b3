{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\AboutPage.js\";\nimport React from 'react';\nimport './AboutPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"about-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"About Me\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"I am a passionate learner and developer, eager to create impactful solutions through technology and continuous self-improvement.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 9\n  }, this);\n};\n_c = AboutPage;\nexport default AboutPage;\nvar _c;\n$RefreshReg$(_c, \"AboutPage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AboutPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/AboutPage.js"], "sourcesContent": ["import React from 'react';\nimport './AboutPage.css';\n\nconst AboutPage = () => {\n    return (\n        <div className=\"about-container\">\n            <h1>About Me</h1>\n            <p>\n                I am a passionate learner and developer, eager to create impactful solutions through technology and continuous self-improvement.\n            </p>\n        </div>\n    );\n};\n\nexport default AboutPage;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACpB,oBACID,OAAA;IAAKE,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC5BH,OAAA;MAAAG,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjBP,OAAA;MAAAG,QAAA,EAAG;IAEH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEd,CAAC;AAACC,EAAA,GATIP,SAAS;AAWf,eAAeA,SAAS;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}