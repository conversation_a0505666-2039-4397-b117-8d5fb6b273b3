{"ast": null, "code": "var isProduction = process.env.NODE_ENV === 'production';\nfunction warning(condition, message) {\n  if (!isProduction) {\n    if (condition) {\n      return;\n    }\n    var text = \"Warning: \" + message;\n    if (typeof console !== 'undefined') {\n      console.warn(text);\n    }\n    try {\n      throw Error(text);\n    } catch (x) {}\n  }\n}\nexport default warning;", "map": {"version": 3, "names": ["isProduction", "process", "env", "NODE_ENV", "warning", "condition", "message", "text", "console", "warn", "Error", "x"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/tiny-warning/dist/tiny-warning.esm.js"], "sourcesContent": ["var isProduction = process.env.NODE_ENV === 'production';\nfunction warning(condition, message) {\n  if (!isProduction) {\n    if (condition) {\n      return;\n    }\n\n    var text = \"Warning: \" + message;\n\n    if (typeof console !== 'undefined') {\n      console.warn(text);\n    }\n\n    try {\n      throw Error(text);\n    } catch (x) {}\n  }\n}\n\nexport default warning;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACxD,SAASC,OAAOA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACnC,IAAI,CAACN,YAAY,EAAE;IACjB,IAAIK,SAAS,EAAE;MACb;IACF;IAEA,IAAIE,IAAI,GAAG,WAAW,GAAGD,OAAO;IAEhC,IAAI,OAAOE,OAAO,KAAK,WAAW,EAAE;MAClCA,OAAO,CAACC,IAAI,CAACF,IAAI,CAAC;IACpB;IAEA,IAAI;MACF,MAAMG,KAAK,CAACH,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOI,CAAC,EAAE,CAAC;EACf;AACF;AAEA,eAAeP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}