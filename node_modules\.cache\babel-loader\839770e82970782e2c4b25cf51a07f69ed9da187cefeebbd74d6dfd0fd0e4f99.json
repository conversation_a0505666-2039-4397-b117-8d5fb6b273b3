{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport PageItem, { Ellipsis, First, Last, Next, Prev } from './PageItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Pagination = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  size,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'pagination');\n  return /*#__PURE__*/_jsx(\"ul\", {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, size && `${decoratedBsPrefix}-${size}`)\n  });\n});\nPagination.displayName = 'Pagination';\nexport default Object.assign(Pagination, {\n  First,\n  Prev,\n  Ellipsis,\n  Item: PageItem,\n  Next,\n  Last\n});", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "PageItem", "El<PERSON><PERSON>", "First", "Last", "Next", "Prev", "jsx", "_jsx", "Pagination", "forwardRef", "bsPrefix", "className", "size", "props", "ref", "decoratedBsPrefix", "displayName", "Object", "assign", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/Pagination.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport PageItem, { Ellipsis, First, Last, Next, Prev } from './PageItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Pagination = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  size,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'pagination');\n  return /*#__PURE__*/_jsx(\"ul\", {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, size && `${decoratedBsPrefix}-${size}`)\n  });\n});\nPagination.displayName = 'Pagination';\nexport default Object.assign(Pagination, {\n  First,\n  Prev,\n  Ellipsis,\n  Item: PageItem,\n  Next,\n  Last\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,QAAQ,IAAIC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,QAAQ,YAAY;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,CAAC;EAChDC,QAAQ;EACRC,SAAS;EACTC,IAAI;EACJ,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,iBAAiB,GAAGhB,kBAAkB,CAACW,QAAQ,EAAE,YAAY,CAAC;EACpE,OAAO,aAAaH,IAAI,CAAC,IAAI,EAAE;IAC7BO,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRF,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAEI,iBAAiB,EAAEH,IAAI,IAAI,GAAGG,iBAAiB,IAAIH,IAAI,EAAE;EAC5F,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,UAAU,CAACQ,WAAW,GAAG,YAAY;AACrC,eAAeC,MAAM,CAACC,MAAM,CAACV,UAAU,EAAE;EACvCN,KAAK;EACLG,IAAI;EACJJ,QAAQ;EACRkB,IAAI,EAAEnB,QAAQ;EACdI,IAAI;EACJD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}