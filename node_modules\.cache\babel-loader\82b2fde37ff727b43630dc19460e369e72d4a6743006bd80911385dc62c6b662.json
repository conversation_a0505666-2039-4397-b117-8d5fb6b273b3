{"ast": null, "code": "import useUpdatedRef from './useUpdatedRef';\nimport { useEffect } from 'react';\n\n/**\n * Attach a callback that fires when a component unmounts\n *\n * @param fn Handler to run when the component unmounts\n * @category effects\n */\nexport default function useWillUnmount(fn) {\n  const onUnmount = useUpdatedRef(fn);\n  useEffect(() => () => onUnmount.current(), []);\n}", "map": {"version": 3, "names": ["useUpdatedRef", "useEffect", "useWillUnmount", "fn", "onUnmount", "current"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/@restart/hooks/esm/useWillUnmount.js"], "sourcesContent": ["import useUpdatedRef from './useUpdatedRef';\nimport { useEffect } from 'react';\n\n/**\n * Attach a callback that fires when a component unmounts\n *\n * @param fn Handler to run when the component unmounts\n * @category effects\n */\nexport default function useWillUnmount(fn) {\n  const onUnmount = useUpdatedRef(fn);\n  useEffect(() => () => onUnmount.current(), []);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,SAASC,SAAS,QAAQ,OAAO;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,EAAE,EAAE;EACzC,MAAMC,SAAS,GAAGJ,aAAa,CAACG,EAAE,CAAC;EACnCF,SAAS,CAAC,MAAM,MAAMG,SAAS,CAACC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}