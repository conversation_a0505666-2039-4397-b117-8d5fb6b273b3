{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\Projects.js\";\nimport React from 'react';\nimport { Container, Row, Col, <PERSON>, Button } from 'react-bootstrap';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProjectsSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({\n  theme\n}) => theme.background};\n  padding: 100px 0;\n`;\n_c = ProjectsSection;\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({\n  theme\n}) => theme.color};\n  \n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({\n  theme\n}) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n_c2 = SectionTitle;\nconst ProjectCard = styled(Card)`\n  background: ${({\n  theme\n}) => theme.cardBackground};\n  border: 1px solid ${({\n  theme\n}) => theme.cardBorderColor};\n  border-radius: 15px;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  height: 100%;\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  }\n`;\n_c3 = ProjectCard;\nconst ProjectTitle = styled(Card.Title)`\n  color: ${({\n  theme\n}) => theme.color};\n  font-weight: 600;\n`;\n_c4 = ProjectTitle;\nconst ProjectText = styled(Card.Text)`\n  color: ${({\n  theme\n}) => theme.color};\n  opacity: 0.8;\n`;\n_c5 = ProjectText;\nconst Projects = () => {\n  // Sample project data\n  const sampleProjects = [{\n    title: \"Personal E-Portfolio\",\n    description: \"A responsive portfolio website built with React, featuring dark mode, smooth animations, and modern design.\",\n    technologies: [\"React\", \"Bootstrap\", \"Styled Components\"],\n    links: [{\n      text: \"Live Demo\",\n      href: \"#\"\n    }, {\n      text: \"GitHub\",\n      href: \"#\"\n    }]\n  }, {\n    title: \"Learning Tracker\",\n    description: \"An application to track daily learning outcomes and progress in various technologies and skills.\",\n    technologies: [\"JavaScript\", \"HTML\", \"CSS\"],\n    links: [{\n      text: \"GitHub\",\n      href: \"#\"\n    }]\n  }];\n  return /*#__PURE__*/_jsxDEV(ProjectsSection, {\n    id: \"projects\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"Projects\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: sampleProjects.map((project, index) => /*#__PURE__*/_jsxDEV(Col, {\n          lg: 6,\n          md: 6,\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(ProjectCard, {\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(ProjectTitle, {\n                children: project.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ProjectText, {\n                children: project.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: project.technologies.map((tech, techIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-primary me-2 mb-2\",\n                  children: tech\n                }, techIndex, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: project.links.map((link, linkIndex) => /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-primary\",\n                  size: \"sm\",\n                  className: \"me-2\",\n                  href: link.href,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: link.text\n                }, linkIndex, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_c6 = Projects;\nexport default Projects;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"ProjectsSection\");\n$RefreshReg$(_c2, \"SectionTitle\");\n$RefreshReg$(_c3, \"ProjectCard\");\n$RefreshReg$(_c4, \"ProjectTitle\");\n$RefreshReg$(_c5, \"ProjectText\");\n$RefreshReg$(_c6, \"Projects\");", "map": {"version": 3, "names": ["React", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "styled", "jsxDEV", "_jsxDEV", "ProjectsSection", "section", "theme", "background", "_c", "SectionTitle", "h2", "color", "accentColor", "_c2", "ProjectCard", "cardBackground", "cardBorderColor", "_c3", "ProjectTitle", "Title", "_c4", "ProjectText", "Text", "_c5", "Projects", "sampleProjects", "title", "description", "technologies", "links", "text", "href", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "project", "index", "lg", "md", "className", "Body", "tech", "techIndex", "link", "linkIndex", "variant", "size", "target", "rel", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/Projects.js"], "sourcesContent": ["import React from 'react';\nimport { Container, <PERSON>, <PERSON>, <PERSON>, Button } from 'react-bootstrap';\nimport styled from 'styled-components';\n\nconst ProjectsSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({ theme }) => theme.background};\n  padding: 100px 0;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({ theme }) => theme.color};\n  \n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({ theme }) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n\nconst ProjectCard = styled(Card)`\n  background: ${({ theme }) => theme.cardBackground};\n  border: 1px solid ${({ theme }) => theme.cardBorderColor};\n  border-radius: 15px;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  height: 100%;\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  }\n`;\n\nconst ProjectTitle = styled(Card.Title)`\n  color: ${({ theme }) => theme.color};\n  font-weight: 600;\n`;\n\nconst ProjectText = styled(Card.Text)`\n  color: ${({ theme }) => theme.color};\n  opacity: 0.8;\n`;\n\nconst Projects = () => {\n  // Sample project data\n  const sampleProjects = [\n    {\n      title: \"Personal E-Portfolio\",\n      description: \"A responsive portfolio website built with React, featuring dark mode, smooth animations, and modern design.\",\n      technologies: [\"React\", \"Bootstrap\", \"Styled Components\"],\n      links: [\n        { text: \"Live Demo\", href: \"#\" },\n        { text: \"GitHub\", href: \"#\" }\n      ]\n    },\n    {\n      title: \"Learning Tracker\",\n      description: \"An application to track daily learning outcomes and progress in various technologies and skills.\",\n      technologies: [\"JavaScript\", \"HTML\", \"CSS\"],\n      links: [\n        { text: \"GitHub\", href: \"#\" }\n      ]\n    }\n  ];\n\n  return (\n    <ProjectsSection id=\"projects\">\n      <Container>\n        <SectionTitle>Projects</SectionTitle>\n        <Row>\n          {sampleProjects.map((project, index) => (\n            <Col lg={6} md={6} key={index} className=\"mb-4\">\n              <ProjectCard>\n                <Card.Body>\n                  <ProjectTitle>{project.title}</ProjectTitle>\n                  <ProjectText>{project.description}</ProjectText>\n                  <div className=\"mb-3\">\n                    {project.technologies.map((tech, techIndex) => (\n                      <span\n                        key={techIndex}\n                        className=\"badge bg-primary me-2 mb-2\"\n                      >\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                  <div>\n                    {project.links.map((link, linkIndex) => (\n                      <Button\n                        key={linkIndex}\n                        variant=\"outline-primary\"\n                        size=\"sm\"\n                        className=\"me-2\"\n                        href={link.href}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                      >\n                        {link.text}\n                      </Button>\n                    ))}\n                  </div>\n                </Card.Body>\n              </ProjectCard>\n            </Col>\n          ))}\n        </Row>\n      </Container>\n    </ProjectsSection>\n  );\n};\n\nexport default Projects;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,QAAQ,iBAAiB;AACnE,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,eAAe,GAAGH,MAAM,CAACI,OAAO;AACtC;AACA;AACA;AACA,gBAAgB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,UAAU;AAC/C;AACA,CAAC;AAACC,EAAA,GANIJ,eAAe;AAQrB,MAAMK,YAAY,GAAGR,MAAM,CAACS,EAAE;AAC9B;AACA;AACA;AACA;AACA,WAAW,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,WAAW;AAClD;AACA;AACA,CAAC;AAACC,GAAA,GAfIJ,YAAY;AAiBlB,MAAMK,WAAW,GAAGb,MAAM,CAACF,IAAI,CAAC;AAChC,gBAAgB,CAAC;EAAEO;AAAM,CAAC,KAAKA,KAAK,CAACS,cAAc;AACnD,sBAAsB,CAAC;EAAET;AAAM,CAAC,KAAKA,KAAK,CAACU,eAAe;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIH,WAAW;AAajB,MAAMI,YAAY,GAAGjB,MAAM,CAACF,IAAI,CAACoB,KAAK,CAAC;AACvC,WAAW,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA,CAAC;AAACS,GAAA,GAHIF,YAAY;AAKlB,MAAMG,WAAW,GAAGpB,MAAM,CAACF,IAAI,CAACuB,IAAI,CAAC;AACrC,WAAW,CAAC;EAAEhB;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA,CAAC;AAACY,GAAA,GAHIF,WAAW;AAKjB,MAAMG,QAAQ,GAAGA,CAAA,KAAM;EACrB;EACA,MAAMC,cAAc,GAAG,CACrB;IACEC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,6GAA6G;IAC1HC,YAAY,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,mBAAmB,CAAC;IACzDC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAI,CAAC,EAChC;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAC;EAEjC,CAAC,EACD;IACEL,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,kGAAkG;IAC/GC,YAAY,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC;IAC3CC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAC;EAEjC,CAAC,CACF;EAED,oBACE5B,OAAA,CAACC,eAAe;IAAC4B,EAAE,EAAC,UAAU;IAAAC,QAAA,eAC5B9B,OAAA,CAACP,SAAS;MAAAqC,QAAA,gBACR9B,OAAA,CAACM,YAAY;QAAAwB,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACrClC,OAAA,CAACN,GAAG;QAAAoC,QAAA,EACDR,cAAc,CAACa,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACjCrC,OAAA,CAACL,GAAG;UAAC2C,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAaC,SAAS,EAAC,MAAM;UAAAV,QAAA,eAC7C9B,OAAA,CAACW,WAAW;YAAAmB,QAAA,eACV9B,OAAA,CAACJ,IAAI,CAAC6C,IAAI;cAAAX,QAAA,gBACR9B,OAAA,CAACe,YAAY;gBAAAe,QAAA,EAAEM,OAAO,CAACb;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAC5ClC,OAAA,CAACkB,WAAW;gBAAAY,QAAA,EAAEM,OAAO,CAACZ;cAAW;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAAC,eAChDlC,OAAA;gBAAKwC,SAAS,EAAC,MAAM;gBAAAV,QAAA,EAClBM,OAAO,CAACX,YAAY,CAACU,GAAG,CAAC,CAACO,IAAI,EAAEC,SAAS,kBACxC3C,OAAA;kBAEEwC,SAAS,EAAC,4BAA4B;kBAAAV,QAAA,EAErCY;gBAAI,GAHAC,SAAS;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIV,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlC,OAAA;gBAAA8B,QAAA,EACGM,OAAO,CAACV,KAAK,CAACS,GAAG,CAAC,CAACS,IAAI,EAAEC,SAAS,kBACjC7C,OAAA,CAACH,MAAM;kBAELiD,OAAO,EAAC,iBAAiB;kBACzBC,IAAI,EAAC,IAAI;kBACTP,SAAS,EAAC,MAAM;kBAChBZ,IAAI,EAAEgB,IAAI,CAAChB,IAAK;kBAChBoB,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBAAAnB,QAAA,EAExBc,IAAI,CAACjB;gBAAI,GARLkB,SAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASR,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GA/BQG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCxB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAACgB,GAAA,GAlEI7B,QAAQ;AAoEd,eAAeA,QAAQ;AAAC,IAAAhB,EAAA,EAAAK,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAA8B,GAAA;AAAAC,YAAA,CAAA9C,EAAA;AAAA8C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}