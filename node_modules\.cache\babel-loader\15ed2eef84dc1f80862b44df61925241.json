{"ast": null, "code": "import _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport { createMemoryHistory, createLocation, locationsAreEqual, createPath } from 'history';\nimport warning from 'tiny-warning';\nimport invariant from 'tiny-invariant';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport pathToRegexp from 'path-to-regexp';\nimport { isValidElementType } from 'react-is';\nimport _objectWithoutPropertiesLoose from '@babel/runtime/helpers/esm/objectWithoutPropertiesLoose';\nimport hoistStatics from 'hoist-non-react-statics';\nvar MAX_SIGNED_31_BIT_INT = **********;\nvar commonjsGlobal = typeof globalThis !== \"undefined\" // 'global proper'\n?\n// eslint-disable-next-line no-undef\nglobalThis : typeof window !== \"undefined\" ? window // Browser\n: typeof global !== \"undefined\" ? global // node.js\n: {};\nfunction getUniqueId() {\n  var key = \"__global_unique_id__\";\n  return commonjsGlobal[key] = (commonjsGlobal[key] || 0) + 1;\n} // Inlined Object.is polyfill.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    // eslint-disable-next-line no-self-compare\n    return x !== x && y !== y;\n  }\n}\nfunction createEventEmitter(value) {\n  var handlers = [];\n  return {\n    on: function on(handler) {\n      handlers.push(handler);\n    },\n    off: function off(handler) {\n      handlers = handlers.filter(function (h) {\n        return h !== handler;\n      });\n    },\n    get: function get() {\n      return value;\n    },\n    set: function set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(function (handler) {\n        return handler(value, changedBits);\n      });\n    }\n  };\n}\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\nfunction createReactContext(defaultValue, calculateChangedBits) {\n  var _Provider$childContex, _Consumer$contextType;\n  var contextProp = \"__create-react-context-\" + getUniqueId() + \"__\";\n  var Provider = /*#__PURE__*/function (_React$Component) {\n    _inheritsLoose(Provider, _React$Component);\n    function Provider() {\n      var _this;\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n      _this.emitter = createEventEmitter(_this.props.value);\n      return _this;\n    }\n    var _proto = Provider.prototype;\n    _proto.getChildContext = function getChildContext() {\n      var _ref;\n      return _ref = {}, _ref[contextProp] = this.emitter, _ref;\n    };\n    _proto.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        var oldValue = this.props.value;\n        var newValue = nextProps.value;\n        var changedBits;\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0; // No change\n        } else {\n          changedBits = typeof calculateChangedBits === \"function\" ? calculateChangedBits(oldValue, newValue) : MAX_SIGNED_31_BIT_INT;\n          if (process.env.NODE_ENV !== \"production\") {\n            process.env.NODE_ENV !== \"production\" ? warning((changedBits & MAX_SIGNED_31_BIT_INT) === changedBits, \"calculateChangedBits: Expected the return value to be a \" + \"31-bit integer. Instead received: \" + changedBits) : void 0;\n          }\n          changedBits |= 0;\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    };\n    _proto.render = function render() {\n      return this.props.children;\n    };\n    return Provider;\n  }(React.Component);\n  Provider.childContextTypes = (_Provider$childContex = {}, _Provider$childContex[contextProp] = PropTypes.object.isRequired, _Provider$childContex);\n  var Consumer = /*#__PURE__*/function (_React$Component2) {\n    _inheritsLoose(Consumer, _React$Component2);\n    function Consumer() {\n      var _this2;\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      _this2 = _React$Component2.call.apply(_React$Component2, [this].concat(args)) || this;\n      _this2.observedBits = void 0;\n      _this2.state = {\n        value: _this2.getValue()\n      };\n      _this2.onUpdate = function (newValue, changedBits) {\n        var observedBits = _this2.observedBits | 0;\n        if ((observedBits & changedBits) !== 0) {\n          _this2.setState({\n            value: _this2.getValue()\n          });\n        }\n      };\n      return _this2;\n    }\n    var _proto2 = Consumer.prototype;\n    _proto2.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      var observedBits = nextProps.observedBits;\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n      : observedBits;\n    };\n    _proto2.componentDidMount = function componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n      var observedBits = this.props.observedBits;\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n      : observedBits;\n    };\n    _proto2.componentWillUnmount = function componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    };\n    _proto2.getValue = function getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    };\n    _proto2.render = function render() {\n      return onlyChild(this.props.children)(this.state.value);\n    };\n    return Consumer;\n  }(React.Component);\n  Consumer.contextTypes = (_Consumer$contextType = {}, _Consumer$contextType[contextProp] = PropTypes.object, _Consumer$contextType);\n  return {\n    Provider: Provider,\n    Consumer: Consumer\n  };\n}\n\n// MIT License\nvar createContext = React.createContext || createReactContext;\n\n// TODO: Replace with React.createContext once we can assume React 16+\n\nvar createNamedContext = function createNamedContext(name) {\n  var context = createContext();\n  context.displayName = name;\n  return context;\n};\nvar historyContext = /*#__PURE__*/createNamedContext(\"Router-History\");\nvar context = /*#__PURE__*/createNamedContext(\"Router\");\n\n/**\n * The public API for putting history on context.\n */\n\nvar Router = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Router, _React$Component);\n  Router.computeRootMatch = function computeRootMatch(pathname) {\n    return {\n      path: \"/\",\n      url: \"/\",\n      params: {},\n      isExact: pathname === \"/\"\n    };\n  };\n  function Router(props) {\n    var _this;\n    _this = _React$Component.call(this, props) || this;\n    _this.state = {\n      location: props.history.location\n    }; // This is a bit of a hack. We have to start listening for location\n    // changes here in the constructor in case there are any <Redirect>s\n    // on the initial render. If there are, they will replace/push when\n    // they mount and since cDM fires in children before parents, we may\n    // get a new location before the <Router> is mounted.\n\n    _this._isMounted = false;\n    _this._pendingLocation = null;\n    if (!props.staticContext) {\n      _this.unlisten = props.history.listen(function (location) {\n        _this._pendingLocation = location;\n      });\n    }\n    return _this;\n  }\n  var _proto = Router.prototype;\n  _proto.componentDidMount = function componentDidMount() {\n    var _this2 = this;\n    this._isMounted = true;\n    if (this.unlisten) {\n      // Any pre-mount location changes have been captured at\n      // this point, so unregister the listener.\n      this.unlisten();\n    }\n    if (!this.props.staticContext) {\n      this.unlisten = this.props.history.listen(function (location) {\n        if (_this2._isMounted) {\n          _this2.setState({\n            location: location\n          });\n        }\n      });\n    }\n    if (this._pendingLocation) {\n      this.setState({\n        location: this._pendingLocation\n      });\n    }\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    if (this.unlisten) {\n      this.unlisten();\n      this._isMounted = false;\n      this._pendingLocation = null;\n    }\n  };\n  _proto.render = function render() {\n    return /*#__PURE__*/React.createElement(context.Provider, {\n      value: {\n        history: this.props.history,\n        location: this.state.location,\n        match: Router.computeRootMatch(this.state.location.pathname),\n        staticContext: this.props.staticContext\n      }\n    }, /*#__PURE__*/React.createElement(historyContext.Provider, {\n      children: this.props.children || null,\n      value: this.props.history\n    }));\n  };\n  return Router;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  Router.propTypes = {\n    children: PropTypes.node,\n    history: PropTypes.object.isRequired,\n    staticContext: PropTypes.object\n  };\n  Router.prototype.componentDidUpdate = function (prevProps) {\n    process.env.NODE_ENV !== \"production\" ? warning(prevProps.history === this.props.history, \"You cannot change <Router history>\") : void 0;\n  };\n}\n\n/**\n * The public API for a <Router> that stores location in memory.\n */\n\nvar MemoryRouter = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(MemoryRouter, _React$Component);\n  function MemoryRouter() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.history = createMemoryHistory(_this.props);\n    return _this;\n  }\n  var _proto = MemoryRouter.prototype;\n  _proto.render = function render() {\n    return /*#__PURE__*/React.createElement(Router, {\n      history: this.history,\n      children: this.props.children\n    });\n  };\n  return MemoryRouter;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  MemoryRouter.propTypes = {\n    initialEntries: PropTypes.array,\n    initialIndex: PropTypes.number,\n    getUserConfirmation: PropTypes.func,\n    keyLength: PropTypes.number,\n    children: PropTypes.node\n  };\n  MemoryRouter.prototype.componentDidMount = function () {\n    process.env.NODE_ENV !== \"production\" ? warning(!this.props.history, \"<MemoryRouter> ignores the history prop. To use a custom history, \" + \"use `import { Router }` instead of `import { MemoryRouter as Router }`.\") : void 0;\n  };\n}\nvar Lifecycle = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Lifecycle, _React$Component);\n  function Lifecycle() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n  var _proto = Lifecycle.prototype;\n  _proto.componentDidMount = function componentDidMount() {\n    if (this.props.onMount) this.props.onMount.call(this, this);\n  };\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.onUpdate) this.props.onUpdate.call(this, this, prevProps);\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    if (this.props.onUnmount) this.props.onUnmount.call(this, this);\n  };\n  _proto.render = function render() {\n    return null;\n  };\n  return Lifecycle;\n}(React.Component);\n\n/**\n * The public API for prompting the user before navigating away from a screen.\n */\n\nfunction Prompt(_ref) {\n  var message = _ref.message,\n    _ref$when = _ref.when,\n    when = _ref$when === void 0 ? true : _ref$when;\n  return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n    !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Prompt> outside a <Router>\") : invariant(false) : void 0;\n    if (!when || context.staticContext) return null;\n    var method = context.history.block;\n    return /*#__PURE__*/React.createElement(Lifecycle, {\n      onMount: function onMount(self) {\n        self.release = method(message);\n      },\n      onUpdate: function onUpdate(self, prevProps) {\n        if (prevProps.message !== message) {\n          self.release();\n          self.release = method(message);\n        }\n      },\n      onUnmount: function onUnmount(self) {\n        self.release();\n      },\n      message: message\n    });\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  var messageType = PropTypes.oneOfType([PropTypes.func, PropTypes.string]);\n  Prompt.propTypes = {\n    when: PropTypes.bool,\n    message: messageType.isRequired\n  };\n}\nvar cache = {};\nvar cacheLimit = 10000;\nvar cacheCount = 0;\nfunction compilePath(path) {\n  if (cache[path]) return cache[path];\n  var generator = pathToRegexp.compile(path);\n  if (cacheCount < cacheLimit) {\n    cache[path] = generator;\n    cacheCount++;\n  }\n  return generator;\n}\n/**\n * Public API for generating a URL pathname from a path and parameters.\n */\n\nfunction generatePath(path, params) {\n  if (path === void 0) {\n    path = \"/\";\n  }\n  if (params === void 0) {\n    params = {};\n  }\n  return path === \"/\" ? path : compilePath(path)(params, {\n    pretty: true\n  });\n}\n\n/**\n * The public API for navigating programmatically with a component.\n */\n\nfunction Redirect(_ref) {\n  var computedMatch = _ref.computedMatch,\n    to = _ref.to,\n    _ref$push = _ref.push,\n    push = _ref$push === void 0 ? false : _ref$push;\n  return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n    !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Redirect> outside a <Router>\") : invariant(false) : void 0;\n    var history = context.history,\n      staticContext = context.staticContext;\n    var method = push ? history.push : history.replace;\n    var location = createLocation(computedMatch ? typeof to === \"string\" ? generatePath(to, computedMatch.params) : _extends({}, to, {\n      pathname: generatePath(to.pathname, computedMatch.params)\n    }) : to); // When rendering in a static context,\n    // set the new location immediately.\n\n    if (staticContext) {\n      method(location);\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(Lifecycle, {\n      onMount: function onMount() {\n        method(location);\n      },\n      onUpdate: function onUpdate(self, prevProps) {\n        var prevLocation = createLocation(prevProps.to);\n        if (!locationsAreEqual(prevLocation, _extends({}, location, {\n          key: prevLocation.key\n        }))) {\n          method(location);\n        }\n      },\n      to: to\n    });\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  Redirect.propTypes = {\n    push: PropTypes.bool,\n    from: PropTypes.string,\n    to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired\n  };\n}\nvar cache$1 = {};\nvar cacheLimit$1 = 10000;\nvar cacheCount$1 = 0;\nfunction compilePath$1(path, options) {\n  var cacheKey = \"\" + options.end + options.strict + options.sensitive;\n  var pathCache = cache$1[cacheKey] || (cache$1[cacheKey] = {});\n  if (pathCache[path]) return pathCache[path];\n  var keys = [];\n  var regexp = pathToRegexp(path, keys, options);\n  var result = {\n    regexp: regexp,\n    keys: keys\n  };\n  if (cacheCount$1 < cacheLimit$1) {\n    pathCache[path] = result;\n    cacheCount$1++;\n  }\n  return result;\n}\n/**\n * Public API for matching a URL pathname to a path.\n */\n\nfunction matchPath(pathname, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (typeof options === \"string\" || Array.isArray(options)) {\n    options = {\n      path: options\n    };\n  }\n  var _options = options,\n    path = _options.path,\n    _options$exact = _options.exact,\n    exact = _options$exact === void 0 ? false : _options$exact,\n    _options$strict = _options.strict,\n    strict = _options$strict === void 0 ? false : _options$strict,\n    _options$sensitive = _options.sensitive,\n    sensitive = _options$sensitive === void 0 ? false : _options$sensitive;\n  var paths = [].concat(path);\n  return paths.reduce(function (matched, path) {\n    if (!path && path !== \"\") return null;\n    if (matched) return matched;\n    var _compilePath = compilePath$1(path, {\n        end: exact,\n        strict: strict,\n        sensitive: sensitive\n      }),\n      regexp = _compilePath.regexp,\n      keys = _compilePath.keys;\n    var match = regexp.exec(pathname);\n    if (!match) return null;\n    var url = match[0],\n      values = match.slice(1);\n    var isExact = pathname === url;\n    if (exact && !isExact) return null;\n    return {\n      path: path,\n      // the path used to match\n      url: path === \"/\" && url === \"\" ? \"/\" : url,\n      // the matched portion of the URL\n      isExact: isExact,\n      // whether or not we matched exactly\n      params: keys.reduce(function (memo, key, index) {\n        memo[key.name] = values[index];\n        return memo;\n      }, {})\n    };\n  }, null);\n}\nfunction isEmptyChildren(children) {\n  return React.Children.count(children) === 0;\n}\nfunction evalChildrenDev(children, props, path) {\n  var value = children(props);\n  process.env.NODE_ENV !== \"production\" ? warning(value !== undefined, \"You returned `undefined` from the `children` function of \" + (\"<Route\" + (path ? \" path=\\\"\" + path + \"\\\"\" : \"\") + \">, but you \") + \"should have returned a React element or `null`\") : void 0;\n  return value || null;\n}\n/**\n * The public API for matching a single path and rendering.\n */\n\nvar Route = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Route, _React$Component);\n  function Route() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n  var _proto = Route.prototype;\n  _proto.render = function render() {\n    var _this = this;\n    return /*#__PURE__*/React.createElement(context.Consumer, null, function (context$1) {\n      !context$1 ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Route> outside a <Router>\") : invariant(false) : void 0;\n      var location = _this.props.location || context$1.location;\n      var match = _this.props.computedMatch ? _this.props.computedMatch // <Switch> already computed the match for us\n      : _this.props.path ? matchPath(location.pathname, _this.props) : context$1.match;\n      var props = _extends({}, context$1, {\n        location: location,\n        match: match\n      });\n      var _this$props = _this.props,\n        children = _this$props.children,\n        component = _this$props.component,\n        render = _this$props.render; // Preact uses an empty array as children by\n      // default, so use null if that's the case.\n\n      if (Array.isArray(children) && isEmptyChildren(children)) {\n        children = null;\n      }\n      return /*#__PURE__*/React.createElement(context.Provider, {\n        value: props\n      }, props.match ? children ? typeof children === \"function\" ? process.env.NODE_ENV !== \"production\" ? evalChildrenDev(children, props, _this.props.path) : children(props) : children : component ? /*#__PURE__*/React.createElement(component, props) : render ? render(props) : null : typeof children === \"function\" ? process.env.NODE_ENV !== \"production\" ? evalChildrenDev(children, props, _this.props.path) : children(props) : null);\n    });\n  };\n  return Route;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  Route.propTypes = {\n    children: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),\n    component: function component(props, propName) {\n      if (props[propName] && !isValidElementType(props[propName])) {\n        return new Error(\"Invalid prop 'component' supplied to 'Route': the prop is not a valid React component\");\n      }\n    },\n    exact: PropTypes.bool,\n    location: PropTypes.object,\n    path: PropTypes.oneOfType([PropTypes.string, PropTypes.arrayOf(PropTypes.string)]),\n    render: PropTypes.func,\n    sensitive: PropTypes.bool,\n    strict: PropTypes.bool\n  };\n  Route.prototype.componentDidMount = function () {\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.children && !isEmptyChildren(this.props.children) && this.props.component), \"You should not use <Route component> and <Route children> in the same route; <Route component> will be ignored\") : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.children && !isEmptyChildren(this.props.children) && this.props.render), \"You should not use <Route render> and <Route children> in the same route; <Route render> will be ignored\") : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.component && this.props.render), \"You should not use <Route component> and <Route render> in the same route; <Route render> will be ignored\") : void 0;\n  };\n  Route.prototype.componentDidUpdate = function (prevProps) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.location && !prevProps.location), '<Route> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(!this.props.location && prevProps.location), '<Route> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.') : void 0;\n  };\n}\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === \"/\" ? path : \"/\" + path;\n}\nfunction addBasename(basename, location) {\n  if (!basename) return location;\n  return _extends({}, location, {\n    pathname: addLeadingSlash(basename) + location.pathname\n  });\n}\nfunction stripBasename(basename, location) {\n  if (!basename) return location;\n  var base = addLeadingSlash(basename);\n  if (location.pathname.indexOf(base) !== 0) return location;\n  return _extends({}, location, {\n    pathname: location.pathname.substr(base.length)\n  });\n}\nfunction createURL(location) {\n  return typeof location === \"string\" ? location : createPath(location);\n}\nfunction staticHandler(methodName) {\n  return function () {\n    process.env.NODE_ENV !== \"production\" ? invariant(false, \"You cannot %s with <StaticRouter>\", methodName) : invariant(false);\n  };\n}\nfunction noop() {}\n/**\n * The public top-level API for a \"static\" <Router>, so-called because it\n * can't actually change the current location. Instead, it just records\n * location changes in a context object. Useful mainly in testing and\n * server-rendering scenarios.\n */\n\nvar StaticRouter = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(StaticRouter, _React$Component);\n  function StaticRouter() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.handlePush = function (location) {\n      return _this.navigateTo(location, \"PUSH\");\n    };\n    _this.handleReplace = function (location) {\n      return _this.navigateTo(location, \"REPLACE\");\n    };\n    _this.handleListen = function () {\n      return noop;\n    };\n    _this.handleBlock = function () {\n      return noop;\n    };\n    return _this;\n  }\n  var _proto = StaticRouter.prototype;\n  _proto.navigateTo = function navigateTo(location, action) {\n    var _this$props = this.props,\n      _this$props$basename = _this$props.basename,\n      basename = _this$props$basename === void 0 ? \"\" : _this$props$basename,\n      _this$props$context = _this$props.context,\n      context = _this$props$context === void 0 ? {} : _this$props$context;\n    context.action = action;\n    context.location = addBasename(basename, createLocation(location));\n    context.url = createURL(context.location);\n  };\n  _proto.render = function render() {\n    var _this$props2 = this.props,\n      _this$props2$basename = _this$props2.basename,\n      basename = _this$props2$basename === void 0 ? \"\" : _this$props2$basename,\n      _this$props2$context = _this$props2.context,\n      context = _this$props2$context === void 0 ? {} : _this$props2$context,\n      _this$props2$location = _this$props2.location,\n      location = _this$props2$location === void 0 ? \"/\" : _this$props2$location,\n      rest = _objectWithoutPropertiesLoose(_this$props2, [\"basename\", \"context\", \"location\"]);\n    var history = {\n      createHref: function createHref(path) {\n        return addLeadingSlash(basename + createURL(path));\n      },\n      action: \"POP\",\n      location: stripBasename(basename, createLocation(location)),\n      push: this.handlePush,\n      replace: this.handleReplace,\n      go: staticHandler(\"go\"),\n      goBack: staticHandler(\"goBack\"),\n      goForward: staticHandler(\"goForward\"),\n      listen: this.handleListen,\n      block: this.handleBlock\n    };\n    return /*#__PURE__*/React.createElement(Router, _extends({}, rest, {\n      history: history,\n      staticContext: context\n    }));\n  };\n  return StaticRouter;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  StaticRouter.propTypes = {\n    basename: PropTypes.string,\n    context: PropTypes.object,\n    location: PropTypes.oneOfType([PropTypes.string, PropTypes.object])\n  };\n  StaticRouter.prototype.componentDidMount = function () {\n    process.env.NODE_ENV !== \"production\" ? warning(!this.props.history, \"<StaticRouter> ignores the history prop. To use a custom history, \" + \"use `import { Router }` instead of `import { StaticRouter as Router }`.\") : void 0;\n  };\n}\n\n/**\n * The public API for rendering the first <Route> that matches.\n */\n\nvar Switch = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Switch, _React$Component);\n  function Switch() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n  var _proto = Switch.prototype;\n  _proto.render = function render() {\n    var _this = this;\n    return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n      !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <Switch> outside a <Router>\") : invariant(false) : void 0;\n      var location = _this.props.location || context.location;\n      var element, match; // We use React.Children.forEach instead of React.Children.toArray().find()\n      // here because toArray adds keys to all child elements and we do not want\n      // to trigger an unmount/remount for two <Route>s that render the same\n      // component at different URLs.\n\n      React.Children.forEach(_this.props.children, function (child) {\n        if (match == null && /*#__PURE__*/React.isValidElement(child)) {\n          element = child;\n          var path = child.props.path || child.props.from;\n          match = path ? matchPath(location.pathname, _extends({}, child.props, {\n            path: path\n          })) : context.match;\n        }\n      });\n      return match ? /*#__PURE__*/React.cloneElement(element, {\n        location: location,\n        computedMatch: match\n      }) : null;\n    });\n  };\n  return Switch;\n}(React.Component);\nif (process.env.NODE_ENV !== \"production\") {\n  Switch.propTypes = {\n    children: PropTypes.node,\n    location: PropTypes.object\n  };\n  Switch.prototype.componentDidUpdate = function (prevProps) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(this.props.location && !prevProps.location), '<Switch> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!(!this.props.location && prevProps.location), '<Switch> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.') : void 0;\n  };\n}\n\n/**\n * A public higher-order component to access the imperative API\n */\n\nfunction withRouter(Component) {\n  var displayName = \"withRouter(\" + (Component.displayName || Component.name) + \")\";\n  var C = function C(props) {\n    var wrappedComponentRef = props.wrappedComponentRef,\n      remainingProps = _objectWithoutPropertiesLoose(props, [\"wrappedComponentRef\"]);\n    return /*#__PURE__*/React.createElement(context.Consumer, null, function (context) {\n      !context ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You should not use <\" + displayName + \" /> outside a <Router>\") : invariant(false) : void 0;\n      return /*#__PURE__*/React.createElement(Component, _extends({}, remainingProps, context, {\n        ref: wrappedComponentRef\n      }));\n    });\n  };\n  C.displayName = displayName;\n  C.WrappedComponent = Component;\n  if (process.env.NODE_ENV !== \"production\") {\n    C.propTypes = {\n      wrappedComponentRef: PropTypes.oneOfType([PropTypes.string, PropTypes.func, PropTypes.object])\n    };\n  }\n  return hoistStatics(C, Component);\n}\nvar useContext = React.useContext;\nfunction useHistory() {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useHistory()\") : invariant(false) : void 0;\n  }\n  return useContext(historyContext);\n}\nfunction useLocation() {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useLocation()\") : invariant(false) : void 0;\n  }\n  return useContext(context).location;\n}\nfunction useParams() {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useParams()\") : invariant(false) : void 0;\n  }\n  var match = useContext(context).match;\n  return match ? match.params : {};\n}\nfunction useRouteMatch(path) {\n  if (process.env.NODE_ENV !== \"production\") {\n    !(typeof useContext === \"function\") ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You must use React >= 16.8 in order to use useRouteMatch()\") : invariant(false) : void 0;\n  }\n  var location = useLocation();\n  var match = useContext(context).match;\n  return path ? matchPath(location.pathname, path) : match;\n}\nif (process.env.NODE_ENV !== \"production\") {\n  if (typeof window !== \"undefined\") {\n    var global$1 = window;\n    var key = \"__react_router_build__\";\n    var buildNames = {\n      cjs: \"CommonJS\",\n      esm: \"ES modules\",\n      umd: \"UMD\"\n    };\n    if (global$1[key] && global$1[key] !== \"esm\") {\n      var initialBuildName = buildNames[global$1[key]];\n      var secondaryBuildName = buildNames[\"esm\"]; // TODO: Add link to article that explains in detail how to avoid\n      // loading 2 different builds.\n\n      throw new Error(\"You are loading the \" + secondaryBuildName + \" build of React Router \" + (\"on a page that is already running the \" + initialBuildName + \" \") + \"build, so things won't work right.\");\n    }\n    global$1[key] = \"esm\";\n  }\n}\nexport { MemoryRouter, Prompt, Redirect, Route, Router, StaticRouter, Switch, historyContext as __HistoryContext, context as __RouterContext, generatePath, matchPath, useHistory, useLocation, useParams, useRouteMatch, withRouter };", "map": {"version": 3, "names": ["MAX_SIGNED_31_BIT_INT", "commonjsGlobal", "globalThis", "window", "global", "getUniqueId", "key", "objectIs", "x", "y", "createEventEmitter", "value", "handlers", "on", "handler", "push", "off", "filter", "h", "get", "set", "newValue", "changedBits", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "children", "Array", "isArray", "createReactContext", "defaultValue", "calculateChangedBits", "contextProp", "Provider", "_React$Component", "emitter", "_this", "props", "getChildContext", "_ref", "componentWillReceiveProps", "nextProps", "oldValue", "process", "env", "NODE_ENV", "warning", "render", "React", "Component", "childContextTypes", "_Provider$childContex", "PropTypes", "object", "isRequired", "Consumer", "_React$Component2", "observedBits", "state", "_this2", "getValue", "onUpdate", "setState", "undefined", "componentDidMount", "context", "componentWillUnmount", "contextTypes", "_Consumer$contextType", "createContext", "createNamedContext", "name", "displayName", "historyContext", "Router", "computeRootMatch", "pathname", "path", "url", "params", "isExact", "location", "history", "_isMounted", "_pendingLocation", "staticContext", "unlisten", "listen", "createElement", "match", "propTypes", "node", "prototype", "componentDidUpdate", "prevProps", "MemoryRouter", "createMemoryHistory", "initialEntries", "array", "initialIndex", "number", "getUserConfirmation", "func", "<PERSON><PERSON><PERSON><PERSON>", "Lifecycle", "onMount", "call", "onUnmount", "Prompt", "message", "when", "_ref$when", "invariant", "method", "block", "self", "release", "messageType", "oneOfType", "string", "bool", "cache", "cacheLimit", "cacheCount", "compilePath", "generator", "pathToRegexp", "compile", "generatePath", "pretty", "Redirect", "computedMatch", "to", "_ref$push", "replace", "createLocation", "_extends", "prevLocation", "locationsAreEqual", "from", "cache$1", "cacheLimit$1", "cacheCount$1", "compilePath$1", "options", "cache<PERSON>ey", "end", "strict", "sensitive", "pathCache", "keys", "regexp", "result", "matchPath", "_options", "exact", "_options$exact", "_options$strict", "_options$sensitive", "paths", "concat", "reduce", "matched", "_compilePath", "exec", "values", "slice", "memo", "index", "isEmptyChildren", "Children", "count", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Route", "context$1", "_this$props", "component", "propName", "isValidElementType", "Error", "arrayOf", "addLeadingSlash", "char<PERSON>t", "addBasename", "basename", "stripBasename", "base", "indexOf", "substr", "length", "createURL", "createPath", "static<PERSON><PERSON><PERSON>", "methodName", "noop", "StaticRouter", "handlePush", "navigateTo", "handleReplace", "handleListen", "handleBlock", "action", "_this$props$basename", "_this$props$context", "_this$props2$basename", "_this$props2$context", "_this$props2$location", "rest", "_objectWithoutPropertiesLoose", "_this$props2", "createHref", "go", "goBack", "goForward", "Switch", "element", "child", "isValidElement", "cloneElement", "with<PERSON><PERSON><PERSON>", "C", "wrappedComponentRef", "remainingProps", "ref", "WrappedComponent", "hoistStatics", "useContext", "useHistory", "useLocation", "useParams", "useRouteMatch", "global$1", "buildNames", "cjs", "esm", "umd", "initialBuildName", "secondaryBuildName"], "sources": ["../modules/miniCreateReactContext.js", "../modules/createContext.js", "../modules/createNamedContext.js", "../modules/HistoryContext.js", "../modules/RouterContext.js", "../modules/Router.js", "../modules/MemoryRouter.js", "../modules/Lifecycle.js", "../modules/Prompt.js", "../modules/generatePath.js", "../modules/Redirect.js", "../modules/matchPath.js", "../modules/Route.js", "../modules/StaticRouter.js", "../modules/Switch.js", "../modules/withRouter.js", "../modules/hooks.js", "../modules/index.js"], "sourcesContent": ["// MIT License\n// Copyright (c) 2019-present StringEpsilon <<EMAIL>>\n// Copyright (c) 2017-2019 <PERSON> <<EMAIL>>\n// https://github.com/StringEpsilon/mini-create-react-context\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport warning from \"tiny-warning\";\n\nconst MAX_SIGNED_31_BIT_INT = **********;\n\nconst commonjsGlobal =\n  typeof globalThis !== \"undefined\" // 'global proper'\n    ? // eslint-disable-next-line no-undef\n      globalThis\n    : typeof window !== \"undefined\"\n    ? window // Browser\n    : typeof global !== \"undefined\"\n    ? global // node.js\n    : {};\n\nfunction getUniqueId() {\n  let key = \"__global_unique_id__\";\n  return (commonjsGlobal[key] = (commonjsGlobal[key] || 0) + 1);\n}\n\n// Inlined Object.is polyfill.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    // eslint-disable-next-line no-self-compare\n    return x !== x && y !== y;\n  }\n}\n\nfunction createEventEmitter(value) {\n  let handlers = [];\n  return {\n    on(handler) {\n      handlers.push(handler);\n    },\n\n    off(handler) {\n      handlers = handlers.filter(h => h !== handler);\n    },\n\n    get() {\n      return value;\n    },\n\n    set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(handler => handler(value, changedBits));\n    }\n  };\n}\n\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\n\nexport default function createReactContext(defaultValue, calculateChangedBits) {\n  const contextProp = \"__create-react-context-\" + getUniqueId() + \"__\";\n\n  class Provider extends React.Component {\n    emitter = createEventEmitter(this.props.value);\n\n    static childContextTypes = {\n      [contextProp]: PropTypes.object.isRequired\n    };\n\n    getChildContext() {\n      return {\n        [contextProp]: this.emitter\n      };\n    }\n\n    componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        let oldValue = this.props.value;\n        let newValue = nextProps.value;\n        let changedBits;\n\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0; // No change\n        } else {\n          changedBits =\n            typeof calculateChangedBits === \"function\"\n              ? calculateChangedBits(oldValue, newValue)\n              : MAX_SIGNED_31_BIT_INT;\n          if (process.env.NODE_ENV !== \"production\") {\n            warning(\n              (changedBits & MAX_SIGNED_31_BIT_INT) === changedBits,\n              \"calculateChangedBits: Expected the return value to be a \" +\n                \"31-bit integer. Instead received: \" +\n                changedBits\n            );\n          }\n\n          changedBits |= 0;\n\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    }\n\n    render() {\n      return this.props.children;\n    }\n  }\n\n  class Consumer extends React.Component {\n    static contextTypes = {\n      [contextProp]: PropTypes.object\n    };\n\n    observedBits;\n\n    state = {\n      value: this.getValue()\n    };\n\n    componentWillReceiveProps(nextProps) {\n      let { observedBits } = nextProps;\n      this.observedBits =\n        observedBits === undefined || observedBits === null\n          ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n          : observedBits;\n    }\n\n    componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n      let { observedBits } = this.props;\n      this.observedBits =\n        observedBits === undefined || observedBits === null\n          ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n          : observedBits;\n    }\n\n    componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    }\n\n    getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    }\n\n    onUpdate = (newValue, changedBits) => {\n      const observedBits = this.observedBits | 0;\n      if ((observedBits & changedBits) !== 0) {\n        this.setState({ value: this.getValue() });\n      }\n    };\n\n    render() {\n      return onlyChild(this.props.children)(this.state.value);\n    }\n  }\n\n  return {\n    Provider,\n    Consumer\n  };\n}\n", "// MIT License\n// Copyright (c) 2019-present StringEpsilon <<EMAIL>>\n// Copyright (c) 2017-2019 <PERSON> <<EMAIL>>\n// https://github.com/StringEpsilon/mini-create-react-context\nimport React from \"react\";\nimport createReactContext from \"./miniCreateReactContext\";\n\nexport default React.createContext || createReactContext;\n", "// TODO: Replace with React.createContext once we can assume React 16+\nimport createContext from \"./createContext\";\n\nconst createNamedContext = name => {\n  const context = createContext();\n  context.displayName = name;\n\n  return context;\n};\n\nexport default createNamedContext;\n", "import createNamedContext from \"./createNamedContext\";\n\nconst historyContext = /*#__PURE__*/ createNamedContext(\"Router-History\");\nexport default historyContext;\n", "import createNamedContext from \"./createNamedContext\";\n\nconst context = /*#__PURE__*/ createNamedContext(\"Router\");\nexport default context;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport warning from \"tiny-warning\";\n\nimport HistoryContext from \"./HistoryContext.js\";\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * The public API for putting history on context.\n */\nclass Router extends React.Component {\n  static computeRootMatch(pathname) {\n    return { path: \"/\", url: \"/\", params: {}, isExact: pathname === \"/\" };\n  }\n\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      location: props.history.location\n    };\n\n    // This is a bit of a hack. We have to start listening for location\n    // changes here in the constructor in case there are any <Redirect>s\n    // on the initial render. If there are, they will replace/push when\n    // they mount and since cDM fires in children before parents, we may\n    // get a new location before the <Router> is mounted.\n    this._isMounted = false;\n    this._pendingLocation = null;\n\n    if (!props.staticContext) {\n      this.unlisten = props.history.listen(location => {\n        this._pendingLocation = location;\n      });\n    }\n  }\n\n  componentDidMount() {\n    this._isMounted = true;\n\n    if (this.unlisten) {\n      // Any pre-mount location changes have been captured at\n      // this point, so unregister the listener.\n      this.unlisten();\n    }\n    if (!this.props.staticContext) {\n      this.unlisten = this.props.history.listen(location => {\n        if (this._isMounted) {\n          this.setState({ location });\n        }\n      });\n    }\n    if (this._pendingLocation) {\n      this.setState({ location: this._pendingLocation });\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.unlisten) {\n      this.unlisten();\n      this._isMounted = false;\n      this._pendingLocation = null;\n    }\n  }\n\n  render() {\n    return (\n      <RouterContext.Provider\n        value={{\n          history: this.props.history,\n          location: this.state.location,\n          match: Router.computeRootMatch(this.state.location.pathname),\n          staticContext: this.props.staticContext\n        }}\n      >\n        <HistoryContext.Provider\n          children={this.props.children || null}\n          value={this.props.history}\n        />\n      </RouterContext.Provider>\n    );\n  }\n}\n\nif (__DEV__) {\n  Router.propTypes = {\n    children: PropTypes.node,\n    history: PropTypes.object.isRequired,\n    staticContext: PropTypes.object\n  };\n\n  Router.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      prevProps.history === this.props.history,\n      \"You cannot change <Router history>\"\n    );\n  };\n}\n\nexport default Router;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createMemoryHistory as createHistory } from \"history\";\nimport warning from \"tiny-warning\";\n\nimport Router from \"./Router.js\";\n\n/**\n * The public API for a <Router> that stores location in memory.\n */\nclass MemoryRouter extends React.Component {\n  history = createHistory(this.props);\n\n  render() {\n    return <Router history={this.history} children={this.props.children} />;\n  }\n}\n\nif (__DEV__) {\n  MemoryRouter.propTypes = {\n    initialEntries: PropTypes.array,\n    initialIndex: PropTypes.number,\n    getUserConfirmation: PropTypes.func,\n    keyLength: PropTypes.number,\n    children: PropTypes.node\n  };\n\n  MemoryRouter.prototype.componentDidMount = function() {\n    warning(\n      !this.props.history,\n      \"<MemoryRouter> ignores the history prop. To use a custom history, \" +\n        \"use `import { Router }` instead of `import { MemoryRouter as Router }`.\"\n    );\n  };\n}\n\nexport default MemoryRouter;\n", "import React from \"react\";\n\nclass Lifecycle extends React.Component {\n  componentDidMount() {\n    if (this.props.onMount) this.props.onMount.call(this, this);\n  }\n\n  componentDidUpdate(prevProps) {\n    if (this.props.onUpdate) this.props.onUpdate.call(this, this, prevProps);\n  }\n\n  componentWillUnmount() {\n    if (this.props.onUnmount) this.props.onUnmount.call(this, this);\n  }\n\n  render() {\n    return null;\n  }\n}\n\nexport default Lifecycle;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\n\nimport Lifecycle from \"./Lifecycle.js\";\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * The public API for prompting the user before navigating away from a screen.\n */\nfunction Prompt({ message, when = true }) {\n  return (\n    <RouterContext.Consumer>\n      {context => {\n        invariant(context, \"You should not use <Prompt> outside a <Router>\");\n\n        if (!when || context.staticContext) return null;\n\n        const method = context.history.block;\n\n        return (\n          <Lifecycle\n            onMount={self => {\n              self.release = method(message);\n            }}\n            onUpdate={(self, prevProps) => {\n              if (prevProps.message !== message) {\n                self.release();\n                self.release = method(message);\n              }\n            }}\n            onUnmount={self => {\n              self.release();\n            }}\n            message={message}\n          />\n        );\n      }}\n    </RouterContext.Consumer>\n  );\n}\n\nif (__DEV__) {\n  const messageType = PropTypes.oneOfType([PropTypes.func, PropTypes.string]);\n\n  Prompt.propTypes = {\n    when: PropTypes.bool,\n    message: messageType.isRequired\n  };\n}\n\nexport default Prompt;\n", "import pathToRegexp from \"path-to-regexp\";\n\nconst cache = {};\nconst cacheLimit = 10000;\nlet cacheCount = 0;\n\nfunction compilePath(path) {\n  if (cache[path]) return cache[path];\n\n  const generator = pathToRegexp.compile(path);\n\n  if (cacheCount < cacheLimit) {\n    cache[path] = generator;\n    cacheCount++;\n  }\n\n  return generator;\n}\n\n/**\n * Public API for generating a URL pathname from a path and parameters.\n */\nfunction generatePath(path = \"/\", params = {}) {\n  return path === \"/\" ? path : compilePath(path)(params, { pretty: true });\n}\n\nexport default generatePath;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createLocation, locationsAreEqual } from \"history\";\nimport invariant from \"tiny-invariant\";\n\nimport Lifecycle from \"./Lifecycle.js\";\nimport RouterContext from \"./RouterContext.js\";\nimport generatePath from \"./generatePath.js\";\n\n/**\n * The public API for navigating programmatically with a component.\n */\nfunction Redirect({ computedMatch, to, push = false }) {\n  return (\n    <RouterContext.Consumer>\n      {context => {\n        invariant(context, \"You should not use <Redirect> outside a <Router>\");\n\n        const { history, staticContext } = context;\n\n        const method = push ? history.push : history.replace;\n        const location = createLocation(\n          computedMatch\n            ? typeof to === \"string\"\n              ? generatePath(to, computedMatch.params)\n              : {\n                  ...to,\n                  pathname: generatePath(to.pathname, computedMatch.params)\n                }\n            : to\n        );\n\n        // When rendering in a static context,\n        // set the new location immediately.\n        if (staticContext) {\n          method(location);\n          return null;\n        }\n\n        return (\n          <Lifecycle\n            onMount={() => {\n              method(location);\n            }}\n            onUpdate={(self, prevProps) => {\n              const prevLocation = createLocation(prevProps.to);\n              if (\n                !locationsAreEqual(prevLocation, {\n                  ...location,\n                  key: prevLocation.key\n                })\n              ) {\n                method(location);\n              }\n            }}\n            to={to}\n          />\n        );\n      }}\n    </RouterContext.Consumer>\n  );\n}\n\nif (__DEV__) {\n  Redirect.propTypes = {\n    push: PropTypes.bool,\n    from: PropTypes.string,\n    to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired\n  };\n}\n\nexport default Redirect;\n", "import pathToRegexp from \"path-to-regexp\";\n\nconst cache = {};\nconst cacheLimit = 10000;\nlet cacheCount = 0;\n\nfunction compilePath(path, options) {\n  const cacheKey = `${options.end}${options.strict}${options.sensitive}`;\n  const pathCache = cache[cacheKey] || (cache[cacheKey] = {});\n\n  if (pathCache[path]) return pathCache[path];\n\n  const keys = [];\n  const regexp = pathToRegexp(path, keys, options);\n  const result = { regexp, keys };\n\n  if (cacheCount < cacheLimit) {\n    pathCache[path] = result;\n    cacheCount++;\n  }\n\n  return result;\n}\n\n/**\n * Public API for matching a URL pathname to a path.\n */\nfunction matchPath(pathname, options = {}) {\n  if (typeof options === \"string\" || Array.isArray(options)) {\n    options = { path: options };\n  }\n\n  const { path, exact = false, strict = false, sensitive = false } = options;\n\n  const paths = [].concat(path);\n\n  return paths.reduce((matched, path) => {\n    if (!path && path !== \"\") return null;\n    if (matched) return matched;\n\n    const { regexp, keys } = compilePath(path, {\n      end: exact,\n      strict,\n      sensitive\n    });\n    const match = regexp.exec(pathname);\n\n    if (!match) return null;\n\n    const [url, ...values] = match;\n    const isExact = pathname === url;\n\n    if (exact && !isExact) return null;\n\n    return {\n      path, // the path used to match\n      url: path === \"/\" && url === \"\" ? \"/\" : url, // the matched portion of the URL\n      isExact, // whether or not we matched exactly\n      params: keys.reduce((memo, key, index) => {\n        memo[key.name] = values[index];\n        return memo;\n      }, {})\n    };\n  }, null);\n}\n\nexport default matchPath;\n", "import React from \"react\";\nimport { isValidElementType } from \"react-is\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport matchPath from \"./matchPath.js\";\n\nfunction isEmptyChildren(children) {\n  return React.Children.count(children) === 0;\n}\n\nfunction evalChildrenDev(children, props, path) {\n  const value = children(props);\n\n  warning(\n    value !== undefined,\n    \"You returned `undefined` from the `children` function of \" +\n      `<Route${path ? ` path=\"${path}\"` : \"\"}>, but you ` +\n      \"should have returned a React element or `null`\"\n  );\n\n  return value || null;\n}\n\n/**\n * The public API for matching a single path and rendering.\n */\nclass Route extends React.Component {\n  render() {\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(context, \"You should not use <Route> outside a <Router>\");\n\n          const location = this.props.location || context.location;\n          const match = this.props.computedMatch\n            ? this.props.computedMatch // <Switch> already computed the match for us\n            : this.props.path\n            ? matchPath(location.pathname, this.props)\n            : context.match;\n\n          const props = { ...context, location, match };\n\n          let { children, component, render } = this.props;\n\n          // Preact uses an empty array as children by\n          // default, so use null if that's the case.\n          if (Array.isArray(children) && isEmptyChildren(children)) {\n            children = null;\n          }\n\n          return (\n            <RouterContext.Provider value={props}>\n              {props.match\n                ? children\n                  ? typeof children === \"function\"\n                    ? __DEV__\n                      ? evalChildrenDev(children, props, this.props.path)\n                      : children(props)\n                    : children\n                  : component\n                  ? React.createElement(component, props)\n                  : render\n                  ? render(props)\n                  : null\n                : typeof children === \"function\"\n                ? __DEV__\n                  ? evalChildrenDev(children, props, this.props.path)\n                  : children(props)\n                : null}\n            </RouterContext.Provider>\n          );\n        }}\n      </RouterContext.Consumer>\n    );\n  }\n}\n\nif (__DEV__) {\n  Route.propTypes = {\n    children: PropTypes.oneOfType([PropTypes.func, PropTypes.node]),\n    component: (props, propName) => {\n      if (props[propName] && !isValidElementType(props[propName])) {\n        return new Error(\n          `Invalid prop 'component' supplied to 'Route': the prop is not a valid React component`\n        );\n      }\n    },\n    exact: PropTypes.bool,\n    location: PropTypes.object,\n    path: PropTypes.oneOfType([\n      PropTypes.string,\n      PropTypes.arrayOf(PropTypes.string)\n    ]),\n    render: PropTypes.func,\n    sensitive: PropTypes.bool,\n    strict: PropTypes.bool\n  };\n\n  Route.prototype.componentDidMount = function() {\n    warning(\n      !(\n        this.props.children &&\n        !isEmptyChildren(this.props.children) &&\n        this.props.component\n      ),\n      \"You should not use <Route component> and <Route children> in the same route; <Route component> will be ignored\"\n    );\n\n    warning(\n      !(\n        this.props.children &&\n        !isEmptyChildren(this.props.children) &&\n        this.props.render\n      ),\n      \"You should not use <Route render> and <Route children> in the same route; <Route render> will be ignored\"\n    );\n\n    warning(\n      !(this.props.component && this.props.render),\n      \"You should not use <Route component> and <Route render> in the same route; <Route render> will be ignored\"\n    );\n  };\n\n  Route.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      !(this.props.location && !prevProps.location),\n      '<Route> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.'\n    );\n\n    warning(\n      !(!this.props.location && prevProps.location),\n      '<Route> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.'\n    );\n  };\n}\n\nexport default Route;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { createLocation, createPath } from \"history\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport Router from \"./Router.js\";\n\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === \"/\" ? path : \"/\" + path;\n}\n\nfunction addBasename(basename, location) {\n  if (!basename) return location;\n\n  return {\n    ...location,\n    pathname: addLeadingSlash(basename) + location.pathname\n  };\n}\n\nfunction stripBasename(basename, location) {\n  if (!basename) return location;\n\n  const base = addLeadingSlash(basename);\n\n  if (location.pathname.indexOf(base) !== 0) return location;\n\n  return {\n    ...location,\n    pathname: location.pathname.substr(base.length)\n  };\n}\n\nfunction createURL(location) {\n  return typeof location === \"string\" ? location : createPath(location);\n}\n\nfunction staticHandler(methodName) {\n  return () => {\n    invariant(false, \"You cannot %s with <StaticRouter>\", methodName);\n  };\n}\n\nfunction noop() {}\n\n/**\n * The public top-level API for a \"static\" <Router>, so-called because it\n * can't actually change the current location. Instead, it just records\n * location changes in a context object. Useful mainly in testing and\n * server-rendering scenarios.\n */\nclass StaticRouter extends React.Component {\n  navigateTo(location, action) {\n    const { basename = \"\", context = {} } = this.props;\n    context.action = action;\n    context.location = addBasename(basename, createLocation(location));\n    context.url = createURL(context.location);\n  }\n\n  handlePush = location => this.navigateTo(location, \"PUSH\");\n  handleReplace = location => this.navigateTo(location, \"REPLACE\");\n  handleListen = () => noop;\n  handleBlock = () => noop;\n\n  render() {\n    const { basename = \"\", context = {}, location = \"/\", ...rest } = this.props;\n\n    const history = {\n      createHref: path => addLeadingSlash(basename + createURL(path)),\n      action: \"POP\",\n      location: stripBasename(basename, createLocation(location)),\n      push: this.handlePush,\n      replace: this.handleReplace,\n      go: staticHandler(\"go\"),\n      goBack: staticHandler(\"goBack\"),\n      goForward: staticHandler(\"goForward\"),\n      listen: this.handleListen,\n      block: this.handleBlock\n    };\n\n    return <Router {...rest} history={history} staticContext={context} />;\n  }\n}\n\nif (__DEV__) {\n  StaticRouter.propTypes = {\n    basename: PropTypes.string,\n    context: PropTypes.object,\n    location: PropTypes.oneOfType([PropTypes.string, PropTypes.object])\n  };\n\n  StaticRouter.prototype.componentDidMount = function() {\n    warning(\n      !this.props.history,\n      \"<StaticRouter> ignores the history prop. To use a custom history, \" +\n        \"use `import { Router }` instead of `import { StaticRouter as Router }`.\"\n    );\n  };\n}\n\nexport default StaticRouter;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport invariant from \"tiny-invariant\";\nimport warning from \"tiny-warning\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport matchPath from \"./matchPath.js\";\n\n/**\n * The public API for rendering the first <Route> that matches.\n */\nclass Switch extends React.Component {\n  render() {\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(context, \"You should not use <Switch> outside a <Router>\");\n\n          const location = this.props.location || context.location;\n\n          let element, match;\n\n          // We use React.Children.forEach instead of React.Children.toArray().find()\n          // here because toArray adds keys to all child elements and we do not want\n          // to trigger an unmount/remount for two <Route>s that render the same\n          // component at different URLs.\n          React.Children.forEach(this.props.children, child => {\n            if (match == null && React.isValidElement(child)) {\n              element = child;\n\n              const path = child.props.path || child.props.from;\n\n              match = path\n                ? matchPath(location.pathname, { ...child.props, path })\n                : context.match;\n            }\n          });\n\n          return match\n            ? React.cloneElement(element, { location, computedMatch: match })\n            : null;\n        }}\n      </RouterContext.Consumer>\n    );\n  }\n}\n\nif (__DEV__) {\n  Switch.propTypes = {\n    children: PropTypes.node,\n    location: PropTypes.object\n  };\n\n  Switch.prototype.componentDidUpdate = function(prevProps) {\n    warning(\n      !(this.props.location && !prevProps.location),\n      '<Switch> elements should not change from uncontrolled to controlled (or vice versa). You initially used no \"location\" prop and then provided one on a subsequent render.'\n    );\n\n    warning(\n      !(!this.props.location && prevProps.location),\n      '<Switch> elements should not change from controlled to uncontrolled (or vice versa). You provided a \"location\" prop initially but omitted it on a subsequent render.'\n    );\n  };\n}\n\nexport default Switch;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport hoistStatics from \"hoist-non-react-statics\";\nimport invariant from \"tiny-invariant\";\n\nimport RouterContext from \"./RouterContext.js\";\n\n/**\n * A public higher-order component to access the imperative API\n */\nfunction withRouter(Component) {\n  const displayName = `withRouter(${Component.displayName || Component.name})`;\n  const C = props => {\n    const { wrappedComponentRef, ...remainingProps } = props;\n\n    return (\n      <RouterContext.Consumer>\n        {context => {\n          invariant(\n            context,\n            `You should not use <${displayName} /> outside a <Router>`\n          );\n          return (\n            <Component\n              {...remainingProps}\n              {...context}\n              ref={wrappedComponentRef}\n            />\n          );\n        }}\n      </RouterContext.Consumer>\n    );\n  };\n\n  C.displayName = displayName;\n  C.WrappedComponent = Component;\n\n  if (__DEV__) {\n    C.propTypes = {\n      wrappedComponentRef: PropTypes.oneOfType([\n        PropTypes.string,\n        PropTypes.func,\n        PropTypes.object\n      ])\n    };\n  }\n\n  return hoistStatics(C, Component);\n}\n\nexport default withRouter;\n", "import React from \"react\";\nimport invariant from \"tiny-invariant\";\n\nimport RouterContext from \"./RouterContext.js\";\nimport HistoryContext from \"./HistoryContext.js\";\nimport matchPath from \"./matchPath.js\";\n\nconst useContext = React.useContext;\n\nexport function useHistory() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useHistory()\"\n    );\n  }\n\n  return useContext(HistoryContext);\n}\n\nexport function useLocation() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useLocation()\"\n    );\n  }\n\n  return useContext(RouterContext).location;\n}\n\nexport function useParams() {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useParams()\"\n    );\n  }\n\n  const match = useContext(RouterContext).match;\n  return match ? match.params : {};\n}\n\nexport function useRouteMatch(path) {\n  if (__DEV__) {\n    invariant(\n      typeof useContext === \"function\",\n      \"You must use React >= 16.8 in order to use useRouteMatch()\"\n    );\n  }\n\n  const location = useLocation();\n  const match = useContext(RouterContext).match;\n  return path ? matchPath(location.pathname, path) : match;\n}\n", "if (__DEV__) {\n  if (typeof window !== \"undefined\") {\n    const global = window;\n    const key = \"__react_router_build__\";\n    const buildNames = { cjs: \"CommonJS\", esm: \"ES modules\", umd: \"UMD\" };\n\n    if (global[key] && global[key] !== process.env.BUILD_FORMAT) {\n      const initialBuildName = buildNames[global[key]];\n      const secondaryBuildName = buildNames[process.env.BUILD_FORMAT];\n\n      // TODO: Add link to article that explains in detail how to avoid\n      // loading 2 different builds.\n      throw new Error(\n        `You are loading the ${secondaryBuildName} build of React Router ` +\n          `on a page that is already running the ${initialBuildName} ` +\n          `build, so things won't work right.`\n      );\n    }\n\n    global[key] = process.env.BUILD_FORMAT;\n  }\n}\n\nexport { default as MemoryRouter } from \"./MemoryRouter.js\";\nexport { default as Prompt } from \"./Prompt.js\";\nexport { default as Redirect } from \"./Redirect.js\";\nexport { default as Route } from \"./Route.js\";\nexport { default as Router } from \"./Router.js\";\nexport { default as StaticRouter } from \"./StaticRouter.js\";\nexport { default as Switch } from \"./Switch.js\";\nexport { default as generatePath } from \"./generatePath.js\";\nexport { default as matchPath } from \"./matchPath.js\";\nexport { default as withRouter } from \"./withRouter.js\";\n\nexport { default as __HistoryContext } from \"./HistoryContext.js\";\nexport { default as __RouterContext } from \"./RouterContext.js\";\n\nexport { useHistory, useLocation, useParams, useRouteMatch } from \"./hooks.js\";\n"], "mappings": ";;;;;;;;;;;AAQA,IAAMA,qBAAqB,GAAG,UAA9B;AAEA,IAAMC,cAAc,GAClB,OAAOC,UAAP,KAAsB,WAAtB;AAAA;;AAEIA,UAFJ,GAGI,OAAOC,MAAP,KAAkB,WAAlB,GACAA,MADA;AAAA,EAEA,OAAOC,MAAP,KAAkB,WAAlB,GACAA,MADA;AAAA,EAEA,EARN;AAUA,SAASC,WAATA,CAAA,EAAuB;MACjBC,GAAG,GAAG,sBAAV;SACQL,cAAc,CAACK,GAAD,CAAd,GAAsB,CAACL,cAAc,CAACK,GAAD,CAAd,IAAuB,CAAxB,IAA6B,CAA3D;;;;AAKF,SAASC,QAATA,CAAkBC,CAAlB,EAAqBC,CAArB,EAAwB;MAClBD,CAAC,KAAKC,CAAV,EAAa;WACJD,CAAC,KAAK,CAAN,IAAW,IAAIA,CAAJ,KAAU,IAAIC,CAAhC;GADF,MAEO;;WAEED,CAAC,KAAKA,CAAN,IAAWC,CAAC,KAAKA,CAAxB;;;AAIJ,SAASC,kBAATA,CAA4BC,KAA5B,EAAmC;MAC7BC,QAAQ,GAAG,EAAf;SACO;IACLC,EADK,WAAAA,GACFC,OADE,EACO;MACVF,QAAQ,CAACG,IAAT,CAAcD,OAAd;KAFG;IAKLE,GALK,WAAAA,IAKDF,OALC,EAKQ;MACXF,QAAQ,GAAGA,QAAQ,CAACK,MAAT,CAAgB,UAAAC,CAAC;eAAIA,CAAC,KAAKJ,OAAV;OAAjB,CAAX;KANG;IASLK,GATK,WAAAA,IAAA,EASC;aACGR,KAAP;KAVG;IAaLS,GAbK,WAAAA,IAaDC,QAbC,EAaSC,WAbT,EAasB;MACzBX,KAAK,GAAGU,QAAR;MACAT,QAAQ,CAACW,OAAT,CAAiB,UAAAT,OAAO;eAAIA,OAAO,CAACH,KAAD,EAAQW,WAAR,CAAX;OAAxB;;GAfJ;;AAoBF,SAASE,SAATA,CAAmBC,QAAnB,EAA6B;SACpBC,KAAK,CAACC,OAAN,CAAcF,QAAd,IAA0BA,QAAQ,CAAC,CAAD,CAAlC,GAAwCA,QAA/C;;AAGF,SAAwBG,kBAATA,CAA4BC,YAA5B,EAA0CC,oBAA1C,EAAgE;;MACvEC,WAAW,GAAG,4BAA4B1B,WAAW,EAAvC,GAA4C,IAAhE;MAEM2B,QAHuE,0BAAAC,gBAAA;;;;;;;;YAI3EC,OAJ2E,GAIjExB,kBAAkB,CAACyB,KAAA,CAAKC,KAAL,CAAWzB,KAAZ,CAJ+C;;;;WAU3E0B,eAV2E,GAU3E,SAAAA,gBAAA,EAAkB;;6BAEbN,WADH,IACiB,KAAKG,OADtB,EAAAI,IAAA;KAXyE;WAgB3EC,yBAhB2E,GAgB3E,SAAAA,0BAA0BC,SAA1B,EAAqC;UAC/B,KAAKJ,KAAL,CAAWzB,KAAX,KAAqB6B,SAAS,CAAC7B,KAAnC,EAA0C;YACpC8B,QAAQ,GAAG,KAAKL,KAAL,CAAWzB,KAA1B;YACIU,QAAQ,GAAGmB,SAAS,CAAC7B,KAAzB;YACIW,WAAJ;YAEIf,QAAQ,CAACkC,QAAD,EAAWpB,QAAX,CAAZ,EAAkC;UAChCC,WAAW,GAAG,CAAd,CADgC;SAAlC,MAEO;UACLA,WAAW,GACT,OAAOQ,oBAAP,KAAgC,UAAhC,GACIA,oBAAoB,CAACW,QAAD,EAAWpB,QAAX,CADxB,GAEIrB,qBAHN;cAII0C,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;oDACzCC,OAAO,CACL,CAACvB,WAAW,GAAGtB,qBAAf,MAA0CsB,WADrC,EAEL,6DACE,oCADF,GAEEA,WAJG,CAAP;;UAQFA,WAAW,IAAI,CAAf;cAEIA,WAAW,KAAK,CAApB,EAAuB;iBAChBY,OAAL,CAAad,GAAb,CAAiBoB,SAAS,CAAC7B,KAA3B,EAAkCW,WAAlC;;;;KAzCmE;WA+C3EwB,MA/C2E,GA+C3E,SAAAA,OAAA,EAAS;aACA,KAAKV,KAAL,CAAWX,QAAlB;KAhDyE;;IAGtDsB,KAAK,CAACC,SAHgD;EAGvEhB,QAHuE,CAMpEiB,iBANoE,IAAAC,qBAAA,OAAAA,qBAAA,CAOxEnB,WAPwE,IAO1DoB,SAAS,CAACC,MAAV,CAAiBC,UAPyC,EAAAH,qBAAA;MAoDvEI,QApDuE,0BAAAC,iBAAA;;;;;;;;aAyD3EC,YAzD2E;aA2D3EC,KA3D2E,GA2DnE;QACN9C,KAAK,EAAE+C,MAAA,CAAKC,QAAL;OA5DkE;aAgG3EC,QAhG2E,GAgGhE,UAACvC,QAAD,EAAWC,WAAX,EAA2B;YAC9BkC,YAAY,GAAGE,MAAA,CAAKF,YAAL,GAAoB,CAAzC;YACI,CAACA,YAAY,GAAGlC,WAAhB,MAAiC,CAArC,EAAwC;iBACjCuC,QAAL,CAAc;YAAElD,KAAK,EAAE+C,MAAA,CAAKC,QAAL;WAAvB;;OAnGuE;;;;YA+D3EpB,yBA/D2E,GA+D3E,SAAAA,0BAA0BC,SAA1B,EAAqC;UAC7BgB,YAD6B,GACZhB,SADY,CAC7BgB,YAD6B;WAE9BA,YAAL,GACEA,YAAY,KAAKM,SAAjB,IAA8BN,YAAY,KAAK,IAA/C,GACIxD,qBADJ;MAAA,EAEIwD,YAHN;KAjEyE;YAuE3EO,iBAvE2E,GAuE3E,SAAAA,kBAAA,EAAoB;UACd,KAAKC,OAAL,CAAajC,WAAb,CAAJ,EAA+B;aACxBiC,OAAL,CAAajC,WAAb,EAA0BlB,EAA1B,CAA6B,KAAK+C,QAAlC;;UAEIJ,YAJY,GAIK,KAAKpB,KAJV,CAIZoB,YAJY;WAKbA,YAAL,GACEA,YAAY,KAAKM,SAAjB,IAA8BN,YAAY,KAAK,IAA/C,GACIxD,qBADJ;MAAA,EAEIwD,YAHN;KA5EyE;YAkF3ES,oBAlF2E,GAkF3E,SAAAA,qBAAA,EAAuB;UACjB,KAAKD,OAAL,CAAajC,WAAb,CAAJ,EAA+B;aACxBiC,OAAL,CAAajC,WAAb,EAA0Bf,GAA1B,CAA8B,KAAK4C,QAAnC;;KApFuE;YAwF3ED,QAxF2E,GAwF3E,SAAAA,SAAA,EAAW;UACL,KAAKK,OAAL,CAAajC,WAAb,CAAJ,EAA+B;eACtB,KAAKiC,OAAL,CAAajC,WAAb,EAA0BZ,GAA1B,EAAP;OADF,MAEO;eACEU,YAAP;;KA5FuE;YAuG3EiB,MAvG2E,GAuG3E,SAAAA,OAAA,EAAS;aACAtB,SAAS,CAAC,KAAKY,KAAL,CAAWX,QAAZ,CAAT,CAA+B,KAAKgC,KAAL,CAAW9C,KAA1C,CAAP;KAxGyE;;IAoDtDoC,KAAK,CAACC,SApDgD;EAoDvEM,QApDuE,CAqDpEY,YArDoE,IAAAC,qBAAA,OAAAA,qBAAA,CAsDxEpC,WAtDwE,IAsD1DoB,SAAS,CAACC,MAtDgD,EAAAe,qBAAA;SA4GtE;IACLnC,QAAQ,EAARA,QADK;IAELsB,QAAQ,EAARA;GAFF;;;AC1KF;AACA,IAMAc,aAAA,GAAerB,KAAK,CAACqB,aAAN,IAAuBxC,kBAAtC;;ACPA;;AAGA,IAAMyC,kBAAkB,GAAG,SAArBA,kBAAqBA,CAAAC,IAAI,EAAI;MAC3BN,OAAO,GAAGI,aAAa,EAA7B;EACAJ,OAAO,CAACO,WAAR,GAAsBD,IAAtB;SAEON,OAAP;CAJF;ACDA,IAAMQ,cAAc,gBAAiBH,kBAAkB,CAAC,gBAAD,CAAvD;ACAA,IAAML,OAAO,gBAAiBK,kBAAkB,CAAC,QAAD,CAAhD;;ACKA;;;;IAGMI,MAAA,0BAAAxC,gBAAA;;SACGyC,gBAAA,GAAP,SAAAA,iBAAwBC,QAAxB,EAAkC;WACzB;MAAEC,IAAI,EAAE,GAAR;MAAaC,GAAG,EAAE,GAAlB;MAAuBC,MAAM,EAAE,EAA/B;MAAmCC,OAAO,EAAEJ,QAAQ,KAAK;KAAhE;;kBAGUvC,KAAZ,EAAmB;;wCACXA,KAAN;UAEKqB,KAAL,GAAa;MACXuB,QAAQ,EAAE5C,KAAK,CAAC6C,OAAN,CAAcD;KAD1B,CAHiB;;;;;;UAYZE,UAAL,GAAkB,KAAlB;UACKC,gBAAL,GAAwB,IAAxB;QAEI,CAAC/C,KAAK,CAACgD,aAAX,EAA0B;YACnBC,QAAL,GAAgBjD,KAAK,CAAC6C,OAAN,CAAcK,MAAd,CAAqB,UAAAN,QAAQ,EAAI;cAC1CG,gBAAL,GAAwBH,QAAxB;OADc,CAAhB;;;;;SAMJjB,iBAAA,YAAAA,kBAAA,EAAoB;;SACbmB,UAAL,GAAkB,IAAlB;QAEI,KAAKG,QAAT,EAAmB;;;WAGZA,QAAL;;QAEE,CAAC,KAAKjD,KAAL,CAAWgD,aAAhB,EAA+B;WACxBC,QAAL,GAAgB,KAAKjD,KAAL,CAAW6C,OAAX,CAAmBK,MAAnB,CAA0B,UAAAN,QAAQ,EAAI;YAChDtB,MAAI,CAACwB,UAAT,EAAqB;UACnBxB,MAAI,CAACG,QAAL,CAAc;YAAEmB,QAAQ,EAARA;WAAhB;;OAFY,CAAhB;;QAME,KAAKG,gBAAT,EAA2B;WACpBtB,QAAL,CAAc;QAAEmB,QAAQ,EAAE,KAAKG;OAA/B;;;SAIJlB,oBAAA,YAAAA,qBAAA,EAAuB;QACjB,KAAKoB,QAAT,EAAmB;WACZA,QAAL;WACKH,UAAL,GAAkB,KAAlB;WACKC,gBAAL,GAAwB,IAAxB;;;SAIJrC,MAAA,YAAAA,OAAA,EAAS;wBAELC,KAAA,CAAAwC,aAAA,CAACvB,OAAD,CAAehC,QAAf;MACErB,KAAK,EAAE;QACLsE,OAAO,EAAE,KAAK7C,KAAL,CAAW6C,OADf;QAELD,QAAQ,EAAE,KAAKvB,KAAL,CAAWuB,QAFhB;QAGLQ,KAAK,EAAEf,MAAM,CAACC,gBAAP,CAAwB,KAAKjB,KAAL,CAAWuB,QAAX,CAAoBL,QAA5C,CAHF;QAILS,aAAa,EAAE,KAAKhD,KAAL,CAAWgD;;oBAG5BrC,KAAA,CAAAwC,aAAA,CAACf,cAAD,CAAgBxC,QAAhB;MACEP,QAAQ,EAAE,KAAKW,KAAL,CAAWX,QAAX,IAAuB,IADnC;MAEEd,KAAK,EAAE,KAAKyB,KAAL,CAAW6C;MAVtB,CADF;;;EAxDiBlC,KAAK,CAACC,SAAA;AA0E3B,IAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACX6B,MAAM,CAACgB,SAAP,GAAmB;IACjBhE,QAAQ,EAAE0B,SAAS,CAACuC,IADH;IAEjBT,OAAO,EAAE9B,SAAS,CAACC,MAAV,CAAiBC,UAFT;IAGjB+B,aAAa,EAAEjC,SAAS,CAACC;GAH3B;EAMAqB,MAAM,CAACkB,SAAP,CAAiBC,kBAAjB,GAAsC,UAASC,SAAT,EAAoB;4CACxDhD,OAAO,CACLgD,SAAS,CAACZ,OAAV,KAAsB,KAAK7C,KAAL,CAAW6C,OAD5B,EAEL,oCAFK,CAAP;GADF;;;ACpFF;;;;IAGMa,YAAA,0BAAA7D,gBAAA;;;;;;;;UACJgD,OAAA,GAAUc,mBAAa,CAAC5D,KAAA,CAAKC,KAAN;;;;SAEvBU,MAAA,YAAAA,OAAA,EAAS;wBACAC,KAAA,CAAAwC,aAAA,CAACd,MAAD;MAAQQ,OAAO,EAAE,KAAKA,OAAtB;MAA+BxD,QAAQ,EAAE,KAAKW,KAAL,CAAWX;MAA3D;;;EAJuBsB,KAAK,CAACC,SAAA;AAQjC,IAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXkD,YAAY,CAACL,SAAb,GAAyB;IACvBO,cAAc,EAAE7C,SAAS,CAAC8C,KADH;IAEvBC,YAAY,EAAE/C,SAAS,CAACgD,MAFD;IAGvBC,mBAAmB,EAAEjD,SAAS,CAACkD,IAHR;IAIvBC,SAAS,EAAEnD,SAAS,CAACgD,MAJE;IAKvB1E,QAAQ,EAAE0B,SAAS,CAACuC;GALtB;EAQAI,YAAY,CAACH,SAAb,CAAuB5B,iBAAvB,GAA2C,YAAW;4CACpDlB,OAAO,CACL,CAAC,KAAKT,KAAL,CAAW6C,OADP,EAEL,uEACE,yEAHG,CAAP;GADF;;ICzBIsB,SAAA,0BAAAtE,gBAAA;;;;;;SACJ8B,iBAAA,YAAAA,kBAAA,EAAoB;QACd,KAAK3B,KAAL,CAAWoE,OAAf,EAAwB,KAAKpE,KAAL,CAAWoE,OAAX,CAAmBC,IAAnB,CAAwB,IAAxB,EAA8B,IAA9B;;SAG1Bb,kBAAA,YAAAA,mBAAmBC,SAAnB,EAA8B;QACxB,KAAKzD,KAAL,CAAWwB,QAAf,EAAyB,KAAKxB,KAAL,CAAWwB,QAAX,CAAoB6C,IAApB,CAAyB,IAAzB,EAA+B,IAA/B,EAAqCZ,SAArC;;SAG3B5B,oBAAA,YAAAA,qBAAA,EAAuB;QACjB,KAAK7B,KAAL,CAAWsE,SAAf,EAA0B,KAAKtE,KAAL,CAAWsE,SAAX,CAAqBD,IAArB,CAA0B,IAA1B,EAAgC,IAAhC;;SAG5B3D,MAAA,YAAAA,OAAA,EAAS;WACA,IAAP;;;EAdoBC,KAAK,CAACC,SAAA;;ACK9B;;;;AAGA,SAAS2D,MAATA,CAAArE,IAAA,EAA0C;MAAxBsE,OAAwB,GAAAtE,IAAA,CAAxBsE,OAAwB;qBAAfC,IAAe;IAAfA,IAAe,GAAAC,SAAA,cAAR,IAAQ,GAAAA,SAAA;sBAEtC/D,KAAA,CAAAwC,aAAA,CAACvB,OAAD,CAAeV,QAAf,QACG,UAAAU,OAAO,EAAI;KACAA,OAAV,GAAAtB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAmE,SAAS,QAAU,gDAAV,CAAT,GAAAA,SAAS,OAAT;QAEI,CAACF,IAAD,IAAS7C,OAAO,CAACoB,aAArB,EAAoC,OAAO,IAAP;QAE9B4B,MAAM,GAAGhD,OAAO,CAACiB,OAAR,CAAgBgC,KAA/B;wBAGElE,KAAA,CAAAwC,aAAA,CAACgB,SAAD;MACEC,OAAO,EAAE,SAAAA,QAAAU,IAAI,EAAI;QACfA,IAAI,CAACC,OAAL,GAAeH,MAAM,CAACJ,OAAD,CAArB;OAFJ;MAIEhD,QAAQ,EAAE,SAAAA,SAACsD,IAAD,EAAOrB,SAAP,EAAqB;YACzBA,SAAS,CAACe,OAAV,KAAsBA,OAA1B,EAAmC;UACjCM,IAAI,CAACC,OAAL;UACAD,IAAI,CAACC,OAAL,GAAeH,MAAM,CAACJ,OAAD,CAArB;;OAPN;MAUEF,SAAS,EAAE,SAAAA,UAAAQ,IAAI,EAAI;QACjBA,IAAI,CAACC,OAAL;OAXJ;MAaEP,OAAO,EAAEA;MAdb;GARJ,CADF;;AA+BF,IAAAlE,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;MACLwE,WAAW,GAAGjE,SAAS,CAACkE,SAAV,CAAoB,CAAClE,SAAS,CAACkD,IAAX,EAAiBlD,SAAS,CAACmE,MAA3B,CAApB,CAApB;EAEAX,MAAM,CAAClB,SAAP,GAAmB;IACjBoB,IAAI,EAAE1D,SAAS,CAACoE,IADC;IAEjBX,OAAO,EAAEQ,WAAW,CAAC/D;GAFvB;;AC3CF,IAAMmE,KAAK,GAAG,EAAd;AACA,IAAMC,UAAU,GAAG,KAAnB;AACA,IAAIC,UAAU,GAAG,CAAjB;AAEA,SAASC,WAATA,CAAqB/C,IAArB,EAA2B;MACrB4C,KAAK,CAAC5C,IAAD,CAAT,EAAiB,OAAO4C,KAAK,CAAC5C,IAAD,CAAZ;MAEXgD,SAAS,GAAGC,YAAY,CAACC,OAAb,CAAqBlD,IAArB,CAAlB;MAEI8C,UAAU,GAAGD,UAAjB,EAA6B;IAC3BD,KAAK,CAAC5C,IAAD,CAAL,GAAcgD,SAAd;IACAF,UAAU;;SAGLE,SAAP;;;;;;AAMF,SAASG,YAATA,CAAsBnD,IAAtB,EAAkCE,MAAlC,EAA+C;MAAzBF,IAAyB;IAAzBA,IAAyB,GAAlB,GAAkB;;MAAbE,MAAa;IAAbA,MAAa,GAAJ,EAAI;;SACtCF,IAAI,KAAK,GAAT,GAAeA,IAAf,GAAsB+C,WAAW,CAAC/C,IAAD,CAAX,CAAkBE,MAAlB,EAA0B;IAAEkD,MAAM,EAAE;GAApC,CAA7B;;;ACdF;;;;AAGA,SAASC,QAATA,CAAA3F,IAAA,EAAuD;MAAnC4F,aAAmC,GAAA5F,IAAA,CAAnC4F,aAAmC;IAApBC,EAAoB,GAAA7F,IAAA,CAApB6F,EAAoB;qBAAhBpH,IAAgB;IAAhBA,IAAgB,GAAAqH,SAAA,cAAT,KAAS,GAAAA,SAAA;sBAEnDrF,KAAA,CAAAwC,aAAA,CAACvB,OAAD,CAAeV,QAAf,QACG,UAAAU,OAAO,EAAI;KACAA,OAAV,GAAAtB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAmE,SAAS,QAAU,kDAAV,CAAT,GAAAA,SAAS,OAAT;QAEQ9B,OAHE,GAGyBjB,OAHzB,CAGFiB,OAHE;MAGOG,aAHP,GAGyBpB,OAHzB,CAGOoB,aAHP;QAKJ4B,MAAM,GAAGjG,IAAI,GAAGkE,OAAO,CAAClE,IAAX,GAAkBkE,OAAO,CAACoD,OAA7C;QACMrD,QAAQ,GAAGsD,cAAc,CAC7BJ,aAAa,GACT,OAAOC,EAAP,KAAc,QAAd,GACEJ,YAAY,CAACI,EAAD,EAAKD,aAAa,CAACpD,MAAnB,CADd,GAAAyD,QAAA,KAGOJ,EAHP;MAIIxD,QAAQ,EAAEoD,YAAY,CAACI,EAAE,CAACxD,QAAJ,EAAcuD,aAAa,CAACpD,MAA5B;MALjB,GAOTqD,EARyB,CAA/B,CANU;;;QAmBN/C,aAAJ,EAAmB;MACjB4B,MAAM,CAAChC,QAAD,CAAN;aACO,IAAP;;wBAIAjC,KAAA,CAAAwC,aAAA,CAACgB,SAAD;MACEC,OAAO,EAAE,SAAAA,QAAA,EAAM;QACbQ,MAAM,CAAChC,QAAD,CAAN;OAFJ;MAIEpB,QAAQ,EAAE,SAAAA,SAACsD,IAAD,EAAOrB,SAAP,EAAqB;YACvB2C,YAAY,GAAGF,cAAc,CAACzC,SAAS,CAACsC,EAAX,CAAnC;YAEE,CAACM,iBAAiB,CAACD,YAAD,EAAAD,QAAA,KACbvD,QADa;UAEhB1E,GAAG,EAAEkI,YAAY,CAAClI;WAHtB,EAKE;UACA0G,MAAM,CAAChC,QAAD,CAAN;;OAZN;MAeEmD,EAAE,EAAEA;MAhBR;GAzBJ,CADF;;AAkDF,IAAAzF,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXqF,QAAQ,CAACxC,SAAT,GAAqB;IACnB1E,IAAI,EAAEoC,SAAS,CAACoE,IADG;IAEnBmB,IAAI,EAAEvF,SAAS,CAACmE,MAFG;IAGnBa,EAAE,EAAEhF,SAAS,CAACkE,SAAV,CAAoB,CAAClE,SAAS,CAACmE,MAAX,EAAmBnE,SAAS,CAACC,MAA7B,CAApB,EAA0DC;GAHhE;;AC9DF,IAAMsF,OAAK,GAAG,EAAd;AACA,IAAMC,YAAU,GAAG,KAAnB;AACA,IAAIC,YAAU,GAAG,CAAjB;AAEA,SAASC,aAATnB,CAAqB/C,IAArB,EAA2BmE,OAA3B,EAAoC;MAC5BC,QAAQ,QAAMD,OAAO,CAACE,GAAd,GAAoBF,OAAO,CAACG,MAA5B,GAAqCH,OAAO,CAACI,SAA3D;MACMC,SAAS,GAAGT,OAAK,CAACK,QAAD,CAAL,KAAoBL,OAAK,CAACK,QAAD,CAAL,GAAkB,EAAtC,CAAlB;MAEII,SAAS,CAACxE,IAAD,CAAb,EAAqB,OAAOwE,SAAS,CAACxE,IAAD,CAAhB;MAEfyE,IAAI,GAAG,EAAb;MACMC,MAAM,GAAGzB,YAAY,CAACjD,IAAD,EAAOyE,IAAP,EAAaN,OAAb,CAA3B;MACMQ,MAAM,GAAG;IAAED,MAAM,EAANA,MAAF;IAAUD,IAAI,EAAJA;GAAzB;MAEIR,YAAU,GAAGD,YAAjB,EAA6B;IAC3BQ,SAAS,CAACxE,IAAD,CAAT,GAAkB2E,MAAlB;IACAV,YAAU;;SAGLU,MAAP;;;;;;AAMF,SAASC,SAATA,CAAmB7E,QAAnB,EAA6BoE,OAA7B,EAA2C;MAAdA,OAAc;IAAdA,OAAc,GAAJ,EAAI;;MACrC,OAAOA,OAAP,KAAmB,QAAnB,IAA+BrH,KAAK,CAACC,OAAN,CAAcoH,OAAd,CAAnC,EAA2D;IACzDA,OAAO,GAAG;MAAEnE,IAAI,EAAEmE;KAAlB;;iBAGiEA,OAL1B;IAKjCnE,IALiC,GAAA6E,QAAA,CAKjC7E,IALiC;8BAK3B8E,KAL2B;IAK3BA,KAL2B,GAAAC,cAAA,cAKnB,KALmB,GAAAA,cAAA;+BAKZT,MALY;IAKZA,MALY,GAAAU,eAAA,cAKH,KALG,GAAAA,eAAA;kCAKIT,SALJ;IAKIA,SALJ,GAAAU,kBAAA,cAKgB,KALhB,GAAAA,kBAAA;MAOnCC,KAAK,GAAG,GAAGC,MAAH,CAAUnF,IAAV,CAAd;SAEOkF,KAAK,CAACE,MAAN,CAAa,UAACC,OAAD,EAAUrF,IAAV,EAAmB;QACjC,CAACA,IAAD,IAASA,IAAI,KAAK,EAAtB,EAA0B,OAAO,IAAP;QACtBqF,OAAJ,EAAa,OAAOA,OAAP;uBAEYnB,aAAW,CAAClE,IAAD,EAAO;QACzCqE,GAAG,EAAES,KADoC;QAEzCR,MAAM,EAANA,MAFyC;QAGzCC,SAAS,EAATA;OAHkC,CAJC;MAI7BG,MAJ6B,GAAAY,YAAA,CAI7BZ,MAJ6B;MAIrBD,IAJqB,GAAAa,YAAA,CAIrBb,IAJqB;QAS/B7D,KAAK,GAAG8D,MAAM,CAACa,IAAP,CAAYxF,QAAZ,CAAd;QAEI,CAACa,KAAL,EAAY,OAAO,IAAP;QAELX,GAb8B,GAaZW,KAbY;MAatB4E,MAbsB,GAaZ5E,KAbY,CAAA6E,KAAA;QAc/BtF,OAAO,GAAGJ,QAAQ,KAAKE,GAA7B;QAEI6E,KAAK,IAAI,CAAC3E,OAAd,EAAuB,OAAO,IAAP;WAEhB;MACLH,IAAI,EAAJA,IADK;;MAELC,GAAG,EAAED,IAAI,KAAK,GAAT,IAAgBC,GAAG,KAAK,EAAxB,GAA6B,GAA7B,GAAmCA,GAFnC;;MAGLE,OAAO,EAAPA,OAHK;;MAILD,MAAM,EAAEuE,IAAI,CAACW,MAAL,CAAY,UAACM,IAAD,EAAOhK,GAAP,EAAYiK,KAAZ,EAAsB;QACxCD,IAAI,CAAChK,GAAG,CAACgE,IAAL,CAAJ,GAAiB8F,MAAM,CAACG,KAAD,CAAvB;eACOD,IAAP;OAFM,EAGL,EAHK;KAJV;GAlBK,EA2BJ,IA3BI,CAAP;;AC3BF,SAASE,eAATA,CAAyB/I,QAAzB,EAAmC;SAC1BsB,KAAK,CAAC0H,QAAN,CAAeC,KAAf,CAAqBjJ,QAArB,MAAmC,CAA1C;;AAGF,SAASkJ,eAATA,CAAyBlJ,QAAzB,EAAmCW,KAAnC,EAA0CwC,IAA1C,EAAgD;MACxCjE,KAAK,GAAGc,QAAQ,CAACW,KAAD,CAAtB;0CAEAS,OAAO,CACLlC,KAAK,KAAKmD,SADL,EAEL,2EACWc,IAAI,gBAAaA,IAAb,UAAuB,EADtC,qBAEE,gDAJG,CAAP;SAOOjE,KAAK,IAAI,IAAhB;;;;;;IAMIiK,KAAA,0BAAA3I,gBAAA;;;;;;SACJa,MAAA,YAAAA,OAAA,EAAS;;wBAELC,KAAA,CAAAwC,aAAA,CAACvB,OAAD,CAAeV,QAAf,QACG,UAAAuH,SAAO,EAAI;OACAA,SAAV,GAAAnI,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAmE,SAAS,QAAU,+CAAV,CAAT,GAAAA,SAAS,OAAT;UAEM/B,QAAQ,GAAG7C,KAAI,CAACC,KAAL,CAAW4C,QAAX,IAAuB6F,SAAO,CAAC7F,QAAhD;UACMQ,KAAK,GAAGrD,KAAI,CAACC,KAAL,CAAW8F,aAAX,GACV/F,KAAI,CAACC,KAAL,CAAW8F,aADD;MAAA,EAEV/F,KAAI,CAACC,KAAL,CAAWwC,IAAX,GACA4E,SAAS,CAACxE,QAAQ,CAACL,QAAV,EAAoBxC,KAAI,CAACC,KAAzB,CADT,GAEAyI,SAAO,CAACrF,KAJZ;UAMMpD,KAAK,GAAAmG,QAAA,KAAQsC,SAAR;QAAiB7F,QAAQ,EAARA,QAAjB;QAA2BQ,KAAK,EAALA;QAAtC;wBAEsCrD,KAAI,CAACC,KAZjC;QAYJX,QAZI,GAAAqJ,WAAA,CAYJrJ,QAZI;QAYMsJ,SAZN,GAAAD,WAAA,CAYMC,SAZN;QAYiBjI,MAZjB,GAAAgI,WAAA,CAYiBhI,MAZjB;;;UAgBNpB,KAAK,CAACC,OAAN,CAAcF,QAAd,KAA2B+I,eAAe,CAAC/I,QAAD,CAA9C,EAA0D;QACxDA,QAAQ,GAAG,IAAX;;0BAIAsB,KAAA,CAAAwC,aAAA,CAACvB,OAAD,CAAehC,QAAf;QAAwBrB,KAAK,EAAEyB;SAC5BA,KAAK,CAACoD,KAAN,GACG/D,QAAQ,GACN,OAAOA,QAAP,KAAoB,UAApB,GACEiB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBACE+H,eAAe,CAAClJ,QAAD,EAAWW,KAAX,EAAkBD,KAAI,CAACC,KAAL,CAAWwC,IAA7B,CADjB,GAEEnD,QAAQ,CAACW,KAAD,CAHZ,GAIEX,QALI,GAMNsJ,SAAS,gBACThI,KAAK,CAACwC,aAAN,CAAoBwF,SAApB,EAA+B3I,KAA/B,CADS,GAETU,MAAM,GACNA,MAAM,CAACV,KAAD,CADA,GAEN,IAXL,GAYG,OAAOX,QAAP,KAAoB,UAApB,GACAiB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBACE+H,eAAe,CAAClJ,QAAD,EAAWW,KAAX,EAAkBD,KAAI,CAACC,KAAL,CAAWwC,IAA7B,CADjB,GAEEnD,QAAQ,CAACW,KAAD,CAHV,GAIA,IAjBN,CADF;KArBJ,CADF;;;EAFgBW,KAAK,CAACC,SAAA;AAmD1B,IAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXgI,KAAK,CAACnF,SAAN,GAAkB;IAChBhE,QAAQ,EAAE0B,SAAS,CAACkE,SAAV,CAAoB,CAAClE,SAAS,CAACkD,IAAX,EAAiBlD,SAAS,CAACuC,IAA3B,CAApB,CADM;IAEhBqF,SAAS,EAAE,SAAAA,UAAC3I,KAAD,EAAQ4I,QAAR,EAAqB;UAC1B5I,KAAK,CAAC4I,QAAD,CAAL,IAAmB,CAACC,kBAAkB,CAAC7I,KAAK,CAAC4I,QAAD,CAAN,CAA1C,EAA6D;eACpD,IAAIE,KAAJ,yFAAP;;KAJY;IAShBxB,KAAK,EAAEvG,SAAS,CAACoE,IATD;IAUhBvC,QAAQ,EAAE7B,SAAS,CAACC,MAVJ;IAWhBwB,IAAI,EAAEzB,SAAS,CAACkE,SAAV,CAAoB,CACxBlE,SAAS,CAACmE,MADc,EAExBnE,SAAS,CAACgI,OAAV,CAAkBhI,SAAS,CAACmE,MAA5B,CAFwB,CAApB,CAXU;IAehBxE,MAAM,EAAEK,SAAS,CAACkD,IAfF;IAgBhB8C,SAAS,EAAEhG,SAAS,CAACoE,IAhBL;IAiBhB2B,MAAM,EAAE/F,SAAS,CAACoE;GAjBpB;EAoBAqD,KAAK,CAACjF,SAAN,CAAgB5B,iBAAhB,GAAoC,YAAW;4CAC7ClB,OAAO,CACL,EACE,KAAKT,KAAL,CAAWX,QAAX,IACA,CAAC+I,eAAe,CAAC,KAAKpI,KAAL,CAAWX,QAAZ,CADhB,IAEA,KAAKW,KAAL,CAAW2I,SAHb,CADK,EAML,gHANK,CAAP;4CASAlI,OAAO,CACL,EACE,KAAKT,KAAL,CAAWX,QAAX,IACA,CAAC+I,eAAe,CAAC,KAAKpI,KAAL,CAAWX,QAAZ,CADhB,IAEA,KAAKW,KAAL,CAAWU,MAHb,CADK,EAML,0GANK,CAAP;4CASAD,OAAO,CACL,EAAE,KAAKT,KAAL,CAAW2I,SAAX,IAAwB,KAAK3I,KAAL,CAAWU,MAArC,CADK,EAEL,2GAFK,CAAP;GAnBF;EAyBA8H,KAAK,CAACjF,SAAN,CAAgBC,kBAAhB,GAAqC,UAASC,SAAT,EAAoB;4CACvDhD,OAAO,CACL,EAAE,KAAKT,KAAL,CAAW4C,QAAX,IAAuB,CAACa,SAAS,CAACb,QAApC,CADK,EAEL,yKAFK,CAAP;4CAKAnC,OAAO,CACL,EAAE,CAAC,KAAKT,KAAL,CAAW4C,QAAZ,IAAwBa,SAAS,CAACb,QAApC,CADK,EAEL,qKAFK,CAAP;GANF;;ACtHF,SAASoG,eAATA,CAAyBxG,IAAzB,EAA+B;SACtBA,IAAI,CAACyG,MAAL,CAAY,CAAZ,MAAmB,GAAnB,GAAyBzG,IAAzB,GAAgC,MAAMA,IAA7C;;AAGF,SAAS0G,WAATA,CAAqBC,QAArB,EAA+BvG,QAA/B,EAAyC;MACnC,CAACuG,QAAL,EAAe,OAAOvG,QAAP;sBAGVA,QADL;IAEEL,QAAQ,EAAEyG,eAAe,CAACG,QAAD,CAAf,GAA4BvG,QAAQ,CAACL;;;AAInD,SAAS6G,aAATA,CAAuBD,QAAvB,EAAiCvG,QAAjC,EAA2C;MACrC,CAACuG,QAAL,EAAe,OAAOvG,QAAP;MAETyG,IAAI,GAAGL,eAAe,CAACG,QAAD,CAA5B;MAEIvG,QAAQ,CAACL,QAAT,CAAkB+G,OAAlB,CAA0BD,IAA1B,MAAoC,CAAxC,EAA2C,OAAOzG,QAAP;sBAGtCA,QADL;IAEEL,QAAQ,EAAEK,QAAQ,CAACL,QAAT,CAAkBgH,MAAlB,CAAyBF,IAAI,CAACG,MAA9B;;;AAId,SAASC,SAATA,CAAmB7G,QAAnB,EAA6B;SACpB,OAAOA,QAAP,KAAoB,QAApB,GAA+BA,QAA/B,GAA0C8G,UAAU,CAAC9G,QAAD,CAA3D;;AAGF,SAAS+G,aAATA,CAAuBC,UAAvB,EAAmC;SAC1B,YAAM;4CACXjF,SAAS,QAAQ,mCAAR,EAA6CiF,UAA7C,CAAT,GAAAjF,SAAS,OAAT;GADF;;AAKF,SAASkF,IAATA,CAAA,EAAgB;;;;;;;;IAQVC,YAAA,0BAAAjK,gBAAA;;;;;;;;UAQJkK,UAAA,GAAa,UAAAnH,QAAQ;aAAI7C,KAAA,CAAKiK,UAAL,CAAgBpH,QAAhB,EAA0B,MAA1B,CAAJ;;UACrBqH,aAAA,GAAgB,UAAArH,QAAQ;aAAI7C,KAAA,CAAKiK,UAAL,CAAgBpH,QAAhB,EAA0B,SAA1B,CAAJ;;UACxBsH,YAAA,GAAe;aAAML,IAAN;;UACfM,WAAA,GAAc;aAAMN,IAAN;;;;;SAVdG,UAAA,YAAAA,WAAWpH,QAAX,EAAqBwH,MAArB,EAA6B;sBACa,KAAKpK,KADlB;yCACnBmJ,QADmB;MACnBA,QADmB,GAAAkB,oBAAA,cACR,EADQ,GAAAA,oBAAA;wCACJzI,OADI;MACJA,OADI,GAAA0I,mBAAA,cACM,EADN,GAAAA,mBAAA;IAE3B1I,OAAO,CAACwI,MAAR,GAAiBA,MAAjB;IACAxI,OAAO,CAACgB,QAAR,GAAmBsG,WAAW,CAACC,QAAD,EAAWjD,cAAc,CAACtD,QAAD,CAAzB,CAA9B;IACAhB,OAAO,CAACa,GAAR,GAAcgH,SAAS,CAAC7H,OAAO,CAACgB,QAAT,CAAvB;;SAQFlC,MAAA,YAAAA,OAAA,EAAS;uBAC0D,KAAKV,KAD/D;2CACCmJ,QADD;MACCA,QADD,GAAAoB,qBAAA,cACY,EADZ,GAAAA,qBAAA;0CACgB3I,OADhB;MACgBA,OADhB,GAAA4I,oBAAA,cAC0B,EAD1B,GAAAA,oBAAA;2CAC8B5H,QAD9B;MAC8BA,QAD9B,GAAA6H,qBAAA,cACyC,GADzC,GAAAA,qBAAA;MACiDC,IADjD,GAAAC,6BAAA,CAAAC,YAAA;QAGD/H,OAAO,GAAG;MACdgI,UAAU,EAAE,SAAAA,WAAArI,IAAI;eAAIwG,eAAe,CAACG,QAAQ,GAAGM,SAAS,CAACjH,IAAD,CAArB,CAAnB;OADF;MAEd4H,MAAM,EAAE,KAFM;MAGdxH,QAAQ,EAAEwG,aAAa,CAACD,QAAD,EAAWjD,cAAc,CAACtD,QAAD,CAAzB,CAHT;MAIdjE,IAAI,EAAE,KAAKoL,UAJG;MAKd9D,OAAO,EAAE,KAAKgE,aALA;MAMda,EAAE,EAAEnB,aAAa,CAAC,IAAD,CANH;MAOdoB,MAAM,EAAEpB,aAAa,CAAC,QAAD,CAPP;MAQdqB,SAAS,EAAErB,aAAa,CAAC,WAAD,CARV;MASdzG,MAAM,EAAE,KAAKgH,YATC;MAUdrF,KAAK,EAAE,KAAKsF;KAVd;wBAaOxJ,KAAA,CAAAwC,aAAA,CAACd,MAAD,EAAA8D,QAAA,KAAYuE,IAAZ;MAAkB7H,OAAO,EAAEA,OAA3B;MAAoCG,aAAa,EAAEpB;OAA1D;;;EA7BuBjB,KAAK,CAACC,SAAA;AAiCjC,IAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXsJ,YAAY,CAACzG,SAAb,GAAyB;IACvB8F,QAAQ,EAAEpI,SAAS,CAACmE,MADG;IAEvBtD,OAAO,EAAEb,SAAS,CAACC,MAFI;IAGvB4B,QAAQ,EAAE7B,SAAS,CAACkE,SAAV,CAAoB,CAAClE,SAAS,CAACmE,MAAX,EAAmBnE,SAAS,CAACC,MAA7B,CAApB;GAHZ;EAMA8I,YAAY,CAACvG,SAAb,CAAuB5B,iBAAvB,GAA2C,YAAW;4CACpDlB,OAAO,CACL,CAAC,KAAKT,KAAL,CAAW6C,OADP,EAEL,uEACE,yEAHG,CAAP;GADF;;;ACpFF;;;;IAGMoI,MAAA,0BAAApL,gBAAA;;;;;;SACJa,MAAA,YAAAA,OAAA,EAAS;;wBAELC,KAAA,CAAAwC,aAAA,CAACvB,OAAD,CAAeV,QAAf,QACG,UAAAU,OAAO,EAAI;OACAA,OAAV,GAAAtB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAmE,SAAS,QAAU,gDAAV,CAAT,GAAAA,SAAS,OAAT;UAEM/B,QAAQ,GAAG7C,KAAI,CAACC,KAAL,CAAW4C,QAAX,IAAuBhB,OAAO,CAACgB,QAAhD;UAEIsI,OAAJ,EAAa9H,KAAb,CALU;;;;;MAWVzC,KAAK,CAAC0H,QAAN,CAAelJ,OAAf,CAAuBY,KAAI,CAACC,KAAL,CAAWX,QAAlC,EAA4C,UAAA8L,KAAK,EAAI;YAC/C/H,KAAK,IAAI,IAAT,iBAAiBzC,KAAK,CAACyK,cAAN,CAAqBD,KAArB,CAArB,EAAkD;UAChDD,OAAO,GAAGC,KAAV;cAEM3I,IAAI,GAAG2I,KAAK,CAACnL,KAAN,CAAYwC,IAAZ,IAAoB2I,KAAK,CAACnL,KAAN,CAAYsG,IAA7C;UAEAlD,KAAK,GAAGZ,IAAI,GACR4E,SAAS,CAACxE,QAAQ,CAACL,QAAV,EAAA4D,QAAA,KAAyBgF,KAAK,CAACnL,KAA/B;YAAsCwC,IAAI,EAAJA;aADvC,GAERZ,OAAO,CAACwB,KAFZ;;OANJ;aAYOA,KAAK,gBACRzC,KAAK,CAAC0K,YAAN,CAAmBH,OAAnB,EAA4B;QAAEtI,QAAQ,EAARA,QAAF;QAAYkD,aAAa,EAAE1C;OAAvD,CADQ,GAER,IAFJ;KAxBJ,CADF;;;EAFiBzC,KAAK,CAACC,SAAA;AAoC3B,IAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;EACXyK,MAAM,CAAC5H,SAAP,GAAmB;IACjBhE,QAAQ,EAAE0B,SAAS,CAACuC,IADH;IAEjBV,QAAQ,EAAE7B,SAAS,CAACC;GAFtB;EAKAiK,MAAM,CAAC1H,SAAP,CAAiBC,kBAAjB,GAAsC,UAASC,SAAT,EAAoB;4CACxDhD,OAAO,CACL,EAAE,KAAKT,KAAL,CAAW4C,QAAX,IAAuB,CAACa,SAAS,CAACb,QAApC,CADK,EAEL,0KAFK,CAAP;4CAKAnC,OAAO,CACL,EAAE,CAAC,KAAKT,KAAL,CAAW4C,QAAZ,IAAwBa,SAAS,CAACb,QAApC,CADK,EAEL,sKAFK,CAAP;GANF;;;AC9CF;;;;AAGA,SAAS0I,UAATA,CAAoB1K,SAApB,EAA+B;MACvBuB,WAAW,oBAAiBvB,SAAS,CAACuB,WAAV,IAAyBvB,SAAS,CAACsB,IAApD,OAAjB;MACMqJ,CAAC,GAAG,SAAJA,CAAIA,CAAAvL,KAAK,EAAI;QACTwL,mBADS,GACkCxL,KADlC,CACTwL,mBADS;MACeC,cADf,GAAAd,6BAAA,CACkC3K,KADlC;wBAIfW,KAAA,CAAAwC,aAAA,CAACvB,OAAD,CAAeV,QAAf,QACG,UAAAU,OAAO,EAAI;OAERA,OADF,GAAAtB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAmE,SAAS,iCAEgBxC,WAFhB,4BAAT,GAAAwC,SAAS,OAAT;0BAKEhE,KAAA,CAAAwC,aAAA,CAACvC,SAAD,EAAAuF,QAAA,KACMsF,cADN,EAEM7J,OAFN;QAGE8J,GAAG,EAAEF;SAJT;KANJ,CADF;GAHF;EAsBAD,CAAC,CAACpJ,WAAF,GAAgBA,WAAhB;EACAoJ,CAAC,CAACI,gBAAF,GAAqB/K,SAArB;6CAEa;IACX2K,CAAC,CAAClI,SAAF,GAAc;MACZmI,mBAAmB,EAAEzK,SAAS,CAACkE,SAAV,CAAoB,CACvClE,SAAS,CAACmE,MAD6B,EAEvCnE,SAAS,CAACkD,IAF6B,EAGvClD,SAAS,CAACC,MAH6B,CAApB;KADvB;;SASK4K,YAAY,CAACL,CAAD,EAAI3K,SAAJ,CAAnB;;ACxCF,IAAMiL,UAAU,GAAGlL,KAAK,CAACkL,UAAzB;AAEA,SAAgBC,UAATA,CAAA,EAAsB;6CACd;MAET,OAAOD,UAAP,KAAsB,UADxB,IAAAvL,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAmE,SAAS,QAEP,yDAFO,CAAT,GAAAA,SAAS,OAAT;;SAMKkH,UAAU,CAACzJ,cAAD,CAAjB;;AAGF,SAAgB2J,WAATA,CAAA,EAAuB;6CACf;MAET,OAAOF,UAAP,KAAsB,UADxB,IAAAvL,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAmE,SAAS,QAEP,0DAFO,CAAT,GAAAA,SAAS,OAAT;;SAMKkH,UAAU,CAACjK,OAAD,CAAV,CAA0BgB,QAAjC;;AAGF,SAAgBoJ,SAATA,CAAA,EAAqB;6CACb;MAET,OAAOH,UAAP,KAAsB,UADxB,IAAAvL,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAmE,SAAS,QAEP,wDAFO,CAAT,GAAAA,SAAS,OAAT;;MAMIvB,KAAK,GAAGyI,UAAU,CAACjK,OAAD,CAAV,CAA0BwB,KAAxC;SACOA,KAAK,GAAGA,KAAK,CAACV,MAAT,GAAkB,EAA9B;;AAGF,SAAgBuJ,aAATA,CAAuBzJ,IAAvB,EAA6B;6CACrB;MAET,OAAOqJ,UAAP,KAAsB,UADxB,IAAAvL,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAmE,SAAS,QAEP,4DAFO,CAAT,GAAAA,SAAS,OAAT;;MAMI/B,QAAQ,GAAGmJ,WAAW,EAA5B;MACM3I,KAAK,GAAGyI,UAAU,CAACjK,OAAD,CAAV,CAA0BwB,KAAxC;SACOZ,IAAI,GAAG4E,SAAS,CAACxE,QAAQ,CAACL,QAAV,EAAoBC,IAApB,CAAZ,GAAwCY,KAAnD;;ACrDF,IAAA9C,OAAA,CAAAC,GAAA,CAAAC,QAAA,mBAAa;MACP,OAAOzC,MAAP,KAAkB,WAAtB,EAAmC;QAC3BmO,QAAM,GAAGnO,MAAf;QACMG,GAAG,GAAG,wBAAZ;QACMiO,UAAU,GAAG;MAAEC,GAAG,EAAE,UAAP;MAAmBC,GAAG,EAAE,YAAxB;MAAsCC,GAAG,EAAE;KAA9D;QAEIJ,QAAM,CAAChO,GAAD,CAAN,IAAegO,QAAM,CAAChO,GAAD,CAAN,KAAgB,KAAnC,EAA6D;UACrDqO,gBAAgB,GAAGJ,UAAU,CAACD,QAAM,CAAChO,GAAD,CAAP,CAAnC;UACMsO,kBAAkB,GAAGL,UAAU,CAAC,KAAD,CAArC,CAF2D;;;YAMrD,IAAIrD,KAAJ,CACJ,yBAAuB0D,kBAAvB,2EAC2CD,gBAD3C,8CADI,CAAN;;IAOFL,QAAM,CAAChO,GAAD,CAAN,GAAc,KAAd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}