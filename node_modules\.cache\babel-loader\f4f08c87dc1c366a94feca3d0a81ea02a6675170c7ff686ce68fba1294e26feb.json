{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_stackoverflow = register(\"stackoverflow\", {\n  \"color\": \"#ed803d\",\n  \"path\": \"M64 0v64H0V0zM46.145 37.265H42.8v10.038H19.376V37.265H16.03V50.65h30.115zm-6.688 2.46L23.023 36.27l.69-3.287 16.435 3.456zm.964-4.234-15.224-7.09 1.418-3.045 15.224 7.09zm1.895-3.811L29.41 20.932l2.15-2.58 12.906 10.747zm-7.27-16.688 2.695-2.004 10.022 13.476-2.695 2.004zm4.407 28.965H22.722v-3.346h16.73z\"\n});\nexport { _socialIcons_stackoverflow as default };", "map": {"version": 3, "names": ["register", "_socialIcons_stackoverflow", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/stackoverflow.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_stackoverflow = register(\"stackoverflow\", {\"color\":\"#ed803d\",\"path\":\"M64 0v64H0V0zM46.145 37.265H42.8v10.038H19.376V37.265H16.03V50.65h30.115zm-6.688 2.46L23.023 36.27l.69-3.287 16.435 3.456zm.964-4.234-15.224-7.09 1.418-3.045 15.224 7.09zm1.895-3.811L29.41 20.932l2.15-2.58 12.906 10.747zm-7.27-16.688 2.695-2.004 10.022 13.476-2.695 2.004zm4.407 28.965H22.722v-3.346h16.73z\"});\n\nexport { _socialIcons_stackoverflow as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,0BAA0B,GAAGD,QAAQ,CAAC,eAAe,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAoT,CAAC,CAAC;AAE3Y,SAASC,0BAA0B,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}