{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { ThemeProvider } from 'styled-components';\nimport { lightTheme, darkTheme } from './theme/themes';\nimport GlobalStyle from './theme/GlobalStyle';\nimport Navbar from './components/Navbar';\nimport Home from './components/Home';\nimport About from './components/About';\nimport Skills from './components/Skills';\nimport Education from './components/Education';\nimport Projects from './components/Projects';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [theme, setTheme] = useState('light');\n  const toggleTheme = () => {\n    setTheme(theme === 'light' ? 'dark' : 'light');\n  };\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      setTheme(savedTheme);\n    }\n  }, []);\n  useEffect(() => {\n    localStorage.setItem('theme', theme);\n  }, [theme]);\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme === 'light' ? lightTheme : darkTheme,\n    children: [/*#__PURE__*/_jsxDEV(GlobalStyle, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        theme: theme,\n        toggleTheme: toggleTheme\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Skills, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Education, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Experience, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Projects, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"l0NnHMBAjTNA2m05PT0LPL3eOKc=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ThemeProvider", "lightTheme", "darkTheme", "GlobalStyle", "<PERSON><PERSON><PERSON>", "Home", "About", "Skills", "Education", "Projects", "jsxDEV", "_jsxDEV", "App", "_s", "theme", "setTheme", "toggleTheme", "savedTheme", "localStorage", "getItem", "setItem", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "Experience", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { ThemeProvider } from 'styled-components';\nimport { lightTheme, darkTheme } from './theme/themes';\nimport GlobalStyle from './theme/GlobalStyle';\nimport Navbar from './components/Navbar';\nimport Home from './components/Home';\nimport About from './components/About';\nimport Skills from './components/Skills';\nimport Education from './components/Education';\nimport Projects from './components/Projects';\nimport 'bootstrap/dist/css/bootstrap.min.css';\n\nfunction App() {\n  const [theme, setTheme] = useState('light');\n\n  const toggleTheme = () => {\n    setTheme(theme === 'light' ? 'dark' : 'light');\n  };\n\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      setTheme(savedTheme);\n    }\n  }, []);\n\n  useEffect(() => {\n    localStorage.setItem('theme', theme);\n  }, [theme]);\n\n  return (\n    <ThemeProvider theme={theme === 'light' ? lightTheme : darkTheme}>\n      <GlobalStyle />\n      <div className=\"App\">\n        <Navbar theme={theme} toggleTheme={toggleTheme} />\n        <Home />\n        <About />\n        <Skills />\n        <Education />\n        <Experience />\n        <Projects />\n      </div>\n    </ThemeProvider>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,mBAAmB;AACjD,SAASC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AACtD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAO,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,OAAO,CAAC;EAE3C,MAAMkB,WAAW,GAAGA,CAAA,KAAM;IACxBD,QAAQ,CAACD,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;EAChD,CAAC;EAEDf,SAAS,CAAC,MAAM;IACd,MAAMkB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,IAAIF,UAAU,EAAE;MACdF,QAAQ,CAACE,UAAU,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;EAENlB,SAAS,CAAC,MAAM;IACdmB,YAAY,CAACE,OAAO,CAAC,OAAO,EAAEN,KAAK,CAAC;EACtC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,oBACEH,OAAA,CAACX,aAAa;IAACc,KAAK,EAAEA,KAAK,KAAK,OAAO,GAAGb,UAAU,GAAGC,SAAU;IAAAmB,QAAA,gBAC/DV,OAAA,CAACR,WAAW;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfd,OAAA;MAAKe,SAAS,EAAC,KAAK;MAAAL,QAAA,gBAClBV,OAAA,CAACP,MAAM;QAACU,KAAK,EAAEA,KAAM;QAACE,WAAW,EAAEA;MAAY;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDd,OAAA,CAACN,IAAI;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACRd,OAAA,CAACL,KAAK;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACTd,OAAA,CAACJ,MAAM;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVd,OAAA,CAACH,SAAS;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACbd,OAAA,CAACgB,UAAU;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACdd,OAAA,CAACF,QAAQ;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACZ,EAAA,CAhCQD,GAAG;AAAAgB,EAAA,GAAHhB,GAAG;AAkCZ,eAAeA,GAAG;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}