{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\Skills.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card } from 'react-bootstrap';\nimport Fade from 'react-reveal/Fade';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SkillsSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({\n  theme\n}) => theme.background};\n  padding: 100px 0;\n`;\n_c = SkillsSection;\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 30px;\n  color: ${({\n  theme\n}) => theme.color};\n  \n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({\n  theme\n}) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n_c2 = SectionTitle;\nconst IntroText = styled.p`\n  font-size: 1.1rem;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({\n  theme\n}) => theme.color};\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c3 = IntroText;\nconst SkillCard = styled(Card)`\n  background: ${({\n  theme\n}) => theme.cardBackground};\n  border: 1px solid ${({\n  theme\n}) => theme.cardBorderColor};\n  border-radius: 15px;\n  padding: 20px;\n  margin-bottom: 30px;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  }\n`;\n_c4 = SkillCard;\nconst SkillCategory = styled.h4`\n  color: ${({\n  theme\n}) => theme.accentColor};\n  margin-bottom: 20px;\n  text-align: center;\n`;\n_c5 = SkillCategory;\nconst SkillGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\n  gap: 20px;\n  justify-items: center;\n`;\n_c6 = SkillGrid;\nconst SkillItem = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  \n  img {\n    width: 50px;\n    height: 50px;\n    margin-bottom: 10px;\n    transition: transform 0.3s ease;\n    \n    &:hover {\n      transform: scale(1.1);\n    }\n  }\n  \n  span {\n    font-size: 0.9rem;\n    color: ${({\n  theme\n}) => theme.color};\n  }\n`;\n_c7 = SkillItem;\nconst Skills = () => {\n  _s();\n  const [skillsData, setSkillsData] = useState(null);\n  useEffect(() => {\n    fetch('/personal-e-portfolio/profile/skills.json').then(response => response.json()).then(data => setSkillsData(data)).catch(error => console.error('Error loading skills data:', error));\n  }, []);\n  if (!skillsData) return null;\n  return /*#__PURE__*/_jsxDEV(SkillsSection, {\n    id: \"skills\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"Skills\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IntroText, {\n        children: skillsData.intro\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: skillsData.skills.map((category, index) => /*#__PURE__*/_jsxDEV(Col, {\n          lg: 4,\n          md: 6,\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Fade, {\n            bottom: true,\n            delay: index * 200,\n            children: /*#__PURE__*/_jsxDEV(SkillCard, {\n              children: [/*#__PURE__*/_jsxDEV(SkillCategory, {\n                children: category.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(SkillGrid, {\n                children: category.items.map((skill, skillIndex) => /*#__PURE__*/_jsxDEV(SkillItem, {\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: `/personal-e-portfolio/${skill.icon}`,\n                    alt: skill.title,\n                    onError: e => {\n                      e.target.src = `https://via.placeholder.com/50x50/3D84C6/ffffff?text=${skill.title.charAt(0)}`;\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: skill.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 25\n                  }, this)]\n                }, skillIndex, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(Skills, \"VIq0q1mKHRyjYWEIX7BsCxt7dhQ=\");\n_c8 = Skills;\nexport default Skills;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"SkillsSection\");\n$RefreshReg$(_c2, \"SectionTitle\");\n$RefreshReg$(_c3, \"IntroText\");\n$RefreshReg$(_c4, \"SkillCard\");\n$RefreshReg$(_c5, \"SkillCategory\");\n$RefreshReg$(_c6, \"SkillGrid\");\n$RefreshReg$(_c7, \"SkillItem\");\n$RefreshReg$(_c8, \"Skills\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Fade", "styled", "jsxDEV", "_jsxDEV", "SkillsSection", "section", "theme", "background", "_c", "SectionTitle", "h2", "color", "accentColor", "_c2", "IntroText", "p", "_c3", "SkillCard", "cardBackground", "cardBorderColor", "_c4", "SkillCategory", "h4", "_c5", "SkillG<PERSON>", "div", "_c6", "SkillItem", "_c7", "Skills", "_s", "skillsData", "setSkillsData", "fetch", "then", "response", "json", "data", "catch", "error", "console", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "intro", "skills", "map", "category", "index", "lg", "md", "className", "bottom", "delay", "title", "items", "skill", "skillIndex", "src", "icon", "alt", "onError", "e", "target", "char<PERSON>t", "_c8", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/Skills.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card } from 'react-bootstrap';\nimport Fade from 'react-reveal/Fade';\nimport styled from 'styled-components';\n\nconst SkillsSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({ theme }) => theme.background};\n  padding: 100px 0;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-align: center;\n  margin-bottom: 30px;\n  color: ${({ theme }) => theme.color};\n  \n  &:after {\n    content: '';\n    display: block;\n    width: 50px;\n    height: 3px;\n    background: ${({ theme }) => theme.accentColor};\n    margin: 20px auto;\n  }\n`;\n\nconst IntroText = styled.p`\n  font-size: 1.1rem;\n  text-align: center;\n  margin-bottom: 50px;\n  color: ${({ theme }) => theme.color};\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n\nconst SkillCard = styled(Card)`\n  background: ${({ theme }) => theme.cardBackground};\n  border: 1px solid ${({ theme }) => theme.cardBorderColor};\n  border-radius: 15px;\n  padding: 20px;\n  margin-bottom: 30px;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  }\n`;\n\nconst SkillCategory = styled.h4`\n  color: ${({ theme }) => theme.accentColor};\n  margin-bottom: 20px;\n  text-align: center;\n`;\n\nconst SkillGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\n  gap: 20px;\n  justify-items: center;\n`;\n\nconst SkillItem = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  \n  img {\n    width: 50px;\n    height: 50px;\n    margin-bottom: 10px;\n    transition: transform 0.3s ease;\n    \n    &:hover {\n      transform: scale(1.1);\n    }\n  }\n  \n  span {\n    font-size: 0.9rem;\n    color: ${({ theme }) => theme.color};\n  }\n`;\n\nconst Skills = () => {\n  const [skillsData, setSkillsData] = useState(null);\n\n  useEffect(() => {\n    fetch('/personal-e-portfolio/profile/skills.json')\n      .then(response => response.json())\n      .then(data => setSkillsData(data))\n      .catch(error => console.error('Error loading skills data:', error));\n  }, []);\n\n  if (!skillsData) return null;\n\n  return (\n    <SkillsSection id=\"skills\">\n      <Container>\n        <SectionTitle>Skills</SectionTitle>\n        <IntroText>\n          {skillsData.intro}\n        </IntroText>\n        <Row>\n          {skillsData.skills.map((category, index) => (\n            <Col lg={4} md={6} key={index} className=\"mb-4\">\n              <Fade bottom delay={index * 200}>\n                <SkillCard>\n                  <SkillCategory>{category.title}</SkillCategory>\n                  <SkillGrid>\n                    {category.items.map((skill, skillIndex) => (\n                      <SkillItem key={skillIndex}>\n                        <img\n                          src={`/personal-e-portfolio/${skill.icon}`}\n                          alt={skill.title}\n                          onError={(e) => {\n                            e.target.src = `https://via.placeholder.com/50x50/3D84C6/ffffff?text=${skill.title.charAt(0)}`;\n                          }}\n                        />\n                        <span>{skill.title}</span>\n                      </SkillItem>\n                    ))}\n                  </SkillGrid>\n                </SkillCard>\n              </Fade>\n            </Col>\n          ))}\n        </Row>\n      </Container>\n    </SkillsSection>\n  );\n};\n\nexport default Skills;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,QAAQ,iBAAiB;AAC3D,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,aAAa,GAAGH,MAAM,CAACI,OAAO;AACpC;AACA;AACA;AACA,gBAAgB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,UAAU;AAC/C;AACA,CAAC;AAACC,EAAA,GANIJ,aAAa;AAQnB,MAAMK,YAAY,GAAGR,MAAM,CAACS,EAAE;AAC9B;AACA;AACA;AACA;AACA,WAAW,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,WAAW;AAClD;AACA;AACA,CAAC;AAACC,GAAA,GAfIJ,YAAY;AAiBlB,MAAMK,SAAS,GAAGb,MAAM,CAACc,CAAC;AAC1B;AACA;AACA;AACA,WAAW,CAAC;EAAET;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA;AACA;AACA,CAAC;AAACK,GAAA,GARIF,SAAS;AAUf,MAAMG,SAAS,GAAGhB,MAAM,CAACF,IAAI,CAAC;AAC9B,gBAAgB,CAAC;EAAEO;AAAM,CAAC,KAAKA,KAAK,CAACY,cAAc;AACnD,sBAAsB,CAAC;EAAEZ;AAAM,CAAC,KAAKA,KAAK,CAACa,eAAe;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIH,SAAS;AAcf,MAAMI,aAAa,GAAGpB,MAAM,CAACqB,EAAE;AAC/B,WAAW,CAAC;EAAEhB;AAAM,CAAC,KAAKA,KAAK,CAACM,WAAW;AAC3C;AACA;AACA,CAAC;AAACW,GAAA,GAJIF,aAAa;AAMnB,MAAMG,SAAS,GAAGvB,MAAM,CAACwB,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,SAAS;AAOf,MAAMG,SAAS,GAAG1B,MAAM,CAACwB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,CAAC;EAAEnB;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACvC;AACA,CAAC;AAACiB,GAAA,GArBID,SAAS;AAuBf,MAAME,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAElDC,SAAS,CAAC,MAAM;IACdsC,KAAK,CAAC,2CAA2C,CAAC,CAC/CC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAIL,aAAa,CAACK,IAAI,CAAC,CAAC,CACjCC,KAAK,CAACC,KAAK,IAAIC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC,CAAC;EACvE,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACR,UAAU,EAAE,OAAO,IAAI;EAE5B,oBACE5B,OAAA,CAACC,aAAa;IAACqC,EAAE,EAAC,QAAQ;IAAAC,QAAA,eACxBvC,OAAA,CAACP,SAAS;MAAA8C,QAAA,gBACRvC,OAAA,CAACM,YAAY;QAAAiC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACnC3C,OAAA,CAACW,SAAS;QAAA4B,QAAA,EACPX,UAAU,CAACgB;MAAK;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACZ3C,OAAA,CAACN,GAAG;QAAA6C,QAAA,EACDX,UAAU,CAACiB,MAAM,CAACC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBACrChD,OAAA,CAACL,GAAG;UAACsD,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAaC,SAAS,EAAC,MAAM;UAAAZ,QAAA,eAC7CvC,OAAA,CAACH,IAAI;YAACuD,MAAM;YAACC,KAAK,EAAEL,KAAK,GAAG,GAAI;YAAAT,QAAA,eAC9BvC,OAAA,CAACc,SAAS;cAAAyB,QAAA,gBACRvC,OAAA,CAACkB,aAAa;gBAAAqB,QAAA,EAAEQ,QAAQ,CAACO;cAAK;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAgB,CAAC,eAC/C3C,OAAA,CAACqB,SAAS;gBAAAkB,QAAA,EACPQ,QAAQ,CAACQ,KAAK,CAACT,GAAG,CAAC,CAACU,KAAK,EAAEC,UAAU,kBACpCzD,OAAA,CAACwB,SAAS;kBAAAe,QAAA,gBACRvC,OAAA;oBACE0D,GAAG,EAAE,yBAAyBF,KAAK,CAACG,IAAI,EAAG;oBAC3CC,GAAG,EAAEJ,KAAK,CAACF,KAAM;oBACjBO,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,wDAAwDF,KAAK,CAACF,KAAK,CAACU,MAAM,CAAC,CAAC,CAAC,EAAE;oBAChG;kBAAE;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF3C,OAAA;oBAAAuC,QAAA,EAAOiB,KAAK,CAACF;kBAAK;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GARZc,UAAU;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASf,CACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC,GAnBeK,KAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBxB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEpB,CAAC;AAAChB,EAAA,CA/CID,MAAM;AAAAuC,GAAA,GAANvC,MAAM;AAiDZ,eAAeA,MAAM;AAAC,IAAArB,EAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAwC,GAAA;AAAAC,YAAA,CAAA7D,EAAA;AAAA6D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAArD,GAAA;AAAAqD,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}