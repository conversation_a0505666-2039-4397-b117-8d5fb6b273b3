{"ast": null, "code": "import useEventListener from './useEventListener';\nimport { useCallback } from 'react';\n/**\n * Attaches an event handler outside directly to the `document`,\n * bypassing the react synthetic event system.\n *\n * ```ts\n * useGlobalListener('keydown', (event) => {\n *  console.log(event.key)\n * })\n * ```\n *\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */\nexport default function useGlobalListener(event, handler, capture = false) {\n  const documentTarget = useCallback(() => document, []);\n  return useEventListener(documentTarget, event, handler, capture);\n}", "map": {"version": 3, "names": ["useEventListener", "useCallback", "useGlobalListener", "event", "handler", "capture", "documentTarget", "document"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useGlobalListener.js"], "sourcesContent": ["import useEventListener from './useEventListener';\nimport { useCallback } from 'react';\n/**\n * Attaches an event handler outside directly to the `document`,\n * bypassing the react synthetic event system.\n *\n * ```ts\n * useGlobalListener('keydown', (event) => {\n *  console.log(event.key)\n * })\n * ```\n *\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */\nexport default function useGlobalListener(event, handler, capture = false) {\n  const documentTarget = useCallback(() => document, []);\n  return useEventListener(documentTarget, event, handler, capture);\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,WAAW,QAAQ,OAAO;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,GAAG,KAAK,EAAE;EACzE,MAAMC,cAAc,GAAGL,WAAW,CAAC,MAAMM,QAAQ,EAAE,EAAE,CAAC;EACtD,OAAOP,gBAAgB,CAACM,cAAc,EAAEH,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}