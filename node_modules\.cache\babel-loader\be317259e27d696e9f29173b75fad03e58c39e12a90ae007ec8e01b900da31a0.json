{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction toPercent(num) {\n  if (num <= 0) return '100%';\n  if (num < 1) return `${num * 100}%`;\n  return `${num}%`;\n}\nconst Ratio = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  aspectRatio = '1x1',\n  style,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'ratio');\n  const isCustomRatio = typeof aspectRatio === 'number';\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...props,\n    style: {\n      ...style,\n      ...(isCustomRatio && {\n        '--bs-aspect-ratio': toPercent(aspectRatio)\n      })\n    },\n    className: classNames(bsPrefix, className, !isCustomRatio && `${bsPrefix}-${aspectRatio}`),\n    children: React.Children.only(children)\n  });\n});\nRatio.displayName = 'Ratio';\nexport default Ratio;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "toPercent", "num", "<PERSON><PERSON>", "forwardRef", "bsPrefix", "className", "children", "aspectRatio", "style", "props", "ref", "isCustomRatio", "Children", "only", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/Ratio.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction toPercent(num) {\n  if (num <= 0) return '100%';\n  if (num < 1) return `${num * 100}%`;\n  return `${num}%`;\n}\nconst Ratio = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  aspectRatio = '1x1',\n  style,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'ratio');\n  const isCustomRatio = typeof aspectRatio === 'number';\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...props,\n    style: {\n      ...style,\n      ...(isCustomRatio && {\n        '--bs-aspect-ratio': toPercent(aspectRatio)\n      })\n    },\n    className: classNames(bsPrefix, className, !isCustomRatio && `${bsPrefix}-${aspectRatio}`),\n    children: React.Children.only(children)\n  });\n});\nRatio.displayName = 'Ratio';\nexport default Ratio;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,MAAM;EAC3B,IAAIA,GAAG,GAAG,CAAC,EAAE,OAAO,GAAGA,GAAG,GAAG,GAAG,GAAG;EACnC,OAAO,GAAGA,GAAG,GAAG;AAClB;AACA,MAAMC,KAAK,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EAC3CC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACRC,WAAW,GAAG,KAAK;EACnBC,KAAK;EACL,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTN,QAAQ,GAAGP,kBAAkB,CAACO,QAAQ,EAAE,OAAO,CAAC;EAChD,MAAMO,aAAa,GAAG,OAAOJ,WAAW,KAAK,QAAQ;EACrD,OAAO,aAAaR,IAAI,CAAC,KAAK,EAAE;IAC9BW,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRD,KAAK,EAAE;MACL,GAAGA,KAAK;MACR,IAAIG,aAAa,IAAI;QACnB,mBAAmB,EAAEX,SAAS,CAACO,WAAW;MAC5C,CAAC;IACH,CAAC;IACDF,SAAS,EAAEV,UAAU,CAACS,QAAQ,EAAEC,SAAS,EAAE,CAACM,aAAa,IAAI,GAAGP,QAAQ,IAAIG,WAAW,EAAE,CAAC;IAC1FD,QAAQ,EAAEV,KAAK,CAACgB,QAAQ,CAACC,IAAI,CAACP,QAAQ;EACxC,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,KAAK,CAACY,WAAW,GAAG,OAAO;AAC3B,eAAeZ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}