import React from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON>, Button } from 'react-bootstrap';
import styled from 'styled-components';

const ProjectsSection = styled.section`
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: ${({ theme }) => theme.background};
  padding: 100px 0;
`;

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 50px;
  color: ${({ theme }) => theme.color};
  
  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background: ${({ theme }) => theme.accentColor};
    margin: 20px auto;
  }
`;

const ProjectCard = styled(Card)`
  background: ${({ theme }) => theme.cardBackground};
  border: 1px solid ${({ theme }) => theme.cardBorderColor};
  border-radius: 15px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
`;

const ProjectTitle = styled(Card.Title)`
  color: ${({ theme }) => theme.color};
  font-weight: 600;
`;

const ProjectText = styled(Card.Text)`
  color: ${({ theme }) => theme.color};
  opacity: 0.8;
`;

const Projects = () => {
  // Sample project data
  const sampleProjects = [
    {
      title: "Personal E-Portfolio",
      description: "A responsive portfolio website built with React, featuring dark mode, smooth animations, and modern design.",
      technologies: ["React", "Bootstrap", "Styled Components"],
      links: [
        { text: "Live Demo", href: "#" },
        { text: "GitHub", href: "#" }
      ]
    },
    {
      title: "Learning Tracker",
      description: "An application to track daily learning outcomes and progress in various technologies and skills.",
      technologies: ["JavaScript", "HTML", "CSS"],
      links: [
        { text: "GitHub", href: "#" }
      ]
    }
  ];

  return (
    <ProjectsSection id="projects">
      <Container>
        <SectionTitle>Projects</SectionTitle>
        <Row>
          {sampleProjects.map((project, index) => (
            <Col lg={6} md={6} key={index} className="mb-4">
              <ProjectCard>
                <Card.Body>
                  <ProjectTitle>{project.title}</ProjectTitle>
                  <ProjectText>{project.description}</ProjectText>
                  <div className="mb-3">
                    {project.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="badge bg-primary me-2 mb-2"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                  <div>
                    {project.links.map((link, linkIndex) => (
                      <Button
                        key={linkIndex}
                        variant="outline-primary"
                        size="sm"
                        className="me-2"
                        href={link.href}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {link.text}
                      </Button>
                    ))}
                  </div>
                </Card.Body>
              </ProjectCard>
            </Col>
          ))}
        </Row>
      </Container>
    </ProjectsSection>
  );
};

export default Projects;
