{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { cloneElement } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { map } from './ElementChildren';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ROUND_PRECISION = 1000;\n\n/**\n * Validate that children, if any, are instances of `ProgressBar`.\n */\nfunction onlyProgressBar(props, propName, componentName) {\n  const children = props[propName];\n  if (!children) {\n    return null;\n  }\n  let error = null;\n  React.Children.forEach(children, child => {\n    if (error) {\n      return;\n    }\n\n    /**\n     * Compare types in a way that works with libraries that patch and proxy\n     * components like react-hot-loader.\n     *\n     * see https://github.com/gaearon/react-hot-loader#checking-element-types\n     */\n    const element = /*#__PURE__*/_jsx(ProgressBar, {});\n    if (child.type === element.type) return;\n    const childType = child.type;\n    const childIdentifier = /*#__PURE__*/React.isValidElement(child) ? childType.displayName || childType.name || childType : child;\n    error = new Error(`Children of ${componentName} can contain only ProgressBar ` + `components. Found ${childIdentifier}.`);\n  });\n  return error;\n}\nfunction getPercentage(now, min, max) {\n  const percentage = (now - min) / (max - min) * 100;\n  return Math.round(percentage * ROUND_PRECISION) / ROUND_PRECISION;\n}\nfunction renderProgressBar({\n  min,\n  now,\n  max,\n  label,\n  visuallyHidden,\n  striped,\n  animated,\n  className,\n  style,\n  variant,\n  bsPrefix,\n  ...props\n}, ref) {\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...props,\n    role: \"progressbar\",\n    className: classNames(className, `${bsPrefix}-bar`, {\n      [`bg-${variant}`]: variant,\n      [`${bsPrefix}-bar-animated`]: animated,\n      [`${bsPrefix}-bar-striped`]: animated || striped\n    }),\n    style: {\n      width: `${getPercentage(now, min, max)}%`,\n      ...style\n    },\n    \"aria-valuenow\": now,\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    children: visuallyHidden ? /*#__PURE__*/_jsx(\"span\", {\n      className: \"visually-hidden\",\n      children: label\n    }) : label\n  });\n}\nconst ProgressBar = /*#__PURE__*/React.forwardRef(({\n  isChild = false,\n  ...rest\n}, ref) => {\n  const props = {\n    min: 0,\n    max: 100,\n    animated: false,\n    visuallyHidden: false,\n    striped: false,\n    ...rest\n  };\n  props.bsPrefix = useBootstrapPrefix(props.bsPrefix, 'progress');\n  if (isChild) {\n    return renderProgressBar(props, ref);\n  }\n  const {\n    min,\n    now,\n    max,\n    label,\n    visuallyHidden,\n    striped,\n    animated,\n    bsPrefix,\n    variant,\n    className,\n    children,\n    ...wrapperProps\n  } = props;\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...wrapperProps,\n    className: classNames(className, bsPrefix),\n    children: children ? map(children, child => /*#__PURE__*/cloneElement(child, {\n      isChild: true\n    })) : renderProgressBar({\n      min,\n      now,\n      max,\n      label,\n      visuallyHidden,\n      striped,\n      animated,\n      bsPrefix,\n      variant\n    }, ref)\n  });\n});\nProgressBar.displayName = 'ProgressBar';\nexport default ProgressBar;", "map": {"version": 3, "names": ["classNames", "React", "cloneElement", "useBootstrapPrefix", "map", "jsx", "_jsx", "ROUND_PRECISION", "onlyProgressBar", "props", "propName", "componentName", "children", "error", "Children", "for<PERSON>ach", "child", "element", "ProgressBar", "type", "childType", "childIdentifier", "isValidElement", "displayName", "name", "Error", "getPercentage", "now", "min", "max", "percentage", "Math", "round", "renderProgressBar", "label", "visuallyHidden", "striped", "animated", "className", "style", "variant", "bsPrefix", "ref", "role", "width", "forwardRef", "<PERSON><PERSON><PERSON><PERSON>", "rest", "wrapperProps"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/ProgressBar.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { cloneElement } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { map } from './ElementChildren';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ROUND_PRECISION = 1000;\n\n/**\n * Validate that children, if any, are instances of `ProgressBar`.\n */\nfunction onlyProgressBar(props, propName, componentName) {\n  const children = props[propName];\n  if (!children) {\n    return null;\n  }\n  let error = null;\n  React.Children.forEach(children, child => {\n    if (error) {\n      return;\n    }\n\n    /**\n     * Compare types in a way that works with libraries that patch and proxy\n     * components like react-hot-loader.\n     *\n     * see https://github.com/gaearon/react-hot-loader#checking-element-types\n     */\n    const element = /*#__PURE__*/_jsx(ProgressBar, {});\n    if (child.type === element.type) return;\n    const childType = child.type;\n    const childIdentifier = /*#__PURE__*/React.isValidElement(child) ? childType.displayName || childType.name || childType : child;\n    error = new Error(`Children of ${componentName} can contain only ProgressBar ` + `components. Found ${childIdentifier}.`);\n  });\n  return error;\n}\nfunction getPercentage(now, min, max) {\n  const percentage = (now - min) / (max - min) * 100;\n  return Math.round(percentage * ROUND_PRECISION) / ROUND_PRECISION;\n}\nfunction renderProgressBar({\n  min,\n  now,\n  max,\n  label,\n  visuallyHidden,\n  striped,\n  animated,\n  className,\n  style,\n  variant,\n  bsPrefix,\n  ...props\n}, ref) {\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...props,\n    role: \"progressbar\",\n    className: classNames(className, `${bsPrefix}-bar`, {\n      [`bg-${variant}`]: variant,\n      [`${bsPrefix}-bar-animated`]: animated,\n      [`${bsPrefix}-bar-striped`]: animated || striped\n    }),\n    style: {\n      width: `${getPercentage(now, min, max)}%`,\n      ...style\n    },\n    \"aria-valuenow\": now,\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    children: visuallyHidden ? /*#__PURE__*/_jsx(\"span\", {\n      className: \"visually-hidden\",\n      children: label\n    }) : label\n  });\n}\nconst ProgressBar = /*#__PURE__*/React.forwardRef(({\n  isChild = false,\n  ...rest\n}, ref) => {\n  const props = {\n    min: 0,\n    max: 100,\n    animated: false,\n    visuallyHidden: false,\n    striped: false,\n    ...rest\n  };\n  props.bsPrefix = useBootstrapPrefix(props.bsPrefix, 'progress');\n  if (isChild) {\n    return renderProgressBar(props, ref);\n  }\n  const {\n    min,\n    now,\n    max,\n    label,\n    visuallyHidden,\n    striped,\n    animated,\n    bsPrefix,\n    variant,\n    className,\n    children,\n    ...wrapperProps\n  } = props;\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...wrapperProps,\n    className: classNames(className, bsPrefix),\n    children: children ? map(children, child => /*#__PURE__*/cloneElement(child, {\n      isChild: true\n    })) : renderProgressBar({\n      min,\n      now,\n      max,\n      label,\n      visuallyHidden,\n      striped,\n      animated,\n      bsPrefix,\n      variant\n    }, ref)\n  });\n});\nProgressBar.displayName = 'ProgressBar';\nexport default ProgressBar;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,OAAO;AACpC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,QAAQ,mBAAmB;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,eAAe,GAAG,IAAI;;AAE5B;AACA;AACA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EACvD,MAAMC,QAAQ,GAAGH,KAAK,CAACC,QAAQ,CAAC;EAChC,IAAI,CAACE,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,IAAIC,KAAK,GAAG,IAAI;EAChBZ,KAAK,CAACa,QAAQ,CAACC,OAAO,CAACH,QAAQ,EAAEI,KAAK,IAAI;IACxC,IAAIH,KAAK,EAAE;MACT;IACF;;IAEA;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMI,OAAO,GAAG,aAAaX,IAAI,CAACY,WAAW,EAAE,CAAC,CAAC,CAAC;IAClD,IAAIF,KAAK,CAACG,IAAI,KAAKF,OAAO,CAACE,IAAI,EAAE;IACjC,MAAMC,SAAS,GAAGJ,KAAK,CAACG,IAAI;IAC5B,MAAME,eAAe,GAAG,aAAapB,KAAK,CAACqB,cAAc,CAACN,KAAK,CAAC,GAAGI,SAAS,CAACG,WAAW,IAAIH,SAAS,CAACI,IAAI,IAAIJ,SAAS,GAAGJ,KAAK;IAC/HH,KAAK,GAAG,IAAIY,KAAK,CAAC,eAAed,aAAa,gCAAgC,GAAG,qBAAqBU,eAAe,GAAG,CAAC;EAC3H,CAAC,CAAC;EACF,OAAOR,KAAK;AACd;AACA,SAASa,aAAaA,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACpC,MAAMC,UAAU,GAAG,CAACH,GAAG,GAAGC,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAG,GAAG;EAClD,OAAOG,IAAI,CAACC,KAAK,CAACF,UAAU,GAAGvB,eAAe,CAAC,GAAGA,eAAe;AACnE;AACA,SAAS0B,iBAAiBA,CAAC;EACzBL,GAAG;EACHD,GAAG;EACHE,GAAG;EACHK,KAAK;EACLC,cAAc;EACdC,OAAO;EACPC,QAAQ;EACRC,SAAS;EACTC,KAAK;EACLC,OAAO;EACPC,QAAQ;EACR,GAAGhC;AACL,CAAC,EAAEiC,GAAG,EAAE;EACN,OAAO,aAAapC,IAAI,CAAC,KAAK,EAAE;IAC9BoC,GAAG,EAAEA,GAAG;IACR,GAAGjC,KAAK;IACRkC,IAAI,EAAE,aAAa;IACnBL,SAAS,EAAEtC,UAAU,CAACsC,SAAS,EAAE,GAAGG,QAAQ,MAAM,EAAE;MAClD,CAAC,MAAMD,OAAO,EAAE,GAAGA,OAAO;MAC1B,CAAC,GAAGC,QAAQ,eAAe,GAAGJ,QAAQ;MACtC,CAAC,GAAGI,QAAQ,cAAc,GAAGJ,QAAQ,IAAID;IAC3C,CAAC,CAAC;IACFG,KAAK,EAAE;MACLK,KAAK,EAAE,GAAGlB,aAAa,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC,GAAG;MACzC,GAAGU;IACL,CAAC;IACD,eAAe,EAAEZ,GAAG;IACpB,eAAe,EAAEC,GAAG;IACpB,eAAe,EAAEC,GAAG;IACpBjB,QAAQ,EAAEuB,cAAc,GAAG,aAAa7B,IAAI,CAAC,MAAM,EAAE;MACnDgC,SAAS,EAAE,iBAAiB;MAC5B1B,QAAQ,EAAEsB;IACZ,CAAC,CAAC,GAAGA;EACP,CAAC,CAAC;AACJ;AACA,MAAMhB,WAAW,GAAG,aAAajB,KAAK,CAAC4C,UAAU,CAAC,CAAC;EACjDC,OAAO,GAAG,KAAK;EACf,GAAGC;AACL,CAAC,EAAEL,GAAG,KAAK;EACT,MAAMjC,KAAK,GAAG;IACZmB,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,GAAG;IACRQ,QAAQ,EAAE,KAAK;IACfF,cAAc,EAAE,KAAK;IACrBC,OAAO,EAAE,KAAK;IACd,GAAGW;EACL,CAAC;EACDtC,KAAK,CAACgC,QAAQ,GAAGtC,kBAAkB,CAACM,KAAK,CAACgC,QAAQ,EAAE,UAAU,CAAC;EAC/D,IAAIK,OAAO,EAAE;IACX,OAAOb,iBAAiB,CAACxB,KAAK,EAAEiC,GAAG,CAAC;EACtC;EACA,MAAM;IACJd,GAAG;IACHD,GAAG;IACHE,GAAG;IACHK,KAAK;IACLC,cAAc;IACdC,OAAO;IACPC,QAAQ;IACRI,QAAQ;IACRD,OAAO;IACPF,SAAS;IACT1B,QAAQ;IACR,GAAGoC;EACL,CAAC,GAAGvC,KAAK;EACT,OAAO,aAAaH,IAAI,CAAC,KAAK,EAAE;IAC9BoC,GAAG,EAAEA,GAAG;IACR,GAAGM,YAAY;IACfV,SAAS,EAAEtC,UAAU,CAACsC,SAAS,EAAEG,QAAQ,CAAC;IAC1C7B,QAAQ,EAAEA,QAAQ,GAAGR,GAAG,CAACQ,QAAQ,EAAEI,KAAK,IAAI,aAAad,YAAY,CAACc,KAAK,EAAE;MAC3E8B,OAAO,EAAE;IACX,CAAC,CAAC,CAAC,GAAGb,iBAAiB,CAAC;MACtBL,GAAG;MACHD,GAAG;MACHE,GAAG;MACHK,KAAK;MACLC,cAAc;MACdC,OAAO;MACPC,QAAQ;MACRI,QAAQ;MACRD;IACF,CAAC,EAAEE,GAAG;EACR,CAAC,CAAC;AACJ,CAAC,CAAC;AACFxB,WAAW,CAACK,WAAW,GAAG,aAAa;AACvC,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}