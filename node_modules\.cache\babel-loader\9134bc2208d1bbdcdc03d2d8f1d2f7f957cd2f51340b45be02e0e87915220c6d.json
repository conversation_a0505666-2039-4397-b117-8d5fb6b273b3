{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\n\n// TODO\n\nconst FormContext = /*#__PURE__*/React.createContext({});\nexport default FormContext;", "map": {"version": 3, "names": ["React", "FormContext", "createContext"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/FormContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\n\n// TODO\n\nconst FormContext = /*#__PURE__*/React.createContext({});\nexport default FormContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;;AAEA,MAAMC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACxD,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}