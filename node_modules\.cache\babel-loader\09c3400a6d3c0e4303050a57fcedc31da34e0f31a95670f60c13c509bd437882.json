{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_groupme = register(\"groupme\", {\n  \"color\": \"#00aff0\",\n  \"path\": \"M0 0v64h64V0zm40.321 39.434a10.4 9.517 0 0 1-16.64 0 2.6 2.38 0 1 0-4.161 2.856 15.6 14.276 0 0 0 24.961 0 2.6 2.38 0 0 0-4.16-2.856m-17.42-12.848a2.6 2.38 0 0 0 0 4.759h1.3v1.19a2.6 2.38 0 0 0 5.2 0v-1.19h5.2v1.19a2.6 2.38 0 0 0 5.2 0v-1.19h1.3a2.6 2.38 0 0 0 0-4.759h-1.3v-4.758h1.3a2.6 2.38 0 0 0 0-4.759h-1.3v-1.19a2.6 2.38 0 0 0-5.2 0v1.19h-5.2v-1.19a2.6 2.38 0 0 0-5.2 0v1.19h-1.3a2.6 2.38 0 0 0 0 4.759h1.3v4.758zm6.5-4.758h5.2v4.758h-5.2z\"\n});\nexport { _socialIcons_groupme as default };", "map": {"version": 3, "names": ["register", "_socialIcons_groupme", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/groupme.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_groupme = register(\"groupme\", {\"color\":\"#00aff0\",\"path\":\"M0 0v64h64V0zm40.321 39.434a10.4 9.517 0 0 1-16.64 0 2.6 2.38 0 1 0-4.161 2.856 15.6 14.276 0 0 0 24.961 0 2.6 2.38 0 0 0-4.16-2.856m-17.42-12.848a2.6 2.38 0 0 0 0 4.759h1.3v1.19a2.6 2.38 0 0 0 5.2 0v-1.19h5.2v1.19a2.6 2.38 0 0 0 5.2 0v-1.19h1.3a2.6 2.38 0 0 0 0-4.759h-1.3v-4.758h1.3a2.6 2.38 0 0 0 0-4.759h-1.3v-1.19a2.6 2.38 0 0 0-5.2 0v1.19h-5.2v-1.19a2.6 2.38 0 0 0-5.2 0v1.19h-1.3a2.6 2.38 0 0 0 0 4.759h1.3v4.758zm6.5-4.758h5.2v4.758h-5.2z\"});\n\nexport { _socialIcons_groupme as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,oBAAoB,GAAGD,QAAQ,CAAC,SAAS,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAgc,CAAC,CAAC;AAE3gB,SAASC,oBAAoB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}