{"ast": null, "code": "import * as React from 'react';\nimport { useMemo } from 'react';\nimport { useUncontrolledProp } from 'uncontrollable';\nimport { useSSRSafeId } from './ssr';\nimport TabContext from './TabContext';\nimport SelectableContext from './SelectableContext';\nimport TabPanel from './TabPanel';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Tabs = props => {\n  const {\n    id: userId,\n    generateChildId: generateCustomChildId,\n    onSelect: propsOnSelect,\n    activeKey: propsActiveKey,\n    defaultActiveKey,\n    transition,\n    mountOnEnter,\n    unmountOnExit,\n    children\n  } = props;\n  const [activeKey, onSelect] = useUncontrolledProp(propsActiveKey, defaultActiveKey, propsOnSelect);\n  const id = useSSRSafeId(userId);\n  const generateChildId = useMemo(() => generateCustomChildId || ((key, type) => id ? `${id}-${type}-${key}` : null), [id, generateCustomChildId]);\n  const tabContext = useMemo(() => ({\n    onSelect,\n    activeKey,\n    transition,\n    mountOnEnter: mountOnEnter || false,\n    unmountOnExit: unmountOnExit || false,\n    getControlledId: key => generateChildId(key, 'tabpane'),\n    getControllerId: key => generateChildId(key, 'tab')\n  }), [onSelect, activeKey, transition, mountOnEnter, unmountOnExit, generateChildId]);\n  return /*#__PURE__*/_jsx(TabContext.Provider, {\n    value: tabContext,\n    children: /*#__PURE__*/_jsx(SelectableContext.Provider, {\n      value: onSelect || null,\n      children: children\n    })\n  });\n};\nTabs.Panel = TabPanel;\nexport default Tabs;", "map": {"version": 3, "names": ["React", "useMemo", "useUncontrolledProp", "useSSRSafeId", "TabContext", "SelectableContext", "TabPanel", "jsx", "_jsx", "Tabs", "props", "id", "userId", "generateChildId", "generateCustomChildId", "onSelect", "propsOnSelect", "active<PERSON><PERSON>", "propsActiveKey", "defaultActiveKey", "transition", "mountOnEnter", "unmountOnExit", "children", "key", "type", "tabContext", "getControlledId", "getControllerId", "Provider", "value", "Panel"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/@restart/ui/esm/Tabs.js"], "sourcesContent": ["import * as React from 'react';\nimport { useMemo } from 'react';\nimport { useUncontrolledProp } from 'uncontrollable';\nimport { useSSRSafeId } from './ssr';\nimport TabContext from './TabContext';\nimport SelectableContext from './SelectableContext';\nimport TabPanel from './TabPanel';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Tabs = props => {\n  const {\n    id: userId,\n    generateChildId: generateCustomChildId,\n    onSelect: propsOnSelect,\n    activeKey: propsActiveKey,\n    defaultActiveKey,\n    transition,\n    mountOnEnter,\n    unmountOnExit,\n    children\n  } = props;\n  const [activeKey, onSelect] = useUncontrolledProp(propsActiveKey, defaultActiveKey, propsOnSelect);\n  const id = useSSRSafeId(userId);\n  const generateChildId = useMemo(() => generateCustomChildId || ((key, type) => id ? `${id}-${type}-${key}` : null), [id, generateCustomChildId]);\n  const tabContext = useMemo(() => ({\n    onSelect,\n    activeKey,\n    transition,\n    mountOnEnter: mountOnEnter || false,\n    unmountOnExit: unmountOnExit || false,\n    getControlledId: key => generateChildId(key, 'tabpane'),\n    getControllerId: key => generateChildId(key, 'tab')\n  }), [onSelect, activeKey, transition, mountOnEnter, unmountOnExit, generateChildId]);\n  return /*#__PURE__*/_jsx(TabContext.Provider, {\n    value: tabContext,\n    children: /*#__PURE__*/_jsx(SelectableContext.Provider, {\n      value: onSelect || null,\n      children: children\n    })\n  });\n};\nTabs.Panel = TabPanel;\nexport default Tabs;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,YAAY,QAAQ,OAAO;AACpC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,IAAI,GAAGC,KAAK,IAAI;EACpB,MAAM;IACJC,EAAE,EAAEC,MAAM;IACVC,eAAe,EAAEC,qBAAqB;IACtCC,QAAQ,EAAEC,aAAa;IACvBC,SAAS,EAAEC,cAAc;IACzBC,gBAAgB;IAChBC,UAAU;IACVC,YAAY;IACZC,aAAa;IACbC;EACF,CAAC,GAAGb,KAAK;EACT,MAAM,CAACO,SAAS,EAAEF,QAAQ,CAAC,GAAGb,mBAAmB,CAACgB,cAAc,EAAEC,gBAAgB,EAAEH,aAAa,CAAC;EAClG,MAAML,EAAE,GAAGR,YAAY,CAACS,MAAM,CAAC;EAC/B,MAAMC,eAAe,GAAGZ,OAAO,CAAC,MAAMa,qBAAqB,KAAK,CAACU,GAAG,EAAEC,IAAI,KAAKd,EAAE,GAAG,GAAGA,EAAE,IAAIc,IAAI,IAAID,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,CAACb,EAAE,EAAEG,qBAAqB,CAAC,CAAC;EAChJ,MAAMY,UAAU,GAAGzB,OAAO,CAAC,OAAO;IAChCc,QAAQ;IACRE,SAAS;IACTG,UAAU;IACVC,YAAY,EAAEA,YAAY,IAAI,KAAK;IACnCC,aAAa,EAAEA,aAAa,IAAI,KAAK;IACrCK,eAAe,EAAEH,GAAG,IAAIX,eAAe,CAACW,GAAG,EAAE,SAAS,CAAC;IACvDI,eAAe,EAAEJ,GAAG,IAAIX,eAAe,CAACW,GAAG,EAAE,KAAK;EACpD,CAAC,CAAC,EAAE,CAACT,QAAQ,EAAEE,SAAS,EAAEG,UAAU,EAAEC,YAAY,EAAEC,aAAa,EAAET,eAAe,CAAC,CAAC;EACpF,OAAO,aAAaL,IAAI,CAACJ,UAAU,CAACyB,QAAQ,EAAE;IAC5CC,KAAK,EAAEJ,UAAU;IACjBH,QAAQ,EAAE,aAAaf,IAAI,CAACH,iBAAiB,CAACwB,QAAQ,EAAE;MACtDC,KAAK,EAAEf,QAAQ,IAAI,IAAI;MACvBQ,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AACDd,IAAI,CAACsB,KAAK,GAAGzB,QAAQ;AACrB,eAAeG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}