{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ENTERED, ENTERING, EXITING } from 'react-transition-group/Transition';\nimport { getChildRef } from '@restart/ui/utils';\nimport transitionEndListener from './transitionEndListener';\nimport TransitionWrapper from './TransitionWrapper';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst transitionStyles = {\n  [ENTERING]: 'show',\n  [ENTERED]: 'show'\n};\nconst OffcanvasToggling = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  in: inProp = false,\n  mountOnEnter = false,\n  unmountOnExit = false,\n  appear = false,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas');\n  return /*#__PURE__*/_jsx(TransitionWrapper, {\n    ref: ref,\n    addEndListener: transitionEndListener,\n    in: inProp,\n    mountOnEnter: mountOnEnter,\n    unmountOnExit: unmountOnExit,\n    appear: appear,\n    ...props,\n    childRef: getChildRef(children),\n    children: (status, innerProps) => /*#__PURE__*/React.cloneElement(children, {\n      ...innerProps,\n      className: classNames(className, children.props.className, (status === ENTERING || status === EXITING) && `${bsPrefix}-toggling`, transitionStyles[status])\n    })\n  });\n});\nOffcanvasToggling.displayName = 'OffcanvasToggling';\nexport default OffcanvasToggling;", "map": {"version": 3, "names": ["classNames", "React", "ENTERED", "ENTERING", "EXITING", "getChildRef", "transitionEndListener", "TransitionWrapper", "useBootstrapPrefix", "jsx", "_jsx", "transitionStyles", "OffcanvasToggling", "forwardRef", "bsPrefix", "className", "children", "in", "inProp", "mountOnEnter", "unmountOnExit", "appear", "props", "ref", "addEndListener", "childRef", "status", "innerProps", "cloneElement", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/OffcanvasToggling.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ENTERED, ENTERING, EXITING } from 'react-transition-group/Transition';\nimport { getChildRef } from '@restart/ui/utils';\nimport transitionEndListener from './transitionEndListener';\nimport TransitionWrapper from './TransitionWrapper';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst transitionStyles = {\n  [ENTERING]: 'show',\n  [ENTERED]: 'show'\n};\nconst OffcanvasToggling = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  in: inProp = false,\n  mountOnEnter = false,\n  unmountOnExit = false,\n  appear = false,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas');\n  return /*#__PURE__*/_jsx(TransitionWrapper, {\n    ref: ref,\n    addEndListener: transitionEndListener,\n    in: inProp,\n    mountOnEnter: mountOnEnter,\n    unmountOnExit: unmountOnExit,\n    appear: appear,\n    ...props,\n    childRef: getChildRef(children),\n    children: (status, innerProps) => /*#__PURE__*/React.cloneElement(children, {\n      ...innerProps,\n      className: classNames(className, children.props.className, (status === ENTERING || status === EXITING) && `${bsPrefix}-toggling`, transitionStyles[status])\n    })\n  });\n});\nOffcanvasToggling.displayName = 'OffcanvasToggling';\nexport default OffcanvasToggling;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,mCAAmC;AAC9E,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,gBAAgB,GAAG;EACvB,CAACR,QAAQ,GAAG,MAAM;EAClB,CAACD,OAAO,GAAG;AACb,CAAC;AACD,MAAMU,iBAAiB,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,CAAC;EACvDC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,MAAM,GAAG,KAAK;EAClBC,YAAY,GAAG,KAAK;EACpBC,aAAa,GAAG,KAAK;EACrBC,MAAM,GAAG,KAAK;EACd,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTT,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,WAAW,CAAC;EACpD,OAAO,aAAaJ,IAAI,CAACH,iBAAiB,EAAE;IAC1CgB,GAAG,EAAEA,GAAG;IACRC,cAAc,EAAElB,qBAAqB;IACrCW,EAAE,EAAEC,MAAM;IACVC,YAAY,EAAEA,YAAY;IAC1BC,aAAa,EAAEA,aAAa;IAC5BC,MAAM,EAAEA,MAAM;IACd,GAAGC,KAAK;IACRG,QAAQ,EAAEpB,WAAW,CAACW,QAAQ,CAAC;IAC/BA,QAAQ,EAAEA,CAACU,MAAM,EAAEC,UAAU,KAAK,aAAa1B,KAAK,CAAC2B,YAAY,CAACZ,QAAQ,EAAE;MAC1E,GAAGW,UAAU;MACbZ,SAAS,EAAEf,UAAU,CAACe,SAAS,EAAEC,QAAQ,CAACM,KAAK,CAACP,SAAS,EAAE,CAACW,MAAM,KAAKvB,QAAQ,IAAIuB,MAAM,KAAKtB,OAAO,KAAK,GAAGU,QAAQ,WAAW,EAAEH,gBAAgB,CAACe,MAAM,CAAC;IAC5J,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFd,iBAAiB,CAACiB,WAAW,GAAG,mBAAmB;AACnD,eAAejB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}