{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\ResumePage.js\";\nimport React from 'react';\nimport './ResumePage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResumePage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"resume-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"My Resume\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Click the link below to download my resume:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n      href: \"/resume.pdf\",\n      download: true,\n      children: \"Download Resume\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 9\n  }, this);\n};\n_c = ResumePage;\nexport default ResumePage;\nvar _c;\n$RefreshReg$(_c, \"ResumePage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ResumePage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "download", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/ResumePage.js"], "sourcesContent": ["import React from 'react';\nimport './ResumePage.css';\n\nconst ResumePage = () => {\n    return (\n        <div className=\"resume-page\">\n            <h1>My Resume</h1>\n            <p>Click the link below to download my resume:</p>\n            <a href=\"/resume.pdf\" download>\n                Download Resume\n            </a>\n        </div>\n    );\n};\n\nexport default ResumePage;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACrB,oBACID,OAAA;IAAKE,SAAS,EAAC,aAAa;IAAAC,QAAA,gBACxBH,OAAA;MAAAG,QAAA,EAAI;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAClBP,OAAA;MAAAG,QAAA,EAAG;IAA2C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAClDP,OAAA;MAAGQ,IAAI,EAAC,aAAa;MAACC,QAAQ;MAAAN,QAAA,EAAC;IAE/B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEd,CAAC;AAACG,EAAA,GAVIT,UAAU;AAYhB,eAAeA,UAAU;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}