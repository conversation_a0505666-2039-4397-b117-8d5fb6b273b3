{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_onlyfans = register(\"onlyfans\", {\n  \"color\": \"#00aeef\",\n  \"path\": \"M0-.006v64.012h64V-.006zm25.348 18.014a14 14 0 0 1 9.148 3.41c2.534-3.084 5.779-3.41 11.82-3.41h7.032c-1.176 5.179-5.229 9.138-12.264 10.5 3.557 1.024 7.756 0 7.756 0-1.22 5.32-5.083 8.65-10.654 9.056l-.063.12a14 14 0 0 1-.623 1.199 14 14 0 0 1-.357.63 14 14 0 0 1-.713.987 14 14 0 0 1-.526.674 14 14 0 0 1-.771.797 14 14 0 0 1-.696.66 14 14 0 0 1-.83.637 14 14 0 0 1-.822.578 14 14 0 0 1-.877.49 14 14 0 0 1-.943.475 14 14 0 0 1-.904.343 14 14 0 0 1-1.024.338 14 14 0 0 1-.935.207 14 14 0 0 1-1.077.188 14 14 0 0 1-.953.068 14 14 0 0 1-.724.053l.011-.035a14 14 0 1 1-.011-27.965m0 9.8a4.2 4.2 0 0 0 0 8.399 4.194 4.194 0 0 0 4.199-4.2 4.2 4.2 0 0 0-4.2-4.198\"\n});\nexport { _socialIcons_onlyfans as default };", "map": {"version": 3, "names": ["register", "_socialIcons_onlyfans", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/onlyfans.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_onlyfans = register(\"onlyfans\", {\"color\":\"#00aeef\",\"path\":\"M0-.006v64.012h64V-.006zm25.348 18.014a14 14 0 0 1 9.148 3.41c2.534-3.084 5.779-3.41 11.82-3.41h7.032c-1.176 5.179-5.229 9.138-12.264 10.5 3.557 1.024 7.756 0 7.756 0-1.22 5.32-5.083 8.65-10.654 9.056l-.063.12a14 14 0 0 1-.623 1.199 14 14 0 0 1-.357.63 14 14 0 0 1-.713.987 14 14 0 0 1-.526.674 14 14 0 0 1-.771.797 14 14 0 0 1-.696.66 14 14 0 0 1-.83.637 14 14 0 0 1-.822.578 14 14 0 0 1-.877.49 14 14 0 0 1-.943.475 14 14 0 0 1-.904.343 14 14 0 0 1-1.024.338 14 14 0 0 1-.935.207 14 14 0 0 1-1.077.188 14 14 0 0 1-.953.068 14 14 0 0 1-.724.053l.011-.035a14 14 0 1 1-.011-27.965m0 9.8a4.2 4.2 0 0 0 0 8.399 4.194 4.194 0 0 0 4.199-4.2 4.2 4.2 0 0 0-4.2-4.198\"});\n\nexport { _socialIcons_onlyfans as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,qBAAqB,GAAGD,QAAQ,CAAC,UAAU,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAqpB,CAAC,CAAC;AAEluB,SAASC,qBAAqB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}