{"ast": null, "code": "import * as React from 'react';\nimport { jsx, jsxs } from 'react/jsx-runtime';\nconst default_key = 'sharethis';\nconst social_icon = {\n  display: 'inline-block',\n  width: '50px',\n  height: '50px',\n  position: 'relative',\n  overflow: 'hidden',\n  verticalAlign: 'middle'\n};\nconst social_container = {\n  position: 'absolute',\n  top: '0',\n  left: '0',\n  width: '100%',\n  height: '100%'\n};\nconst social_svg = {\n  ...social_container,\n  fillRule: 'evenodd'\n};\nconst social_svg_g = {\n  transition: 'fill 170ms ease-in-out',\n  fill: 'transparent'\n};\nconst makeUriRegex = function () {\n  let socials = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return new RegExp('(?:[/.]|^)($SOCIALS)([.]|$|/)'.replace('$SOCIALS', socials.join('|').replace(/\\./gu, '\\\\.')), 'u');\n};\nconst social_icons = new Map();\nconst network_names = new Set();\nlet uri_regex = makeUriRegex();\nfunction getNetworks() {\n  return [...network_names];\n}\n\n// note: deprecate in v7\nfunction getKeys() {\n  return getNetworks();\n}\nfunction register(social, icon) {\n  social_icons.set(social, icon);\n  network_names.add(social);\n  uri_regex = makeUriRegex(\n  // sort by longest string first\n  [...network_names].sort((pre, post) => post.length - pre.length));\n  return icon;\n}\nfunction networkFor(url) {\n  if (!url) {\n    return default_key;\n  }\n  if (url.startsWith('mailto:')) {\n    return 'mailto';\n  }\n  return url.match(uri_regex)?.[1] || default_key;\n}\nconst SocialIcon = /*#__PURE__*/React.forwardRef(function SocialIcon(props, ref) {\n  const {\n    as = 'a',\n    href,\n    url,\n    network,\n    bgColor,\n    fgColor,\n    className,\n    label,\n    children,\n    fallback,\n    defaultSVG,\n    borderRadius: br = '50%',\n    ...rest\n  } = props;\n  const networkKey = network || networkFor(url);\n  const ariaLabel = label || props['aria-label'] || networkKey;\n  const fallbackIcon = (typeof fallback === 'string' ? social_icons.get(fallback) : fallback || defaultSVG) || social_icons.get(default_key);\n  const {\n    color,\n    path\n  } = networkKey === default_key ? fallbackIcon : social_icons.get(networkKey) || {};\n  const borderRadius = typeof br != 'string' ? '50%' : br;\n  return /*#__PURE__*/React.createElement(as, {\n    href: href || url,\n    className: `social-icon${className ? ` ${className}` : ''}`,\n    ...rest,\n    style: {\n      ...social_icon,\n      ...rest.style\n    },\n    'aria-label': ariaLabel,\n    ref\n  }, /*#__PURE__*/jsx(\"span\", {\n    className: \"social-container\",\n    style: social_container,\n    children: /*#__PURE__*/jsxs(\"svg\", {\n      role: \"img\",\n      \"aria-label\": `${ariaLabel} social icon`,\n      className: \"social-svg\",\n      viewBox: \"0 0 64 64\",\n      style: {\n        ...social_svg,\n        borderRadius\n      },\n      children: [/*#__PURE__*/jsx(\"g\", {\n        className: \"social-svg-icon\",\n        style: {\n          ...social_svg_g,\n          fill: fgColor || 'white'\n        },\n        children: /*#__PURE__*/jsx(\"path\", {\n          d: `M0,0H64V64H0Z${path}`\n        })\n      }), /*#__PURE__*/jsx(\"g\", {\n        className: \"social-svg-mask\",\n        style: {\n          ...social_svg_g,\n          fill: bgColor || color\n        },\n        children: /*#__PURE__*/jsx(\"path\", {\n          d: path\n        })\n      })]\n    })\n  }), children);\n});\nexport { SocialIcon, getKeys, getNetworks, networkFor, network_names, register, social_icons, uri_regex };", "map": {"version": 3, "names": ["React", "jsx", "jsxs", "default_key", "social_icon", "display", "width", "height", "position", "overflow", "verticalAlign", "social_container", "top", "left", "social_svg", "fillRule", "social_svg_g", "transition", "fill", "makeUriRegex", "socials", "arguments", "length", "undefined", "RegExp", "replace", "join", "social_icons", "Map", "network_names", "Set", "uri_regex", "getNetworks", "get<PERSON><PERSON><PERSON>", "register", "social", "icon", "set", "add", "sort", "pre", "post", "networkFor", "url", "startsWith", "match", "SocialIcon", "forwardRef", "props", "ref", "as", "href", "network", "bgColor", "fgColor", "className", "label", "children", "fallback", "defaultSVG", "borderRadius", "br", "rest", "networkKey", "aria<PERSON><PERSON><PERSON>", "fallbackIcon", "get", "color", "path", "createElement", "style", "role", "viewBox", "d"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/component.js"], "sourcesContent": ["import * as React from 'react';\nimport { jsx, jsxs } from 'react/jsx-runtime';\n\nconst default_key = 'sharethis';\nconst social_icon = {\n  display: 'inline-block',\n  width: '50px',\n  height: '50px',\n  position: 'relative',\n  overflow: 'hidden',\n  verticalAlign: 'middle'\n};\nconst social_container = {\n  position: 'absolute',\n  top: '0',\n  left: '0',\n  width: '100%',\n  height: '100%'\n};\nconst social_svg = {\n  ...social_container,\n  fillRule: 'evenodd'\n};\nconst social_svg_g = {\n  transition: 'fill 170ms ease-in-out',\n  fill: 'transparent'\n};\nconst makeUriRegex = function () {\n  let socials = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return new RegExp('(?:[/.]|^)($SOCIALS)([.]|$|/)'.replace('$SOCIALS', socials.join('|').replace(/\\./gu, '\\\\.')), 'u');\n};\nconst social_icons = new Map();\nconst network_names = new Set();\nlet uri_regex = makeUriRegex();\nfunction getNetworks() {\n  return [...network_names];\n}\n\n// note: deprecate in v7\nfunction getKeys() {\n  return getNetworks();\n}\nfunction register(social, icon) {\n  social_icons.set(social, icon);\n  network_names.add(social);\n  uri_regex = makeUriRegex(\n  // sort by longest string first\n  [...network_names].sort((pre, post) => post.length - pre.length));\n  return icon;\n}\nfunction networkFor(url) {\n  if (!url) {\n    return default_key;\n  }\n  if (url.startsWith('mailto:')) {\n    return 'mailto';\n  }\n  return url.match(uri_regex)?.[1] || default_key;\n}\nconst SocialIcon = /*#__PURE__*/React.forwardRef(function SocialIcon(props, ref) {\n  const {\n    as = 'a',\n    href,\n    url,\n    network,\n    bgColor,\n    fgColor,\n    className,\n    label,\n    children,\n    fallback,\n    defaultSVG,\n    borderRadius: br = '50%',\n    ...rest\n  } = props;\n  const networkKey = network || networkFor(url);\n  const ariaLabel = label || props['aria-label'] || networkKey;\n  const fallbackIcon = (typeof fallback === 'string' ? social_icons.get(fallback) : fallback || defaultSVG) || social_icons.get(default_key);\n  const {\n    color,\n    path\n  } = networkKey === default_key ? fallbackIcon : social_icons.get(networkKey) || {};\n  const borderRadius = typeof br != 'string' ? '50%' : br;\n  return /*#__PURE__*/React.createElement(as, {\n    href: href || url,\n    className: `social-icon${className ? ` ${className}` : ''}`,\n    ...rest,\n    style: {\n      ...social_icon,\n      ...rest.style\n    },\n    'aria-label': ariaLabel,\n    ref\n  }, /*#__PURE__*/jsx(\"span\", {\n    className: \"social-container\",\n    style: social_container,\n    children: /*#__PURE__*/jsxs(\"svg\", {\n      role: \"img\",\n      \"aria-label\": `${ariaLabel} social icon`,\n      className: \"social-svg\",\n      viewBox: \"0 0 64 64\",\n      style: {\n        ...social_svg,\n        borderRadius\n      },\n      children: [/*#__PURE__*/jsx(\"g\", {\n        className: \"social-svg-icon\",\n        style: {\n          ...social_svg_g,\n          fill: fgColor || 'white'\n        },\n        children: /*#__PURE__*/jsx(\"path\", {\n          d: `M0,0H64V64H0Z${path}`\n        })\n      }), /*#__PURE__*/jsx(\"g\", {\n        className: \"social-svg-mask\",\n        style: {\n          ...social_svg_g,\n          fill: bgColor || color\n        },\n        children: /*#__PURE__*/jsx(\"path\", {\n          d: path\n        })\n      })]\n    })\n  }), children);\n});\n\nexport { SocialIcon, getKeys, getNetworks, networkFor, network_names, register, social_icons, uri_regex };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,GAAG,EAAEC,IAAI,QAAQ,mBAAmB;AAE7C,MAAMC,WAAW,GAAG,WAAW;AAC/B,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAE,cAAc;EACvBC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,QAAQ;EAClBC,aAAa,EAAE;AACjB,CAAC;AACD,MAAMC,gBAAgB,GAAG;EACvBH,QAAQ,EAAE,UAAU;EACpBI,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,GAAG;EACTP,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE;AACV,CAAC;AACD,MAAMO,UAAU,GAAG;EACjB,GAAGH,gBAAgB;EACnBI,QAAQ,EAAE;AACZ,CAAC;AACD,MAAMC,YAAY,GAAG;EACnBC,UAAU,EAAE,wBAAwB;EACpCC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,YAAY,GAAG,SAAAA,CAAA,EAAY;EAC/B,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACpF,OAAO,IAAIG,MAAM,CAAC,+BAA+B,CAACC,OAAO,CAAC,UAAU,EAAEL,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAACD,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;AACvH,CAAC;AACD,MAAME,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC9B,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC/B,IAAIC,SAAS,GAAGZ,YAAY,CAAC,CAAC;AAC9B,SAASa,WAAWA,CAAA,EAAG;EACrB,OAAO,CAAC,GAAGH,aAAa,CAAC;AAC3B;;AAEA;AACA,SAASI,OAAOA,CAAA,EAAG;EACjB,OAAOD,WAAW,CAAC,CAAC;AACtB;AACA,SAASE,QAAQA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC9BT,YAAY,CAACU,GAAG,CAACF,MAAM,EAAEC,IAAI,CAAC;EAC9BP,aAAa,CAACS,GAAG,CAACH,MAAM,CAAC;EACzBJ,SAAS,GAAGZ,YAAY;EACxB;EACA,CAAC,GAAGU,aAAa,CAAC,CAACU,IAAI,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKA,IAAI,CAACnB,MAAM,GAAGkB,GAAG,CAAClB,MAAM,CAAC,CAAC;EACjE,OAAOc,IAAI;AACb;AACA,SAASM,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAI,CAACA,GAAG,EAAE;IACR,OAAOxC,WAAW;EACpB;EACA,IAAIwC,GAAG,CAACC,UAAU,CAAC,SAAS,CAAC,EAAE;IAC7B,OAAO,QAAQ;EACjB;EACA,OAAOD,GAAG,CAACE,KAAK,CAACd,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI5B,WAAW;AACjD;AACA,MAAM2C,UAAU,GAAG,aAAa9C,KAAK,CAAC+C,UAAU,CAAC,SAASD,UAAUA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC/E,MAAM;IACJC,EAAE,GAAG,GAAG;IACRC,IAAI;IACJR,GAAG;IACHS,OAAO;IACPC,OAAO;IACPC,OAAO;IACPC,SAAS;IACTC,KAAK;IACLC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,YAAY,EAAEC,EAAE,GAAG,KAAK;IACxB,GAAGC;EACL,CAAC,GAAGd,KAAK;EACT,MAAMe,UAAU,GAAGX,OAAO,IAAIV,UAAU,CAACC,GAAG,CAAC;EAC7C,MAAMqB,SAAS,GAAGR,KAAK,IAAIR,KAAK,CAAC,YAAY,CAAC,IAAIe,UAAU;EAC5D,MAAME,YAAY,GAAG,CAAC,OAAOP,QAAQ,KAAK,QAAQ,GAAG/B,YAAY,CAACuC,GAAG,CAACR,QAAQ,CAAC,GAAGA,QAAQ,IAAIC,UAAU,KAAKhC,YAAY,CAACuC,GAAG,CAAC/D,WAAW,CAAC;EAC1I,MAAM;IACJgE,KAAK;IACLC;EACF,CAAC,GAAGL,UAAU,KAAK5D,WAAW,GAAG8D,YAAY,GAAGtC,YAAY,CAACuC,GAAG,CAACH,UAAU,CAAC,IAAI,CAAC,CAAC;EAClF,MAAMH,YAAY,GAAG,OAAOC,EAAE,IAAI,QAAQ,GAAG,KAAK,GAAGA,EAAE;EACvD,OAAO,aAAa7D,KAAK,CAACqE,aAAa,CAACnB,EAAE,EAAE;IAC1CC,IAAI,EAAEA,IAAI,IAAIR,GAAG;IACjBY,SAAS,EAAE,cAAcA,SAAS,GAAG,IAAIA,SAAS,EAAE,GAAG,EAAE,EAAE;IAC3D,GAAGO,IAAI;IACPQ,KAAK,EAAE;MACL,GAAGlE,WAAW;MACd,GAAG0D,IAAI,CAACQ;IACV,CAAC;IACD,YAAY,EAAEN,SAAS;IACvBf;EACF,CAAC,EAAE,aAAahD,GAAG,CAAC,MAAM,EAAE;IAC1BsD,SAAS,EAAE,kBAAkB;IAC7Be,KAAK,EAAE3D,gBAAgB;IACvB8C,QAAQ,EAAE,aAAavD,IAAI,CAAC,KAAK,EAAE;MACjCqE,IAAI,EAAE,KAAK;MACX,YAAY,EAAE,GAAGP,SAAS,cAAc;MACxCT,SAAS,EAAE,YAAY;MACvBiB,OAAO,EAAE,WAAW;MACpBF,KAAK,EAAE;QACL,GAAGxD,UAAU;QACb8C;MACF,CAAC;MACDH,QAAQ,EAAE,CAAC,aAAaxD,GAAG,CAAC,GAAG,EAAE;QAC/BsD,SAAS,EAAE,iBAAiB;QAC5Be,KAAK,EAAE;UACL,GAAGtD,YAAY;UACfE,IAAI,EAAEoC,OAAO,IAAI;QACnB,CAAC;QACDG,QAAQ,EAAE,aAAaxD,GAAG,CAAC,MAAM,EAAE;UACjCwE,CAAC,EAAE,gBAAgBL,IAAI;QACzB,CAAC;MACH,CAAC,CAAC,EAAE,aAAanE,GAAG,CAAC,GAAG,EAAE;QACxBsD,SAAS,EAAE,iBAAiB;QAC5Be,KAAK,EAAE;UACL,GAAGtD,YAAY;UACfE,IAAI,EAAEmC,OAAO,IAAIc;QACnB,CAAC;QACDV,QAAQ,EAAE,aAAaxD,GAAG,CAAC,MAAM,EAAE;UACjCwE,CAAC,EAAEL;QACL,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC,EAAEX,QAAQ,CAAC;AACf,CAAC,CAAC;AAEF,SAASX,UAAU,EAAEb,OAAO,EAAED,WAAW,EAAEU,UAAU,EAAEb,aAAa,EAAEK,QAAQ,EAAEP,YAAY,EAAEI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}