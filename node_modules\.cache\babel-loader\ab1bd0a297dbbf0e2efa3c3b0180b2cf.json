{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\HomePage.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './HomePage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Welcome to My E-Portfolio\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            children: \"About Me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/resume\",\n            children: \"Resume\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/learning-outcomes\",\n            children: \"Learning Outcomes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 9\n  }, this);\n};\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "HomePage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './HomePage.css';\n\nconst HomePage = () => {\n    return (\n        <div className=\"home-page\">\n            <h1>Welcome to My E-Portfolio</h1>\n            <nav>\n                <ul>\n                    <li><Link to=\"/about\">About Me</Link></li>\n                    <li><Link to=\"/resume\">Resume</Link></li>\n                    <li><Link to=\"/learning-outcomes\">Learning Outcomes</Link></li>\n                </ul>\n            </nav>\n        </div>\n    );\n};\n\nexport default HomePage;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACnB,oBACID,OAAA;IAAKE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtBH,OAAA;MAAAG,QAAA,EAAI;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAClCP,OAAA;MAAAG,QAAA,eACIH,OAAA;QAAAG,QAAA,gBACIH,OAAA;UAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;YAACU,EAAE,EAAC,QAAQ;YAAAL,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CP,OAAA;UAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;YAACU,EAAE,EAAC,SAAS;YAAAL,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzCP,OAAA;UAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;YAACU,EAAE,EAAC,oBAAoB;YAAAL,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACE,EAAA,GAbIR,QAAQ;AAed,eAAeA,QAAQ;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}