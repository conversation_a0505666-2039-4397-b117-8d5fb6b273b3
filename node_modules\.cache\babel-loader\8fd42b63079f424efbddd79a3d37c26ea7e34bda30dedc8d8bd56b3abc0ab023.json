{"ast": null, "code": "const learningOutcomes = [{\n  day: \"Day 1\",\n  outcome: \"Learned the basics of HTML and how to structure a webpage.\"\n}, {\n  day: \"Day 2\",\n  outcome: \"Explored CSS for styling and layout design.\"\n}, {\n  day: \"Day 3\",\n  outcome: \"Introduced to JavaScript and its role in web development.\"\n}, {\n  day: \"Day 4\",\n  outcome: \"Started learning about React and component-based architecture.\"\n}, {\n  day: \"Day 5\",\n  outcome: \"Built a simple React application and understood state management.\"\n}];\nexport default learningOutcomes;", "map": {"version": 3, "names": ["learningOutcomes", "day", "outcome"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/data/learningOutcomes.js"], "sourcesContent": ["const learningOutcomes = [\n    {\n        day: \"Day 1\",\n        outcome: \"Learned the basics of HTML and how to structure a webpage.\"\n    },\n    {\n        day: \"Day 2\",\n        outcome: \"Explored CSS for styling and layout design.\"\n    },\n    {\n        day: \"Day 3\",\n        outcome: \"Introduced to JavaScript and its role in web development.\"\n    },\n    {\n        day: \"Day 4\",\n        outcome: \"Started learning about React and component-based architecture.\"\n    },\n    {\n        day: \"Day 5\",\n        outcome: \"Built a simple React application and understood state management.\"\n    }\n];\n\nexport default learningOutcomes;"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG,CACrB;EACIC,GAAG,EAAE,OAAO;EACZC,OAAO,EAAE;AACb,CAAC,EACD;EACID,GAAG,EAAE,OAAO;EACZC,OAAO,EAAE;AACb,CAAC,EACD;EACID,GAAG,EAAE,OAAO;EACZC,OAAO,EAAE;AACb,CAAC,EACD;EACID,GAAG,EAAE,OAAO;EACZC,OAAO,EAAE;AACb,CAAC,EACD;EACID,GAAG,EAAE,OAAO;EACZC,OAAO,EAAE;AACb,CAAC,CACJ;AAED,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}