{"ast": null, "code": "var toArray = Function.prototype.bind.call(Function.prototype.call, [].slice);\n/**\n * Runs `querySelectorAll` on a given element.\n * \n * @param element the element\n * @param selector the selector\n */\n\nexport default function qsa(element, selector) {\n  return toArray(element.querySelectorAll(selector));\n}", "map": {"version": 3, "names": ["toArray", "Function", "prototype", "bind", "call", "slice", "qsa", "element", "selector", "querySelectorAll"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/dom-helpers/esm/querySelectorAll.js"], "sourcesContent": ["var toArray = Function.prototype.bind.call(Function.prototype.call, [].slice);\n/**\n * Runs `querySelectorAll` on a given element.\n * \n * @param element the element\n * @param selector the selector\n */\n\nexport default function qsa(element, selector) {\n  return toArray(element.querySelectorAll(selector));\n}"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,QAAQ,CAACC,SAAS,CAACC,IAAI,CAACC,IAAI,CAACH,QAAQ,CAACC,SAAS,CAACE,IAAI,EAAE,EAAE,CAACC,KAAK,CAAC;AAC7E;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,GAAGA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC7C,OAAOR,OAAO,CAACO,OAAO,CAACE,gBAAgB,CAACD,QAAQ,CAAC,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}