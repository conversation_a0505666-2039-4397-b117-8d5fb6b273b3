{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CloseButton from './CloseButton';\nimport ToastContext from './ToastContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst ToastHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  closeLabel = 'Close',\n  closeVariant,\n  closeButton = true,\n  className,\n  children,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'toast-header');\n  const context = useContext(ToastContext);\n  const handleClick = useEventCallback(e => {\n    context == null || context.onClose == null || context.onClose(e);\n  });\n  return /*#__PURE__*/_jsxs(\"div\", {\n    ref: ref,\n    ...props,\n    className: classNames(bsPrefix, className),\n    children: [children, closeButton && /*#__PURE__*/_jsx(CloseButton, {\n      \"aria-label\": closeLabel,\n      variant: closeVariant,\n      onClick: handleClick,\n      \"data-dismiss\": \"toast\"\n    })]\n  });\n});\nToastHeader.displayName = 'ToastHeader';\nexport default ToastHeader;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useEventCallback", "useBootstrapPrefix", "CloseButton", "ToastContext", "jsx", "_jsx", "jsxs", "_jsxs", "ToastHeader", "forwardRef", "bsPrefix", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "closeButton", "className", "children", "props", "ref", "context", "handleClick", "e", "onClose", "variant", "onClick", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/ToastHeader.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CloseButton from './CloseButton';\nimport ToastContext from './ToastContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst ToastHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  closeLabel = 'Close',\n  closeVariant,\n  closeButton = true,\n  className,\n  children,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'toast-header');\n  const context = useContext(ToastContext);\n  const handleClick = useEventCallback(e => {\n    context == null || context.onClose == null || context.onClose(e);\n  });\n  return /*#__PURE__*/_jsxs(\"div\", {\n    ref: ref,\n    ...props,\n    className: classNames(bsPrefix, className),\n    children: [children, closeButton && /*#__PURE__*/_jsx(CloseButton, {\n      \"aria-label\": closeLabel,\n      variant: closeVariant,\n      onClick: handleClick,\n      \"data-dismiss\": \"toast\"\n    })]\n  });\n});\nToastHeader.displayName = 'ToastHeader';\nexport default ToastHeader;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,WAAW,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,CAAC;EACjDC,QAAQ;EACRC,UAAU,GAAG,OAAO;EACpBC,YAAY;EACZC,WAAW,GAAG,IAAI;EAClBC,SAAS;EACTC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTP,QAAQ,GAAGT,kBAAkB,CAACS,QAAQ,EAAE,cAAc,CAAC;EACvD,MAAMQ,OAAO,GAAGnB,UAAU,CAACI,YAAY,CAAC;EACxC,MAAMgB,WAAW,GAAGnB,gBAAgB,CAACoB,CAAC,IAAI;IACxCF,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACG,OAAO,IAAI,IAAI,IAAIH,OAAO,CAACG,OAAO,CAACD,CAAC,CAAC;EAClE,CAAC,CAAC;EACF,OAAO,aAAab,KAAK,CAAC,KAAK,EAAE;IAC/BU,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRF,SAAS,EAAEjB,UAAU,CAACa,QAAQ,EAAEI,SAAS,CAAC;IAC1CC,QAAQ,EAAE,CAACA,QAAQ,EAAEF,WAAW,IAAI,aAAaR,IAAI,CAACH,WAAW,EAAE;MACjE,YAAY,EAAES,UAAU;MACxBW,OAAO,EAAEV,YAAY;MACrBW,OAAO,EAAEJ,WAAW;MACpB,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFX,WAAW,CAACgB,WAAW,GAAG,aAAa;AACvC,eAAehB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}