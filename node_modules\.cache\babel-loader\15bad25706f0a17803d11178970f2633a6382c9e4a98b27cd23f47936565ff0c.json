{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_slack = register(\"slack\", {\n  \"color\": \"#4A164A\",\n  \"path\": \"M0 0v64h64V0Zm12.636 37.56c0 5.066 8 5.066 8 0v-3.8h-4c-2.209 0-4 1.7-4 3.8m25.28-6.346c2.21 0 4-1.702 4-3.8V17.287c0-5.066-8-5.066-8 0v10.127c0 2.113 1.815 3.82 4.04 3.8zm14.64-3.8c0-5.067-8-5.067-8 0v3.8h4c2.24.042 4.08-1.672 4.08-3.8zm-25.24 6.345c-2.209 0-4 1.702-4 3.8v10.127c0 5.067 8 5.067 8 0V37.559c0-2.098-1.79-3.8-4-3.8m10.64 10.127h-4v3.8c0 3.386 4.309 5.08 6.829 2.687s.735-6.487-2.829-6.487m10.68-10.127h-10.68c-5.324.009-5.324 7.592 0 7.6h10.68c5.325-.008 5.325-7.591 0-7.6m-21.32-10.145h-10.68c-5.342-.008-5.342 7.608 0 7.6h10.68c5.325-.009 5.325-7.592 0-7.6m0-10.127c-5.324.008-5.324 7.592 0 7.6h4v-3.8c0-2.126-1.804-3.8-4-3.8\"\n});\nexport { _socialIcons_slack as default };", "map": {"version": 3, "names": ["register", "_socialIcons_slack", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/slack.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_slack = register(\"slack\", {\"color\":\"#4A164A\",\"path\":\"M0 0v64h64V0Zm12.636 37.56c0 5.066 8 5.066 8 0v-3.8h-4c-2.209 0-4 1.7-4 3.8m25.28-6.346c2.21 0 4-1.702 4-3.8V17.287c0-5.066-8-5.066-8 0v10.127c0 2.113 1.815 3.82 4.04 3.8zm14.64-3.8c0-5.067-8-5.067-8 0v3.8h4c2.24.042 4.08-1.672 4.08-3.8zm-25.24 6.345c-2.209 0-4 1.702-4 3.8v10.127c0 5.067 8 5.067 8 0V37.559c0-2.098-1.79-3.8-4-3.8m10.64 10.127h-4v3.8c0 3.386 4.309 5.08 6.829 2.687s.735-6.487-2.829-6.487m10.68-10.127h-10.68c-5.324.009-5.324 7.592 0 7.6h10.68c5.325-.008 5.325-7.591 0-7.6m-21.32-10.145h-10.68c-5.342-.008-5.342 7.608 0 7.6h10.68c5.325-.009 5.325-7.592 0-7.6m0-10.127c-5.324.008-5.324 7.592 0 7.6h4v-3.8c0-2.126-1.804-3.8-4-3.8\"});\n\nexport { _socialIcons_slack as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,kBAAkB,GAAGD,QAAQ,CAAC,OAAO,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAqoB,CAAC,CAAC;AAE5sB,SAASC,kBAAkB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}