import React, { useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import Fade from 'react-reveal/Fade';
import styled from 'styled-components';

const HomeSection = styled.section`
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: ${({ theme }) => theme.background};
  padding-top: 100px;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: ${({ theme }) => theme.color};

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const HeroSubtitle = styled.h2`
  font-size: 1.8rem;
  font-weight: 400;
  margin-bottom: 30px;
  color: ${({ theme }) => theme.accentColor};

  @media (max-width: 768px) {
    font-size: 1.4rem;
  }
`;

const SocialContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
`;

const SocialLink = styled.a`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.accentColor};
  color: white;
  text-decoration: none;
  font-size: 1.5rem;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
    color: white;
    background-color: ${({ theme }) => theme.color};
  }
`;

const Home = () => {
  const [homeData, setHomeData] = useState(null);
  const [socialData, setSocialData] = useState(null);
  const [currentRole, setCurrentRole] = useState(0);

  useEffect(() => {
    fetch('/personal-e-portfolio/profile/home.json')
      .then(response => response.json())
      .then(data => setHomeData(data))
      .catch(error => console.error('Error loading home data:', error));

    fetch('/personal-e-portfolio/profile/social.json')
      .then(response => response.json())
      .then(data => setSocialData(data))
      .catch(error => console.error('Error loading social data:', error));
  }, []);

  useEffect(() => {
    if (homeData && homeData.roles) {
      const interval = setInterval(() => {
        setCurrentRole((prev) => (prev + 1) % homeData.roles.length);
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [homeData]);

  if (!homeData || !socialData) return null;

  return (
    <HomeSection id="home">
      <Container>
        <Row className="justify-content-center">
          <Col lg={8} className="text-center">
            <Fade bottom>
              <HeroTitle>
                Hi, I'm {homeData.name}
              </HeroTitle>
              <HeroSubtitle>
                I'm {homeData.roles[currentRole]}
              </HeroSubtitle>
              <SocialContainer>
                {socialData.social.map((social, index) => {
                  const getIcon = (network) => {
                    switch (network) {
                      case 'linkedin': return '💼';
                      case 'github': return '🐙';
                      case 'email': return '📧';
                      case 'twitter': return '🐦';
                      default: return '🔗';
                    }
                  };

                  return (
                    <SocialLink
                      key={index}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {getIcon(social.network)}
                    </SocialLink>
                  );
                })}
              </SocialContainer>
            </Fade>
          </Col>
        </Row>
      </Container>
    </HomeSection>
  );
};

export default Home;
