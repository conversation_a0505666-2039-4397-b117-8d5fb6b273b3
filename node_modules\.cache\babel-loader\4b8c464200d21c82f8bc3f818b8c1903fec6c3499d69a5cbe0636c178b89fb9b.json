{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionCollapse from './AccordionCollapse';\nimport AccordionItemContext from './AccordionItemContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AccordionBody = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  onEnter,\n  onEntering,\n  onEntered,\n  onExit,\n  onExiting,\n  onExited,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-body');\n  const {\n    eventKey\n  } = useContext(AccordionItemContext);\n  return /*#__PURE__*/_jsx(AccordionCollapse, {\n    eventKey: eventKey,\n    onEnter: onEnter,\n    onEntering: onEntering,\n    onEntered: onEntered,\n    onExit: onExit,\n    onExiting: onExiting,\n    onExited: onExited,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix)\n    })\n  });\n});\nAccordionBody.displayName = 'AccordionBody';\nexport default AccordionBody;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useBootstrapPrefix", "AccordionCollapse", "AccordionItemContext", "jsx", "_jsx", "AccordionBody", "forwardRef", "as", "Component", "bsPrefix", "className", "onEnter", "onEntering", "onEntered", "onExit", "onExiting", "onExited", "props", "ref", "eventKey", "children", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/AccordionBody.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionCollapse from './AccordionCollapse';\nimport AccordionItemContext from './AccordionItemContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AccordionBody = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  onEnter,\n  onEntering,\n  onEntered,\n  onExit,\n  onExiting,\n  onExited,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-body');\n  const {\n    eventKey\n  } = useContext(AccordionItemContext);\n  return /*#__PURE__*/_jsx(AccordionCollapse, {\n    eventKey: eventKey,\n    onEnter: onEnter,\n    onEntering: onEntering,\n    onEntered: onEntered,\n    onExit: onExit,\n    onExiting: onExiting,\n    onExited: onExited,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix)\n    })\n  });\n});\nAccordionBody.displayName = 'AccordionBody';\nexport default AccordionBody;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EACnD;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrBC,QAAQ;EACRC,SAAS;EACTC,OAAO;EACPC,UAAU;EACVC,SAAS;EACTC,MAAM;EACNC,SAAS;EACTC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTT,QAAQ,GAAGT,kBAAkB,CAACS,QAAQ,EAAE,gBAAgB,CAAC;EACzD,MAAM;IACJU;EACF,CAAC,GAAGpB,UAAU,CAACG,oBAAoB,CAAC;EACpC,OAAO,aAAaE,IAAI,CAACH,iBAAiB,EAAE;IAC1CkB,QAAQ,EAAEA,QAAQ;IAClBR,OAAO,EAAEA,OAAO;IAChBC,UAAU,EAAEA,UAAU;IACtBC,SAAS,EAAEA,SAAS;IACpBC,MAAM,EAAEA,MAAM;IACdC,SAAS,EAAEA,SAAS;IACpBC,QAAQ,EAAEA,QAAQ;IAClBI,QAAQ,EAAE,aAAahB,IAAI,CAACI,SAAS,EAAE;MACrCU,GAAG,EAAEA,GAAG;MACR,GAAGD,KAAK;MACRP,SAAS,EAAEb,UAAU,CAACa,SAAS,EAAED,QAAQ;IAC3C,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,aAAa,CAACgB,WAAW,GAAG,eAAe;AAC3C,eAAehB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}