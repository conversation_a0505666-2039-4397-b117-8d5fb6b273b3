{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\coding\\\\personal-e-portfolio\\\\src\\\\components\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport { SocialIcon } from 'react-social-icons';\nimport Fade from 'react-reveal/Fade';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({\n  theme\n}) => theme.background};\n  padding-top: 100px;\n`;\n_c = HomeSection;\nconst HeroTitle = styled.h1`\n  font-size: 3.5rem;\n  font-weight: 700;\n  margin-bottom: 20px;\n  color: ${({\n  theme\n}) => theme.color};\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n_c2 = HeroTitle;\nconst HeroSubtitle = styled.h2`\n  font-size: 1.8rem;\n  font-weight: 400;\n  margin-bottom: 30px;\n  color: ${({\n  theme\n}) => theme.accentColor};\n  \n  @media (max-width: 768px) {\n    font-size: 1.4rem;\n  }\n`;\n_c3 = HeroSubtitle;\nconst SocialContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  margin-top: 30px;\n  \n  .social-svg {\n    transition: transform 0.3s ease;\n    \n    &:hover {\n      transform: scale(1.1);\n    }\n  }\n`;\n_c4 = SocialContainer;\nconst Home = () => {\n  _s();\n  const [homeData, setHomeData] = useState(null);\n  const [socialData, setSocialData] = useState(null);\n  const [currentRole, setCurrentRole] = useState(0);\n  useEffect(() => {\n    fetch('/personal-e-portfolio/profile/home.json').then(response => response.json()).then(data => setHomeData(data)).catch(error => console.error('Error loading home data:', error));\n    fetch('/personal-e-portfolio/profile/social.json').then(response => response.json()).then(data => setSocialData(data)).catch(error => console.error('Error loading social data:', error));\n  }, []);\n  useEffect(() => {\n    if (homeData && homeData.roles) {\n      const interval = setInterval(() => {\n        setCurrentRole(prev => (prev + 1) % homeData.roles.length);\n      }, 2000);\n      return () => clearInterval(interval);\n    }\n  }, [homeData]);\n  if (!homeData || !socialData) return null;\n  return /*#__PURE__*/_jsxDEV(HomeSection, {\n    id: \"home\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          lg: 8,\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Fade, {\n            bottom: true,\n            children: [/*#__PURE__*/_jsxDEV(HeroTitle, {\n              children: [\"Hi, I'm \", homeData.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(HeroSubtitle, {\n              children: [\"I'm \", homeData.roles[currentRole]]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(SocialContainer, {\n              children: socialData.social.map((social, index) => /*#__PURE__*/_jsxDEV(SocialIcon, {\n                url: social.href,\n                network: social.network,\n                style: {\n                  height: 50,\n                  width: 50\n                },\n                target: \"_blank\",\n                rel: \"noopener noreferrer\"\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"lHGI796c6UZY9pmWxPRLVr1xnpo=\");\n_c5 = Home;\nexport default Home;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"HomeSection\");\n$RefreshReg$(_c2, \"HeroTitle\");\n$RefreshReg$(_c3, \"HeroSubtitle\");\n$RefreshReg$(_c4, \"SocialContainer\");\n$RefreshReg$(_c5, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "SocialIcon", "Fade", "styled", "jsxDEV", "_jsxDEV", "HomeSection", "section", "theme", "background", "_c", "<PERSON><PERSON><PERSON><PERSON>", "h1", "color", "_c2", "HeroSubtitle", "h2", "accentColor", "_c3", "SocialContainer", "div", "_c4", "Home", "_s", "homeData", "setHomeData", "socialData", "setSocialData", "currentRole", "setCurrentRole", "fetch", "then", "response", "json", "data", "catch", "error", "console", "roles", "interval", "setInterval", "prev", "length", "clearInterval", "id", "children", "className", "lg", "bottom", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "social", "map", "index", "url", "href", "network", "style", "height", "width", "target", "rel", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/src/components/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport { SocialIcon } from 'react-social-icons';\nimport Fade from 'react-reveal/Fade';\nimport styled from 'styled-components';\n\nconst HomeSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  background: ${({ theme }) => theme.background};\n  padding-top: 100px;\n`;\n\nconst HeroTitle = styled.h1`\n  font-size: 3.5rem;\n  font-weight: 700;\n  margin-bottom: 20px;\n  color: ${({ theme }) => theme.color};\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n\nconst HeroSubtitle = styled.h2`\n  font-size: 1.8rem;\n  font-weight: 400;\n  margin-bottom: 30px;\n  color: ${({ theme }) => theme.accentColor};\n  \n  @media (max-width: 768px) {\n    font-size: 1.4rem;\n  }\n`;\n\nconst SocialContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  margin-top: 30px;\n  \n  .social-svg {\n    transition: transform 0.3s ease;\n    \n    &:hover {\n      transform: scale(1.1);\n    }\n  }\n`;\n\nconst Home = () => {\n  const [homeData, setHomeData] = useState(null);\n  const [socialData, setSocialData] = useState(null);\n  const [currentRole, setCurrentRole] = useState(0);\n\n  useEffect(() => {\n    fetch('/personal-e-portfolio/profile/home.json')\n      .then(response => response.json())\n      .then(data => setHomeData(data))\n      .catch(error => console.error('Error loading home data:', error));\n\n    fetch('/personal-e-portfolio/profile/social.json')\n      .then(response => response.json())\n      .then(data => setSocialData(data))\n      .catch(error => console.error('Error loading social data:', error));\n  }, []);\n\n  useEffect(() => {\n    if (homeData && homeData.roles) {\n      const interval = setInterval(() => {\n        setCurrentRole((prev) => (prev + 1) % homeData.roles.length);\n      }, 2000);\n      return () => clearInterval(interval);\n    }\n  }, [homeData]);\n\n  if (!homeData || !socialData) return null;\n\n  return (\n    <HomeSection id=\"home\">\n      <Container>\n        <Row className=\"justify-content-center\">\n          <Col lg={8} className=\"text-center\">\n            <Fade bottom>\n              <HeroTitle>\n                Hi, I'm {homeData.name}\n              </HeroTitle>\n              <HeroSubtitle>\n                I'm {homeData.roles[currentRole]}\n              </HeroSubtitle>\n              <SocialContainer>\n                {socialData.social.map((social, index) => (\n                  <SocialIcon\n                    key={index}\n                    url={social.href}\n                    network={social.network}\n                    style={{ height: 50, width: 50 }}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  />\n                ))}\n              </SocialContainer>\n            </Fade>\n          </Col>\n        </Row>\n      </Container>\n    </HomeSection>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACrD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,GAAGH,MAAM,CAACI,OAAO;AAClC;AACA;AACA;AACA,gBAAgB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,UAAU;AAC/C;AACA,CAAC;AAACC,EAAA,GANIJ,WAAW;AAQjB,MAAMK,SAAS,GAAGR,MAAM,CAACS,EAAE;AAC3B;AACA;AACA;AACA,WAAW,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACK,KAAK;AACrC;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIH,SAAS;AAWf,MAAMI,YAAY,GAAGZ,MAAM,CAACa,EAAE;AAC9B;AACA;AACA;AACA,WAAW,CAAC;EAAER;AAAM,CAAC,KAAKA,KAAK,CAACS,WAAW;AAC3C;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIH,YAAY;AAWlB,MAAMI,eAAe,GAAGhB,MAAM,CAACiB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,eAAe;AAerB,MAAMG,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACdiC,KAAK,CAAC,yCAAyC,CAAC,CAC7CC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAIT,WAAW,CAACS,IAAI,CAAC,CAAC,CAC/BC,KAAK,CAACC,KAAK,IAAIC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC,CAAC;IAEnEN,KAAK,CAAC,2CAA2C,CAAC,CAC/CC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAIP,aAAa,CAACO,IAAI,CAAC,CAAC,CACjCC,KAAK,CAACC,KAAK,IAAIC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC,CAAC;EACvE,CAAC,EAAE,EAAE,CAAC;EAENvC,SAAS,CAAC,MAAM;IACd,IAAI2B,QAAQ,IAAIA,QAAQ,CAACc,KAAK,EAAE;MAC9B,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCX,cAAc,CAAEY,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIjB,QAAQ,CAACc,KAAK,CAACI,MAAM,CAAC;MAC9D,CAAC,EAAE,IAAI,CAAC;MACR,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;EAEd,IAAI,CAACA,QAAQ,IAAI,CAACE,UAAU,EAAE,OAAO,IAAI;EAEzC,oBACErB,OAAA,CAACC,WAAW;IAACsC,EAAE,EAAC,MAAM;IAAAC,QAAA,eACpBxC,OAAA,CAACP,SAAS;MAAA+C,QAAA,eACRxC,OAAA,CAACN,GAAG;QAAC+C,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrCxC,OAAA,CAACL,GAAG;UAAC+C,EAAE,EAAE,CAAE;UAACD,SAAS,EAAC,aAAa;UAAAD,QAAA,eACjCxC,OAAA,CAACH,IAAI;YAAC8C,MAAM;YAAAH,QAAA,gBACVxC,OAAA,CAACM,SAAS;cAAAkC,QAAA,GAAC,UACD,EAACrB,QAAQ,CAACyB,IAAI;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACZhD,OAAA,CAACU,YAAY;cAAA8B,QAAA,GAAC,MACR,EAACrB,QAAQ,CAACc,KAAK,CAACV,WAAW,CAAC;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACfhD,OAAA,CAACc,eAAe;cAAA0B,QAAA,EACbnB,UAAU,CAAC4B,MAAM,CAACC,GAAG,CAAC,CAACD,MAAM,EAAEE,KAAK,kBACnCnD,OAAA,CAACJ,UAAU;gBAETwD,GAAG,EAAEH,MAAM,CAACI,IAAK;gBACjBC,OAAO,EAAEL,MAAM,CAACK,OAAQ;gBACxBC,KAAK,EAAE;kBAAEC,MAAM,EAAE,EAAE;kBAAEC,KAAK,EAAE;gBAAG,CAAE;gBACjCC,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC;cAAqB,GALpBR,KAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAElB,CAAC;AAAC9B,EAAA,CA1DID,IAAI;AAAA2C,GAAA,GAAJ3C,IAAI;AA4DV,eAAeA,IAAI;AAAC,IAAAZ,EAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAA4C,GAAA;AAAAC,YAAA,CAAAxD,EAAA;AAAAwD,YAAA,CAAApD,GAAA;AAAAoD,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}