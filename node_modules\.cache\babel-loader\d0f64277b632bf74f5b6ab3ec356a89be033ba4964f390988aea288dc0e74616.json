{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport useBreakpoint from '@restart/hooks/useBreakpoint';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport * as React from 'react';\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport BaseModal from '@restart/ui/Modal';\nimport Fade from './Fade';\nimport OffcanvasBody from './OffcanvasBody';\nimport OffcanvasToggling from './OffcanvasToggling';\nimport ModalContext from './ModalContext';\nimport OffcanvasHeader from './OffcanvasHeader';\nimport OffcanvasTitle from './OffcanvasTitle';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport BootstrapModalManager, { getSharedManager } from './BootstrapModalManager';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction DialogTransition(props) {\n  return /*#__PURE__*/_jsx(OffcanvasToggling, {\n    ...props\n  });\n}\nfunction BackdropTransition(props) {\n  return /*#__PURE__*/_jsx(Fade, {\n    ...props\n  });\n}\nconst Offcanvas = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  'aria-labelledby': ariaLabelledby,\n  placement = 'start',\n  responsive,\n  /* BaseModal props */\n\n  show = false,\n  backdrop = true,\n  keyboard = true,\n  scroll = false,\n  onEscapeKeyDown,\n  onShow,\n  onHide,\n  container,\n  autoFocus = true,\n  enforceFocus = true,\n  restoreFocus = true,\n  restoreFocusOptions,\n  onEntered,\n  onExit,\n  onExiting,\n  onEnter,\n  onEntering,\n  onExited,\n  backdropClassName,\n  manager: propsManager,\n  renderStaticNode = false,\n  ...props\n}, ref) => {\n  const modalManager = useRef();\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas');\n  const [showOffcanvas, setShowOffcanvas] = useState(false);\n  const handleHide = useEventCallback(onHide);\n  const hideResponsiveOffcanvas = useBreakpoint(responsive || 'xs', 'up');\n  useEffect(() => {\n    // Handles the case where screen is resized while the responsive\n    // offcanvas is shown. If `responsive` not provided, just use `show`.\n    setShowOffcanvas(responsive ? show && !hideResponsiveOffcanvas : show);\n  }, [show, responsive, hideResponsiveOffcanvas]);\n  const modalContext = useMemo(() => ({\n    onHide: handleHide\n  }), [handleHide]);\n  function getModalManager() {\n    if (propsManager) return propsManager;\n    if (scroll) {\n      // Have to use a different modal manager since the shared\n      // one handles overflow.\n      if (!modalManager.current) modalManager.current = new BootstrapModalManager({\n        handleContainerOverflow: false\n      });\n      return modalManager.current;\n    }\n    return getSharedManager();\n  }\n  const handleEnter = (node, ...args) => {\n    if (node) node.style.visibility = 'visible';\n    onEnter == null || onEnter(node, ...args);\n  };\n  const handleExited = (node, ...args) => {\n    if (node) node.style.visibility = '';\n    onExited == null || onExited(...args);\n  };\n  const renderBackdrop = useCallback(backdropProps => /*#__PURE__*/_jsx(\"div\", {\n    ...backdropProps,\n    className: classNames(`${bsPrefix}-backdrop`, backdropClassName)\n  }), [backdropClassName, bsPrefix]);\n  const renderDialog = dialogProps => /*#__PURE__*/_jsx(\"div\", {\n    ...dialogProps,\n    ...props,\n    className: classNames(className, responsive ? `${bsPrefix}-${responsive}` : bsPrefix, `${bsPrefix}-${placement}`),\n    \"aria-labelledby\": ariaLabelledby,\n    children: children\n  });\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [!showOffcanvas && (responsive || renderStaticNode) && renderDialog({}), /*#__PURE__*/_jsx(ModalContext.Provider, {\n      value: modalContext,\n      children: /*#__PURE__*/_jsx(BaseModal, {\n        show: showOffcanvas,\n        ref: ref,\n        backdrop: backdrop,\n        container: container,\n        keyboard: keyboard,\n        autoFocus: autoFocus,\n        enforceFocus: enforceFocus && !scroll,\n        restoreFocus: restoreFocus,\n        restoreFocusOptions: restoreFocusOptions,\n        onEscapeKeyDown: onEscapeKeyDown,\n        onShow: onShow,\n        onHide: handleHide,\n        onEnter: handleEnter,\n        onEntering: onEntering,\n        onEntered: onEntered,\n        onExit: onExit,\n        onExiting: onExiting,\n        onExited: handleExited,\n        manager: getModalManager(),\n        transition: DialogTransition,\n        backdropTransition: BackdropTransition,\n        renderBackdrop: renderBackdrop,\n        renderDialog: renderDialog\n      })\n    })]\n  });\n});\nOffcanvas.displayName = 'Offcanvas';\nexport default Object.assign(Offcanvas, {\n  Body: OffcanvasBody,\n  Header: OffcanvasHeader,\n  Title: OffcanvasTitle\n});", "map": {"version": 3, "names": ["classNames", "useBreakpoint", "useEventCallback", "React", "useCallback", "useEffect", "useMemo", "useRef", "useState", "BaseModal", "Fade", "OffcanvasBody", "OffcanvasToggling", "ModalContext", "OffcanvasHeader", "OffcanvasTitle", "useBootstrapPrefix", "BootstrapModalManager", "getSharedManager", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "DialogTransition", "props", "BackdropTransition", "<PERSON><PERSON><PERSON>", "forwardRef", "bsPrefix", "className", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placement", "responsive", "show", "backdrop", "keyboard", "scroll", "onEscapeKeyDown", "onShow", "onHide", "container", "autoFocus", "enforceFocus", "restoreFocus", "restoreFocusOptions", "onEntered", "onExit", "onExiting", "onEnter", "onEntering", "onExited", "backdropClassName", "manager", "props<PERSON>anager", "renderStaticNode", "ref", "modalManager", "showOffcanvas", "setShowOffcanvas", "handleHide", "hideResponsiveOffcanvas", "modalContext", "getModalManager", "current", "handleContainerOverflow", "handleEnter", "node", "args", "style", "visibility", "handleExited", "renderBackdrop", "backdropProps", "renderDialog", "dialogProps", "Provider", "value", "transition", "backdropTransition", "displayName", "Object", "assign", "Body", "Header", "Title"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/Offcanvas.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport useBreakpoint from '@restart/hooks/useBreakpoint';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport * as React from 'react';\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport BaseModal from '@restart/ui/Modal';\nimport Fade from './Fade';\nimport OffcanvasBody from './OffcanvasBody';\nimport OffcanvasToggling from './OffcanvasToggling';\nimport ModalContext from './ModalContext';\nimport OffcanvasHeader from './OffcanvasHeader';\nimport OffcanvasTitle from './OffcanvasTitle';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport BootstrapModalManager, { getSharedManager } from './BootstrapModalManager';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction DialogTransition(props) {\n  return /*#__PURE__*/_jsx(OffcanvasToggling, {\n    ...props\n  });\n}\nfunction BackdropTransition(props) {\n  return /*#__PURE__*/_jsx(Fade, {\n    ...props\n  });\n}\nconst Offcanvas = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  'aria-labelledby': ariaLabelledby,\n  placement = 'start',\n  responsive,\n  /* BaseModal props */\n\n  show = false,\n  backdrop = true,\n  keyboard = true,\n  scroll = false,\n  onEscapeKeyDown,\n  onShow,\n  onHide,\n  container,\n  autoFocus = true,\n  enforceFocus = true,\n  restoreFocus = true,\n  restoreFocusOptions,\n  onEntered,\n  onExit,\n  onExiting,\n  onEnter,\n  onEntering,\n  onExited,\n  backdropClassName,\n  manager: propsManager,\n  renderStaticNode = false,\n  ...props\n}, ref) => {\n  const modalManager = useRef();\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas');\n  const [showOffcanvas, setShowOffcanvas] = useState(false);\n  const handleHide = useEventCallback(onHide);\n  const hideResponsiveOffcanvas = useBreakpoint(responsive || 'xs', 'up');\n  useEffect(() => {\n    // Handles the case where screen is resized while the responsive\n    // offcanvas is shown. If `responsive` not provided, just use `show`.\n    setShowOffcanvas(responsive ? show && !hideResponsiveOffcanvas : show);\n  }, [show, responsive, hideResponsiveOffcanvas]);\n  const modalContext = useMemo(() => ({\n    onHide: handleHide\n  }), [handleHide]);\n  function getModalManager() {\n    if (propsManager) return propsManager;\n    if (scroll) {\n      // Have to use a different modal manager since the shared\n      // one handles overflow.\n      if (!modalManager.current) modalManager.current = new BootstrapModalManager({\n        handleContainerOverflow: false\n      });\n      return modalManager.current;\n    }\n    return getSharedManager();\n  }\n  const handleEnter = (node, ...args) => {\n    if (node) node.style.visibility = 'visible';\n    onEnter == null || onEnter(node, ...args);\n  };\n  const handleExited = (node, ...args) => {\n    if (node) node.style.visibility = '';\n    onExited == null || onExited(...args);\n  };\n  const renderBackdrop = useCallback(backdropProps => /*#__PURE__*/_jsx(\"div\", {\n    ...backdropProps,\n    className: classNames(`${bsPrefix}-backdrop`, backdropClassName)\n  }), [backdropClassName, bsPrefix]);\n  const renderDialog = dialogProps => /*#__PURE__*/_jsx(\"div\", {\n    ...dialogProps,\n    ...props,\n    className: classNames(className, responsive ? `${bsPrefix}-${responsive}` : bsPrefix, `${bsPrefix}-${placement}`),\n    \"aria-labelledby\": ariaLabelledby,\n    children: children\n  });\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [!showOffcanvas && (responsive || renderStaticNode) && renderDialog({}), /*#__PURE__*/_jsx(ModalContext.Provider, {\n      value: modalContext,\n      children: /*#__PURE__*/_jsx(BaseModal, {\n        show: showOffcanvas,\n        ref: ref,\n        backdrop: backdrop,\n        container: container,\n        keyboard: keyboard,\n        autoFocus: autoFocus,\n        enforceFocus: enforceFocus && !scroll,\n        restoreFocus: restoreFocus,\n        restoreFocusOptions: restoreFocusOptions,\n        onEscapeKeyDown: onEscapeKeyDown,\n        onShow: onShow,\n        onHide: handleHide,\n        onEnter: handleEnter,\n        onEntering: onEntering,\n        onEntered: onEntered,\n        onExit: onExit,\n        onExiting: onExiting,\n        onExited: handleExited,\n        manager: getModalManager(),\n        transition: DialogTransition,\n        backdropTransition: BackdropTransition,\n        renderBackdrop: renderBackdrop,\n        renderDialog: renderDialog\n      })\n    })]\n  });\n});\nOffcanvas.displayName = 'Offcanvas';\nexport default Object.assign(Offcanvas, {\n  Body: OffcanvasBody,\n  Header: OffcanvasHeader,\n  Title: OffcanvasTitle\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACzE,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,qBAAqB,IAAIC,gBAAgB,QAAQ,yBAAyB;AACjF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,OAAO,aAAaN,IAAI,CAACR,iBAAiB,EAAE;IAC1C,GAAGc;EACL,CAAC,CAAC;AACJ;AACA,SAASC,kBAAkBA,CAACD,KAAK,EAAE;EACjC,OAAO,aAAaN,IAAI,CAACV,IAAI,EAAE;IAC7B,GAAGgB;EACL,CAAC,CAAC;AACJ;AACA,MAAME,SAAS,GAAG,aAAazB,KAAK,CAAC0B,UAAU,CAAC,CAAC;EAC/CC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACR,iBAAiB,EAAEC,cAAc;EACjCC,SAAS,GAAG,OAAO;EACnBC,UAAU;EACV;;EAEAC,IAAI,GAAG,KAAK;EACZC,QAAQ,GAAG,IAAI;EACfC,QAAQ,GAAG,IAAI;EACfC,MAAM,GAAG,KAAK;EACdC,eAAe;EACfC,MAAM;EACNC,MAAM;EACNC,SAAS;EACTC,SAAS,GAAG,IAAI;EAChBC,YAAY,GAAG,IAAI;EACnBC,YAAY,GAAG,IAAI;EACnBC,mBAAmB;EACnBC,SAAS;EACTC,MAAM;EACNC,SAAS;EACTC,OAAO;EACPC,UAAU;EACVC,QAAQ;EACRC,iBAAiB;EACjBC,OAAO,EAAEC,YAAY;EACrBC,gBAAgB,GAAG,KAAK;EACxB,GAAG/B;AACL,CAAC,EAAEgC,GAAG,KAAK;EACT,MAAMC,YAAY,GAAGpD,MAAM,CAAC,CAAC;EAC7BuB,QAAQ,GAAGd,kBAAkB,CAACc,QAAQ,EAAE,WAAW,CAAC;EACpD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMsD,UAAU,GAAG5D,gBAAgB,CAACwC,MAAM,CAAC;EAC3C,MAAMqB,uBAAuB,GAAG9D,aAAa,CAACkC,UAAU,IAAI,IAAI,EAAE,IAAI,CAAC;EACvE9B,SAAS,CAAC,MAAM;IACd;IACA;IACAwD,gBAAgB,CAAC1B,UAAU,GAAGC,IAAI,IAAI,CAAC2B,uBAAuB,GAAG3B,IAAI,CAAC;EACxE,CAAC,EAAE,CAACA,IAAI,EAAED,UAAU,EAAE4B,uBAAuB,CAAC,CAAC;EAC/C,MAAMC,YAAY,GAAG1D,OAAO,CAAC,OAAO;IAClCoC,MAAM,EAAEoB;EACV,CAAC,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EACjB,SAASG,eAAeA,CAAA,EAAG;IACzB,IAAIT,YAAY,EAAE,OAAOA,YAAY;IACrC,IAAIjB,MAAM,EAAE;MACV;MACA;MACA,IAAI,CAACoB,YAAY,CAACO,OAAO,EAAEP,YAAY,CAACO,OAAO,GAAG,IAAIjD,qBAAqB,CAAC;QAC1EkD,uBAAuB,EAAE;MAC3B,CAAC,CAAC;MACF,OAAOR,YAAY,CAACO,OAAO;IAC7B;IACA,OAAOhD,gBAAgB,CAAC,CAAC;EAC3B;EACA,MAAMkD,WAAW,GAAGA,CAACC,IAAI,EAAE,GAAGC,IAAI,KAAK;IACrC,IAAID,IAAI,EAAEA,IAAI,CAACE,KAAK,CAACC,UAAU,GAAG,SAAS;IAC3CrB,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACkB,IAAI,EAAE,GAAGC,IAAI,CAAC;EAC3C,CAAC;EACD,MAAMG,YAAY,GAAGA,CAACJ,IAAI,EAAE,GAAGC,IAAI,KAAK;IACtC,IAAID,IAAI,EAAEA,IAAI,CAACE,KAAK,CAACC,UAAU,GAAG,EAAE;IACpCnB,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC,GAAGiB,IAAI,CAAC;EACvC,CAAC;EACD,MAAMI,cAAc,GAAGtE,WAAW,CAACuE,aAAa,IAAI,aAAavD,IAAI,CAAC,KAAK,EAAE;IAC3E,GAAGuD,aAAa;IAChB5C,SAAS,EAAE/B,UAAU,CAAC,GAAG8B,QAAQ,WAAW,EAAEwB,iBAAiB;EACjE,CAAC,CAAC,EAAE,CAACA,iBAAiB,EAAExB,QAAQ,CAAC,CAAC;EAClC,MAAM8C,YAAY,GAAGC,WAAW,IAAI,aAAazD,IAAI,CAAC,KAAK,EAAE;IAC3D,GAAGyD,WAAW;IACd,GAAGnD,KAAK;IACRK,SAAS,EAAE/B,UAAU,CAAC+B,SAAS,EAAEI,UAAU,GAAG,GAAGL,QAAQ,IAAIK,UAAU,EAAE,GAAGL,QAAQ,EAAE,GAAGA,QAAQ,IAAII,SAAS,EAAE,CAAC;IACjH,iBAAiB,EAAED,cAAc;IACjCD,QAAQ,EAAEA;EACZ,CAAC,CAAC;EACF,OAAO,aAAaR,KAAK,CAACF,SAAS,EAAE;IACnCU,QAAQ,EAAE,CAAC,CAAC4B,aAAa,KAAKzB,UAAU,IAAIsB,gBAAgB,CAAC,IAAImB,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,aAAaxD,IAAI,CAACP,YAAY,CAACiE,QAAQ,EAAE;MAC1HC,KAAK,EAAEf,YAAY;MACnBhC,QAAQ,EAAE,aAAaZ,IAAI,CAACX,SAAS,EAAE;QACrC2B,IAAI,EAAEwB,aAAa;QACnBF,GAAG,EAAEA,GAAG;QACRrB,QAAQ,EAAEA,QAAQ;QAClBM,SAAS,EAAEA,SAAS;QACpBL,QAAQ,EAAEA,QAAQ;QAClBM,SAAS,EAAEA,SAAS;QACpBC,YAAY,EAAEA,YAAY,IAAI,CAACN,MAAM;QACrCO,YAAY,EAAEA,YAAY;QAC1BC,mBAAmB,EAAEA,mBAAmB;QACxCP,eAAe,EAAEA,eAAe;QAChCC,MAAM,EAAEA,MAAM;QACdC,MAAM,EAAEoB,UAAU;QAClBX,OAAO,EAAEiB,WAAW;QACpBhB,UAAU,EAAEA,UAAU;QACtBJ,SAAS,EAAEA,SAAS;QACpBC,MAAM,EAAEA,MAAM;QACdC,SAAS,EAAEA,SAAS;QACpBG,QAAQ,EAAEoB,YAAY;QACtBlB,OAAO,EAAEU,eAAe,CAAC,CAAC;QAC1Be,UAAU,EAAEvD,gBAAgB;QAC5BwD,kBAAkB,EAAEtD,kBAAkB;QACtC+C,cAAc,EAAEA,cAAc;QAC9BE,YAAY,EAAEA;MAChB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFhD,SAAS,CAACsD,WAAW,GAAG,WAAW;AACnC,eAAeC,MAAM,CAACC,MAAM,CAACxD,SAAS,EAAE;EACtCyD,IAAI,EAAE1E,aAAa;EACnB2E,MAAM,EAAExE,eAAe;EACvByE,KAAK,EAAExE;AACT,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}