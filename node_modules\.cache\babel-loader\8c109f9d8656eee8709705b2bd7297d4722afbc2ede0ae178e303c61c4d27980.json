{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_vsco = register(\"vsco\", {\n  \"color\": \"#83878A\",\n  \"path\": \"M0 0v64h64V0zm18.5 34.5c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5S21 30.6 21 32c-.1 1.4-1.2 2.5-2.5 2.5m6.6 6.6c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5m.1-13.4c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5M32 48c-1.4 0-2.5-1.1-2.5-2.5S30.6 43 32 43s2.5 1.1 2.5 2.5S33.4 48 32 48m-2.5-16.1c0-1.4 1.1-2.5 2.5-2.5s2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5-2.5-1.1-2.5-2.5m2.5-11c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5m6.7 1.8c1.4 0 2.5 1.1 2.5 2.5s-1.1 2.5-2.5 2.5-2.5-1.1-2.5-2.5 1.1-2.5 2.5-2.5m.1 18.5c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5c-.1 1.4-1.2 2.5-2.5 2.5m6.7-6.7c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5S48 30.6 48 32s-1.1 2.5-2.5 2.5\"\n});\nexport { _socialIcons_vsco as default };", "map": {"version": 3, "names": ["register", "_socialIcons_vsco", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/vsco.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_vsco = register(\"vsco\", {\"color\":\"#83878A\",\"path\":\"M0 0v64h64V0zm18.5 34.5c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5S21 30.6 21 32c-.1 1.4-1.2 2.5-2.5 2.5m6.6 6.6c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5m.1-13.4c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5M32 48c-1.4 0-2.5-1.1-2.5-2.5S30.6 43 32 43s2.5 1.1 2.5 2.5S33.4 48 32 48m-2.5-16.1c0-1.4 1.1-2.5 2.5-2.5s2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5-2.5-1.1-2.5-2.5m2.5-11c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5m6.7 1.8c1.4 0 2.5 1.1 2.5 2.5s-1.1 2.5-2.5 2.5-2.5-1.1-2.5-2.5 1.1-2.5 2.5-2.5m.1 18.5c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5c-.1 1.4-1.2 2.5-2.5 2.5m6.7-6.7c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5S48 30.6 48 32s-1.1 2.5-2.5 2.5\"});\n\nexport { _socialIcons_vsco as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,iBAAiB,GAAGD,QAAQ,CAAC,MAAM,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAA+tB,CAAC,CAAC;AAEpyB,SAASC,iBAAiB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}