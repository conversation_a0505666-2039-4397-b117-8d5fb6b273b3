{"ast": null, "code": "/** @license React v17.0.2\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    var React = require('react');\n    var _assign = require('object-assign');\n\n    // ATTENTION\n    // When adding new symbols to this file,\n    // Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n    // The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n    // nor polyfill, then a plain number is used for performance.\n    var REACT_ELEMENT_TYPE = 0xeac7;\n    var REACT_PORTAL_TYPE = 0xeaca;\n    exports.Fragment = 0xeacb;\n    var REACT_STRICT_MODE_TYPE = 0xeacc;\n    var REACT_PROFILER_TYPE = 0xead2;\n    var REACT_PROVIDER_TYPE = 0xeacd;\n    var REACT_CONTEXT_TYPE = 0xeace;\n    var REACT_FORWARD_REF_TYPE = 0xead0;\n    var REACT_SUSPENSE_TYPE = 0xead1;\n    var REACT_SUSPENSE_LIST_TYPE = 0xead8;\n    var REACT_MEMO_TYPE = 0xead3;\n    var REACT_LAZY_TYPE = 0xead4;\n    var REACT_BLOCK_TYPE = 0xead9;\n    var REACT_SERVER_BLOCK_TYPE = 0xeada;\n    var REACT_FUNDAMENTAL_TYPE = 0xead5;\n    var REACT_SCOPE_TYPE = 0xead7;\n    var REACT_OPAQUE_ID_TYPE = 0xeae0;\n    var REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\n    var REACT_OFFSCREEN_TYPE = 0xeae2;\n    var REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n    if (typeof Symbol === 'function' && Symbol.for) {\n      var symbolFor = Symbol.for;\n      REACT_ELEMENT_TYPE = symbolFor('react.element');\n      REACT_PORTAL_TYPE = symbolFor('react.portal');\n      exports.Fragment = symbolFor('react.fragment');\n      REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n      REACT_PROFILER_TYPE = symbolFor('react.profiler');\n      REACT_PROVIDER_TYPE = symbolFor('react.provider');\n      REACT_CONTEXT_TYPE = symbolFor('react.context');\n      REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n      REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n      REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n      REACT_MEMO_TYPE = symbolFor('react.memo');\n      REACT_LAZY_TYPE = symbolFor('react.lazy');\n      REACT_BLOCK_TYPE = symbolFor('react.block');\n      REACT_SERVER_BLOCK_TYPE = symbolFor('react.server.block');\n      REACT_FUNDAMENTAL_TYPE = symbolFor('react.fundamental');\n      REACT_SCOPE_TYPE = symbolFor('react.scope');\n      REACT_OPAQUE_ID_TYPE = symbolFor('react.opaque.id');\n      REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n      REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n      REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n    }\n    var MAYBE_ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n    var FAUX_ITERATOR_SYMBOL = '@@iterator';\n    function getIteratorFn(maybeIterable) {\n      if (maybeIterable === null || typeof maybeIterable !== 'object') {\n        return null;\n      }\n      var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n      if (typeof maybeIterator === 'function') {\n        return maybeIterator;\n      }\n      return null;\n    }\n    var ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n    function error(format) {\n      {\n        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          args[_key2 - 1] = arguments[_key2];\n        }\n        printWarning('error', format, args);\n      }\n    }\n    function printWarning(level, format, args) {\n      // When changing this logic, you might want to also\n      // update consoleWithStackDev.www.js as well.\n      {\n        var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n        var stack = ReactDebugCurrentFrame.getStackAddendum();\n        if (stack !== '') {\n          format += '%s';\n          args = args.concat([stack]);\n        }\n        var argsWithFormat = args.map(function (item) {\n          return '' + item;\n        }); // Careful: RN currently depends on this prefix\n\n        argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n        // breaks IE9: https://github.com/facebook/react/issues/13610\n        // eslint-disable-next-line react-internal/no-production-logging\n\n        Function.prototype.apply.call(console[level], console, argsWithFormat);\n      }\n    }\n\n    // Filter certain DOM attributes (e.g. src, href) if their values are empty strings.\n\n    var enableScopeAPI = false; // Experimental Create Event Handle API.\n\n    function isValidElementType(type) {\n      if (typeof type === 'string' || typeof type === 'function') {\n        return true;\n      } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n      if (type === exports.Fragment || type === REACT_PROFILER_TYPE || type === REACT_DEBUG_TRACING_MODE_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_LEGACY_HIDDEN_TYPE || enableScopeAPI) {\n        return true;\n      }\n      if (typeof type === 'object' && type !== null) {\n        if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_BLOCK_TYPE || type[0] === REACT_SERVER_BLOCK_TYPE) {\n          return true;\n        }\n      }\n      return false;\n    }\n    function getWrappedName(outerType, innerType, wrapperName) {\n      var functionName = innerType.displayName || innerType.name || '';\n      return outerType.displayName || (functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName);\n    }\n    function getContextName(type) {\n      return type.displayName || 'Context';\n    }\n    function getComponentName(type) {\n      if (type == null) {\n        // Host root, text node or just invalid type.\n        return null;\n      }\n      {\n        if (typeof type.tag === 'number') {\n          error('Received an unexpected object in getComponentName(). ' + 'This is likely a bug in React. Please file an issue.');\n        }\n      }\n      if (typeof type === 'function') {\n        return type.displayName || type.name || null;\n      }\n      if (typeof type === 'string') {\n        return type;\n      }\n      switch (type) {\n        case exports.Fragment:\n          return 'Fragment';\n        case REACT_PORTAL_TYPE:\n          return 'Portal';\n        case REACT_PROFILER_TYPE:\n          return 'Profiler';\n        case REACT_STRICT_MODE_TYPE:\n          return 'StrictMode';\n        case REACT_SUSPENSE_TYPE:\n          return 'Suspense';\n        case REACT_SUSPENSE_LIST_TYPE:\n          return 'SuspenseList';\n      }\n      if (typeof type === 'object') {\n        switch (type.$$typeof) {\n          case REACT_CONTEXT_TYPE:\n            var context = type;\n            return getContextName(context) + '.Consumer';\n          case REACT_PROVIDER_TYPE:\n            var provider = type;\n            return getContextName(provider._context) + '.Provider';\n          case REACT_FORWARD_REF_TYPE:\n            return getWrappedName(type, type.render, 'ForwardRef');\n          case REACT_MEMO_TYPE:\n            return getComponentName(type.type);\n          case REACT_BLOCK_TYPE:\n            return getComponentName(type._render);\n          case REACT_LAZY_TYPE:\n            {\n              var lazyComponent = type;\n              var payload = lazyComponent._payload;\n              var init = lazyComponent._init;\n              try {\n                return getComponentName(init(payload));\n              } catch (x) {\n                return null;\n              }\n            }\n        }\n      }\n      return null;\n    }\n\n    // Helpers to patch console.logs to avoid logging during side-effect free\n    // replaying on render function. This currently only patches the object\n    // lazily which won't cover if the log function was extracted eagerly.\n    // We could also eagerly patch the method.\n    var disabledDepth = 0;\n    var prevLog;\n    var prevInfo;\n    var prevWarn;\n    var prevError;\n    var prevGroup;\n    var prevGroupCollapsed;\n    var prevGroupEnd;\n    function disabledLog() {}\n    disabledLog.__reactDisabledLog = true;\n    function disableLogs() {\n      {\n        if (disabledDepth === 0) {\n          /* eslint-disable react-internal/no-production-logging */\n          prevLog = console.log;\n          prevInfo = console.info;\n          prevWarn = console.warn;\n          prevError = console.error;\n          prevGroup = console.group;\n          prevGroupCollapsed = console.groupCollapsed;\n          prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n          var props = {\n            configurable: true,\n            enumerable: true,\n            value: disabledLog,\n            writable: true\n          }; // $FlowFixMe Flow thinks console is immutable.\n\n          Object.defineProperties(console, {\n            info: props,\n            log: props,\n            warn: props,\n            error: props,\n            group: props,\n            groupCollapsed: props,\n            groupEnd: props\n          });\n          /* eslint-enable react-internal/no-production-logging */\n        }\n        disabledDepth++;\n      }\n    }\n    function reenableLogs() {\n      {\n        disabledDepth--;\n        if (disabledDepth === 0) {\n          /* eslint-disable react-internal/no-production-logging */\n          var props = {\n            configurable: true,\n            enumerable: true,\n            writable: true\n          }; // $FlowFixMe Flow thinks console is immutable.\n\n          Object.defineProperties(console, {\n            log: _assign({}, props, {\n              value: prevLog\n            }),\n            info: _assign({}, props, {\n              value: prevInfo\n            }),\n            warn: _assign({}, props, {\n              value: prevWarn\n            }),\n            error: _assign({}, props, {\n              value: prevError\n            }),\n            group: _assign({}, props, {\n              value: prevGroup\n            }),\n            groupCollapsed: _assign({}, props, {\n              value: prevGroupCollapsed\n            }),\n            groupEnd: _assign({}, props, {\n              value: prevGroupEnd\n            })\n          });\n          /* eslint-enable react-internal/no-production-logging */\n        }\n        if (disabledDepth < 0) {\n          error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n        }\n      }\n    }\n    var ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\n    var prefix;\n    function describeBuiltInComponentFrame(name, source, ownerFn) {\n      {\n        if (prefix === undefined) {\n          // Extract the VM specific prefix used by each line.\n          try {\n            throw Error();\n          } catch (x) {\n            var match = x.stack.trim().match(/\\n( *(at )?)/);\n            prefix = match && match[1] || '';\n          }\n        } // We use the prefix to ensure our stacks line up with native stack frames.\n\n        return '\\n' + prefix + name;\n      }\n    }\n    var reentry = false;\n    var componentFrameCache;\n    {\n      var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n      componentFrameCache = new PossiblyWeakMap();\n    }\n    function describeNativeComponentFrame(fn, construct) {\n      // If something asked for a stack inside a fake render, it should get ignored.\n      if (!fn || reentry) {\n        return '';\n      }\n      {\n        var frame = componentFrameCache.get(fn);\n        if (frame !== undefined) {\n          return frame;\n        }\n      }\n      var control;\n      reentry = true;\n      var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n      Error.prepareStackTrace = undefined;\n      var previousDispatcher;\n      {\n        previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n        // for warnings.\n\n        ReactCurrentDispatcher.current = null;\n        disableLogs();\n      }\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            }\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          }\n          fn();\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          // This extracts the first frame from the sample that isn't also in the control.\n          // Skipping one frame that we assume is the frame that calls the two.\n          var sampleLines = sample.stack.split('\\n');\n          var controlLines = control.stack.split('\\n');\n          var s = sampleLines.length - 1;\n          var c = controlLines.length - 1;\n          while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n            // We expect at least one stack frame to be shared.\n            // Typically this will be the root most one. However, stack frames may be\n            // cut off due to maximum stack limits. In this case, one maybe cut off\n            // earlier than the other. We assume that the sample is longer or the same\n            // and there for cut off earlier. So we should find the root most frame in\n            // the sample somewhere in the control.\n            c--;\n          }\n          for (; s >= 1 && c >= 0; s--, c--) {\n            // Next we find the first one that isn't the same which should be the\n            // frame that called our sample function and the control.\n            if (sampleLines[s] !== controlLines[c]) {\n              // In V8, the first line is describing the message but other VMs don't.\n              // If we're about to return the first line, and the control is also on the same\n              // line, that's a pretty good indicator that our sample threw at same line as\n              // the control. I.e. before we entered the sample frame. So we ignore this result.\n              // This can happen if you passed a class to function component, or non-function.\n              if (s !== 1 || c !== 1) {\n                do {\n                  s--;\n                  c--; // We may still have similar intermediate frames from the construct call.\n                  // The next one that isn't the same should be our match though.\n\n                  if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                    // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                    var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at ');\n                    {\n                      if (typeof fn === 'function') {\n                        componentFrameCache.set(fn, _frame);\n                      }\n                    } // Return the line we found.\n\n                    return _frame;\n                  }\n                } while (s >= 1 && c >= 0);\n              }\n              break;\n            }\n          }\n        }\n      } finally {\n        reentry = false;\n        {\n          ReactCurrentDispatcher.current = previousDispatcher;\n          reenableLogs();\n        }\n        Error.prepareStackTrace = previousPrepareStackTrace;\n      } // Fallback to just using the name if we couldn't make it throw.\n\n      var name = fn ? fn.displayName || fn.name : '';\n      var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n      {\n        if (typeof fn === 'function') {\n          componentFrameCache.set(fn, syntheticFrame);\n        }\n      }\n      return syntheticFrame;\n    }\n    function describeFunctionComponentFrame(fn, source, ownerFn) {\n      {\n        return describeNativeComponentFrame(fn, false);\n      }\n    }\n    function shouldConstruct(Component) {\n      var prototype = Component.prototype;\n      return !!(prototype && prototype.isReactComponent);\n    }\n    function describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n      if (type == null) {\n        return '';\n      }\n      if (typeof type === 'function') {\n        {\n          return describeNativeComponentFrame(type, shouldConstruct(type));\n        }\n      }\n      if (typeof type === 'string') {\n        return describeBuiltInComponentFrame(type);\n      }\n      switch (type) {\n        case REACT_SUSPENSE_TYPE:\n          return describeBuiltInComponentFrame('Suspense');\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame('SuspenseList');\n      }\n      if (typeof type === 'object') {\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return describeFunctionComponentFrame(type.render);\n          case REACT_MEMO_TYPE:\n            // Memo may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n          case REACT_BLOCK_TYPE:\n            return describeFunctionComponentFrame(type._render);\n          case REACT_LAZY_TYPE:\n            {\n              var lazyComponent = type;\n              var payload = lazyComponent._payload;\n              var init = lazyComponent._init;\n              try {\n                // Lazy may contain any component type so we recursively resolve it.\n                return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n              } catch (x) {}\n            }\n        }\n      }\n      return '';\n    }\n    var loggedTypeFailures = {};\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    function setCurrentlyValidatingElement(element) {\n      {\n        if (element) {\n          var owner = element._owner;\n          var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n          ReactDebugCurrentFrame.setExtraStackFrame(stack);\n        } else {\n          ReactDebugCurrentFrame.setExtraStackFrame(null);\n        }\n      }\n    }\n    function checkPropTypes(typeSpecs, values, location, componentName, element) {\n      {\n        // $FlowFixMe This is okay but Flow doesn't know it.\n        var has = Function.call.bind(Object.prototype.hasOwnProperty);\n        for (var typeSpecName in typeSpecs) {\n          if (has(typeSpecs, typeSpecName)) {\n            var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n            // fail the render phase where it didn't fail before. So we log it.\n            // After these have been cleaned up, we'll let them throw.\n\n            try {\n              // This is intentionally an invariant that gets caught. It's the same\n              // behavior as without this statement except with a better message.\n              if (typeof typeSpecs[typeSpecName] !== 'function') {\n                var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n                err.name = 'Invariant Violation';\n                throw err;\n              }\n              error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n            } catch (ex) {\n              error$1 = ex;\n            }\n            if (error$1 && !(error$1 instanceof Error)) {\n              setCurrentlyValidatingElement(element);\n              error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n              setCurrentlyValidatingElement(null);\n            }\n            if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n              // Only monitor this failure once because there tends to be a lot of the\n              // same error.\n              loggedTypeFailures[error$1.message] = true;\n              setCurrentlyValidatingElement(element);\n              error('Failed %s type: %s', location, error$1.message);\n              setCurrentlyValidatingElement(null);\n            }\n          }\n        }\n      }\n    }\n    var ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\n    var hasOwnProperty = Object.prototype.hasOwnProperty;\n    var RESERVED_PROPS = {\n      key: true,\n      ref: true,\n      __self: true,\n      __source: true\n    };\n    var specialPropKeyWarningShown;\n    var specialPropRefWarningShown;\n    var didWarnAboutStringRefs;\n    {\n      didWarnAboutStringRefs = {};\n    }\n    function hasValidRef(config) {\n      {\n        if (hasOwnProperty.call(config, 'ref')) {\n          var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n          if (getter && getter.isReactWarning) {\n            return false;\n          }\n        }\n      }\n      return config.ref !== undefined;\n    }\n    function hasValidKey(config) {\n      {\n        if (hasOwnProperty.call(config, 'key')) {\n          var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n          if (getter && getter.isReactWarning) {\n            return false;\n          }\n        }\n      }\n      return config.key !== undefined;\n    }\n    function warnIfStringRefCannotBeAutoConverted(config, self) {\n      {\n        if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n          var componentName = getComponentName(ReactCurrentOwner.current.type);\n          if (!didWarnAboutStringRefs[componentName]) {\n            error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentName(ReactCurrentOwner.current.type), config.ref);\n            didWarnAboutStringRefs[componentName] = true;\n          }\n        }\n      }\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      {\n        var warnAboutAccessingKey = function () {\n          if (!specialPropKeyWarningShown) {\n            specialPropKeyWarningShown = true;\n            error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n          }\n        };\n        warnAboutAccessingKey.isReactWarning = true;\n        Object.defineProperty(props, 'key', {\n          get: warnAboutAccessingKey,\n          configurable: true\n        });\n      }\n    }\n    function defineRefPropWarningGetter(props, displayName) {\n      {\n        var warnAboutAccessingRef = function () {\n          if (!specialPropRefWarningShown) {\n            specialPropRefWarningShown = true;\n            error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n          }\n        };\n        warnAboutAccessingRef.isReactWarning = true;\n        Object.defineProperty(props, 'ref', {\n          get: warnAboutAccessingRef,\n          configurable: true\n        });\n      }\n    }\n    /**\n     * Factory method to create a new React element. This no longer adheres to\n     * the class pattern, so do not use new to call it. Also, instanceof check\n     * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n     * if something is a React Element.\n     *\n     * @param {*} type\n     * @param {*} props\n     * @param {*} key\n     * @param {string|object} ref\n     * @param {*} owner\n     * @param {*} self A *temporary* helper to detect places where `this` is\n     * different from the `owner` when React.createElement is called, so that we\n     * can warn. We want to get rid of owner and replace string `ref`s with arrow\n     * functions, and as long as `this` and owner are the same, there will be no\n     * change in behavior.\n     * @param {*} source An annotation object (added by a transpiler or otherwise)\n     * indicating filename, line number, and/or other information.\n     * @internal\n     */\n\n    var ReactElement = function (type, key, ref, self, source, owner, props) {\n      var element = {\n        // This tag allows us to uniquely identify this as a React Element\n        $$typeof: REACT_ELEMENT_TYPE,\n        // Built-in properties that belong on the element\n        type: type,\n        key: key,\n        ref: ref,\n        props: props,\n        // Record the component responsible for creating this element.\n        _owner: owner\n      };\n      {\n        // The validation flag is currently mutative. We put it on\n        // an external backing store so that we can freeze the whole object.\n        // This can be replaced with a WeakMap once they are implemented in\n        // commonly used development environments.\n        element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n        // the validation flag non-enumerable (where possible, which should\n        // include every environment we run tests in), so the test framework\n        // ignores it.\n\n        Object.defineProperty(element._store, 'validated', {\n          configurable: false,\n          enumerable: false,\n          writable: true,\n          value: false\n        }); // self and source are DEV only properties.\n\n        Object.defineProperty(element, '_self', {\n          configurable: false,\n          enumerable: false,\n          writable: false,\n          value: self\n        }); // Two elements created in two different places should be considered\n        // equal for testing purposes and therefore we hide it from enumeration.\n\n        Object.defineProperty(element, '_source', {\n          configurable: false,\n          enumerable: false,\n          writable: false,\n          value: source\n        });\n        if (Object.freeze) {\n          Object.freeze(element.props);\n          Object.freeze(element);\n        }\n      }\n      return element;\n    };\n    /**\n     * https://github.com/reactjs/rfcs/pull/107\n     * @param {*} type\n     * @param {object} props\n     * @param {string} key\n     */\n\n    function jsxDEV(type, config, maybeKey, source, self) {\n      {\n        var propName; // Reserved names are extracted\n\n        var props = {};\n        var key = null;\n        var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n        // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n        // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n        // but as an intermediary step, we will use jsxDEV for everything except\n        // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n        // key is explicitly declared to be undefined or not.\n\n        if (maybeKey !== undefined) {\n          key = '' + maybeKey;\n        }\n        if (hasValidKey(config)) {\n          key = '' + config.key;\n        }\n        if (hasValidRef(config)) {\n          ref = config.ref;\n          warnIfStringRefCannotBeAutoConverted(config, self);\n        } // Remaining properties are added to a new props object\n\n        for (propName in config) {\n          if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n            props[propName] = config[propName];\n          }\n        } // Resolve default props\n\n        if (type && type.defaultProps) {\n          var defaultProps = type.defaultProps;\n          for (propName in defaultProps) {\n            if (props[propName] === undefined) {\n              props[propName] = defaultProps[propName];\n            }\n          }\n        }\n        if (key || ref) {\n          var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n          if (key) {\n            defineKeyPropWarningGetter(props, displayName);\n          }\n          if (ref) {\n            defineRefPropWarningGetter(props, displayName);\n          }\n        }\n        return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n      }\n    }\n    var ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\n    var ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n    function setCurrentlyValidatingElement$1(element) {\n      {\n        if (element) {\n          var owner = element._owner;\n          var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n          ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n        } else {\n          ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n        }\n      }\n    }\n    var propTypesMisspellWarningShown;\n    {\n      propTypesMisspellWarningShown = false;\n    }\n    /**\n     * Verifies the object is a ReactElement.\n     * See https://reactjs.org/docs/react-api.html#isvalidelement\n     * @param {?object} object\n     * @return {boolean} True if `object` is a ReactElement.\n     * @final\n     */\n\n    function isValidElement(object) {\n      {\n        return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n      }\n    }\n    function getDeclarationErrorAddendum() {\n      {\n        if (ReactCurrentOwner$1.current) {\n          var name = getComponentName(ReactCurrentOwner$1.current.type);\n          if (name) {\n            return '\\n\\nCheck the render method of `' + name + '`.';\n          }\n        }\n        return '';\n      }\n    }\n    function getSourceInfoErrorAddendum(source) {\n      {\n        if (source !== undefined) {\n          var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n          var lineNumber = source.lineNumber;\n          return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n        }\n        return '';\n      }\n    }\n    /**\n     * Warn if there's no key explicitly set on dynamic arrays of children or\n     * object keys are not valid. This allows us to keep track of children between\n     * updates.\n     */\n\n    var ownerHasKeyUseWarning = {};\n    function getCurrentComponentErrorInfo(parentType) {\n      {\n        var info = getDeclarationErrorAddendum();\n        if (!info) {\n          var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n          if (parentName) {\n            info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n          }\n        }\n        return info;\n      }\n    }\n    /**\n     * Warn if the element doesn't have an explicit key assigned to it.\n     * This element is in an array. The array could grow and shrink or be\n     * reordered. All children that haven't already been validated are required to\n     * have a \"key\" property assigned to it. Error statuses are cached so a warning\n     * will only be shown once.\n     *\n     * @internal\n     * @param {ReactElement} element Element that requires a key.\n     * @param {*} parentType element's parent's type.\n     */\n\n    function validateExplicitKey(element, parentType) {\n      {\n        if (!element._store || element._store.validated || element.key != null) {\n          return;\n        }\n        element._store.validated = true;\n        var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n        if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n          return;\n        }\n        ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n        // property, it may be the creator of the child that's responsible for\n        // assigning it a key.\n\n        var childOwner = '';\n        if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n          // Give the component that originally created this child.\n          childOwner = \" It was passed a child from \" + getComponentName(element._owner.type) + \".\";\n        }\n        setCurrentlyValidatingElement$1(element);\n        error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n        setCurrentlyValidatingElement$1(null);\n      }\n    }\n    /**\n     * Ensure that every element either is passed in a static location, in an\n     * array with an explicit keys property defined, or in an object literal\n     * with valid key property.\n     *\n     * @internal\n     * @param {ReactNode} node Statically passed child of any type.\n     * @param {*} parentType node's parent's type.\n     */\n\n    function validateChildKeys(node, parentType) {\n      {\n        if (typeof node !== 'object') {\n          return;\n        }\n        if (Array.isArray(node)) {\n          for (var i = 0; i < node.length; i++) {\n            var child = node[i];\n            if (isValidElement(child)) {\n              validateExplicitKey(child, parentType);\n            }\n          }\n        } else if (isValidElement(node)) {\n          // This element was passed in a valid location.\n          if (node._store) {\n            node._store.validated = true;\n          }\n        } else if (node) {\n          var iteratorFn = getIteratorFn(node);\n          if (typeof iteratorFn === 'function') {\n            // Entry iterators used to provide implicit keys,\n            // but now we print a separate warning for them later.\n            if (iteratorFn !== node.entries) {\n              var iterator = iteratorFn.call(node);\n              var step;\n              while (!(step = iterator.next()).done) {\n                if (isValidElement(step.value)) {\n                  validateExplicitKey(step.value, parentType);\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    /**\n     * Given an element, validate that its props follow the propTypes definition,\n     * provided by the type.\n     *\n     * @param {ReactElement} element\n     */\n\n    function validatePropTypes(element) {\n      {\n        var type = element.type;\n        if (type === null || type === undefined || typeof type === 'string') {\n          return;\n        }\n        var propTypes;\n        if (typeof type === 'function') {\n          propTypes = type.propTypes;\n        } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE ||\n        // Note: Memo only checks outer props here.\n        // Inner props are checked in the reconciler.\n        type.$$typeof === REACT_MEMO_TYPE)) {\n          propTypes = type.propTypes;\n        } else {\n          return;\n        }\n        if (propTypes) {\n          // Intentionally inside to avoid triggering lazy initializers:\n          var name = getComponentName(type);\n          checkPropTypes(propTypes, element.props, 'prop', name, element);\n        } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n          propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n          var _name = getComponentName(type);\n          error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n        }\n        if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n          error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n        }\n      }\n    }\n    /**\n     * Given a fragment, validate that it can only be provided with fragment props\n     * @param {ReactElement} fragment\n     */\n\n    function validateFragmentProps(fragment) {\n      {\n        var keys = Object.keys(fragment.props);\n        for (var i = 0; i < keys.length; i++) {\n          var key = keys[i];\n          if (key !== 'children' && key !== 'key') {\n            setCurrentlyValidatingElement$1(fragment);\n            error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n            setCurrentlyValidatingElement$1(null);\n            break;\n          }\n        }\n        if (fragment.ref !== null) {\n          setCurrentlyValidatingElement$1(fragment);\n          error('Invalid attribute `ref` supplied to `React.Fragment`.');\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n    function jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n      {\n        var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n        // succeed and there will likely be errors in render.\n\n        if (!validType) {\n          var info = '';\n          if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n            info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n          }\n          var sourceInfo = getSourceInfoErrorAddendum(source);\n          if (sourceInfo) {\n            info += sourceInfo;\n          } else {\n            info += getDeclarationErrorAddendum();\n          }\n          var typeString;\n          if (type === null) {\n            typeString = 'null';\n          } else if (Array.isArray(type)) {\n            typeString = 'array';\n          } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n            typeString = \"<\" + (getComponentName(type.type) || 'Unknown') + \" />\";\n            info = ' Did you accidentally export a JSX literal instead of a component?';\n          } else {\n            typeString = typeof type;\n          }\n          error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n        }\n        var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n        // TODO: Drop this when these are no longer allowed as the type argument.\n\n        if (element == null) {\n          return element;\n        } // Skip key warning if the type isn't valid since our key validation logic\n        // doesn't expect a non-string/function type and can throw confusing errors.\n        // We don't want exception behavior to differ between dev and prod.\n        // (Rendering will throw with a helpful message and as soon as the type is\n        // fixed, the key warnings will appear.)\n\n        if (validType) {\n          var children = props.children;\n          if (children !== undefined) {\n            if (isStaticChildren) {\n              if (Array.isArray(children)) {\n                for (var i = 0; i < children.length; i++) {\n                  validateChildKeys(children[i], type);\n                }\n                if (Object.freeze) {\n                  Object.freeze(children);\n                }\n              } else {\n                error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n              }\n            } else {\n              validateChildKeys(children, type);\n            }\n          }\n        }\n        if (type === exports.Fragment) {\n          validateFragmentProps(element);\n        } else {\n          validatePropTypes(element);\n        }\n        return element;\n      }\n    } // These two functions exist to still get child warnings in dev\n\n    var jsxDEV$1 = jsxWithValidation;\n    exports.jsxDEV = jsxDEV$1;\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "React", "require", "_assign", "REACT_ELEMENT_TYPE", "REACT_PORTAL_TYPE", "exports", "Fragment", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_PROVIDER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_BLOCK_TYPE", "REACT_SERVER_BLOCK_TYPE", "REACT_FUNDAMENTAL_TYPE", "REACT_SCOPE_TYPE", "REACT_OPAQUE_ID_TYPE", "REACT_DEBUG_TRACING_MODE_TYPE", "REACT_OFFSCREEN_TYPE", "REACT_LEGACY_HIDDEN_TYPE", "Symbol", "for", "symbolFor", "MAYBE_ITERATOR_SYMBOL", "iterator", "FAUX_ITERATOR_SYMBOL", "getIteratorFn", "maybeIterable", "maybeIterator", "ReactSharedInternals", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "error", "format", "_len2", "arguments", "length", "args", "Array", "_key2", "printWarning", "level", "ReactDebugCurrentFrame", "stack", "getStackAddendum", "concat", "argsWithFormat", "map", "item", "unshift", "Function", "prototype", "apply", "call", "console", "enableScopeAPI", "isValidElementType", "type", "$$typeof", "getWrappedName", "outerType", "innerType", "wrapperName", "functionName", "displayName", "name", "getContextName", "getComponentName", "tag", "context", "provider", "_context", "render", "_render", "lazyComponent", "payload", "_payload", "init", "_init", "x", "<PERSON><PERSON><PERSON><PERSON>", "prevLog", "prevInfo", "prev<PERSON>arn", "prevError", "prevGroup", "prevGroupCollapsed", "prevGroupEnd", "disabledLog", "__reactDisabledLog", "disableLogs", "log", "info", "warn", "group", "groupCollapsed", "groupEnd", "props", "configurable", "enumerable", "value", "writable", "Object", "defineProperties", "reenableLogs", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix", "describeBuiltInComponentFrame", "source", "ownerFn", "undefined", "Error", "match", "trim", "reentry", "componentFrameCache", "PossiblyWeakMap", "WeakMap", "Map", "describeNativeComponentFrame", "fn", "construct", "frame", "get", "control", "previousPrepareStackTrace", "prepareStackTrace", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "Fake", "defineProperty", "set", "Reflect", "sample", "sampleLines", "split", "controlLines", "s", "c", "_frame", "replace", "syntheticFrame", "describeFunctionComponentFrame", "shouldConstruct", "Component", "isReactComponent", "describeUnknownElementTypeFrameInDEV", "loggedTypeFailures", "setCurrentlyValidatingElement", "element", "owner", "_owner", "_source", "setExtraStackFrame", "checkPropTypes", "typeSpecs", "values", "location", "componentName", "has", "bind", "hasOwnProperty", "typeSpecName", "error$1", "err", "ex", "message", "ReactCurrentOwner", "RESERVED_PROPS", "key", "ref", "__self", "__source", "specialPropKeyWarningShown", "specialPropRefWarningShown", "didWarnAboutStringRefs", "hasValidRef", "config", "getter", "getOwnPropertyDescriptor", "isReactWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warnIfStringRefCannotBeAutoConverted", "self", "stateNode", "defineKeyPropWarningGetter", "warnAboutAccessingKey", "defineRefPropWarningGetter", "warnAboutAccessingRef", "ReactElement", "_store", "freeze", "jsxDEV", "<PERSON><PERSON><PERSON>", "propName", "defaultProps", "ReactCurrentOwner$1", "ReactDebugCurrentFrame$1", "setCurrentlyValidatingElement$1", "propTypesMisspellWarningShown", "isValidElement", "object", "getDeclarationErrorAddendum", "getSourceInfoErrorAddendum", "fileName", "lineNumber", "ownerHasKeyUseWarning", "getCurrentComponentErrorInfo", "parentType", "parentName", "validateExplicitKey", "validated", "currentComponentErrorInfo", "childOwner", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "isArray", "i", "child", "iteratorFn", "entries", "step", "next", "done", "validatePropTypes", "propTypes", "PropTypes", "_name", "getDefaultProps", "isReactClassApproved", "validateFragmentProps", "fragment", "keys", "jsxWithValidation", "isStaticChildren", "validType", "sourceInfo", "typeString", "children", "jsxDEV$1"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/** @license React v17.0.2\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar React = require('react');\nvar _assign = require('object-assign');\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar REACT_ELEMENT_TYPE = 0xeac7;\nvar REACT_PORTAL_TYPE = 0xeaca;\nexports.Fragment = 0xeacb;\nvar REACT_STRICT_MODE_TYPE = 0xeacc;\nvar REACT_PROFILER_TYPE = 0xead2;\nvar REACT_PROVIDER_TYPE = 0xeacd;\nvar REACT_CONTEXT_TYPE = 0xeace;\nvar REACT_FORWARD_REF_TYPE = 0xead0;\nvar REACT_SUSPENSE_TYPE = 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = 0xead8;\nvar REACT_MEMO_TYPE = 0xead3;\nvar REACT_LAZY_TYPE = 0xead4;\nvar REACT_BLOCK_TYPE = 0xead9;\nvar REACT_SERVER_BLOCK_TYPE = 0xeada;\nvar REACT_FUNDAMENTAL_TYPE = 0xead5;\nvar REACT_SCOPE_TYPE = 0xead7;\nvar REACT_OPAQUE_ID_TYPE = 0xeae0;\nvar REACT_DEBUG_TRACING_MODE_TYPE = 0xeae1;\nvar REACT_OFFSCREEN_TYPE = 0xeae2;\nvar REACT_LEGACY_HIDDEN_TYPE = 0xeae3;\n\nif (typeof Symbol === 'function' && Symbol.for) {\n  var symbolFor = Symbol.for;\n  REACT_ELEMENT_TYPE = symbolFor('react.element');\n  REACT_PORTAL_TYPE = symbolFor('react.portal');\n  exports.Fragment = symbolFor('react.fragment');\n  REACT_STRICT_MODE_TYPE = symbolFor('react.strict_mode');\n  REACT_PROFILER_TYPE = symbolFor('react.profiler');\n  REACT_PROVIDER_TYPE = symbolFor('react.provider');\n  REACT_CONTEXT_TYPE = symbolFor('react.context');\n  REACT_FORWARD_REF_TYPE = symbolFor('react.forward_ref');\n  REACT_SUSPENSE_TYPE = symbolFor('react.suspense');\n  REACT_SUSPENSE_LIST_TYPE = symbolFor('react.suspense_list');\n  REACT_MEMO_TYPE = symbolFor('react.memo');\n  REACT_LAZY_TYPE = symbolFor('react.lazy');\n  REACT_BLOCK_TYPE = symbolFor('react.block');\n  REACT_SERVER_BLOCK_TYPE = symbolFor('react.server.block');\n  REACT_FUNDAMENTAL_TYPE = symbolFor('react.fundamental');\n  REACT_SCOPE_TYPE = symbolFor('react.scope');\n  REACT_OPAQUE_ID_TYPE = symbolFor('react.opaque.id');\n  REACT_DEBUG_TRACING_MODE_TYPE = symbolFor('react.debug_trace_mode');\n  REACT_OFFSCREEN_TYPE = symbolFor('react.offscreen');\n  REACT_LEGACY_HIDDEN_TYPE = symbolFor('react.legacy_hidden');\n}\n\nvar MAYBE_ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    printWarning('error', format, args);\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    }\n\n    var argsWithFormat = args.map(function (item) {\n      return '' + item;\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// Filter certain DOM attributes (e.g. src, href) if their values are empty strings.\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === exports.Fragment || type === REACT_PROFILER_TYPE || type === REACT_DEBUG_TRACING_MODE_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_LEGACY_HIDDEN_TYPE || enableScopeAPI ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_BLOCK_TYPE || type[0] === REACT_SERVER_BLOCK_TYPE) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var functionName = innerType.displayName || innerType.name || '';\n  return outerType.displayName || (functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName);\n}\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nfunction getComponentName(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentName(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case exports.Fragment:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        return getComponentName(type.type);\n\n      case REACT_BLOCK_TYPE:\n        return getComponentName(type._render);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentName(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: _assign({}, props, {\n          value: prevLog\n        }),\n        info: _assign({}, props, {\n          value: prevInfo\n        }),\n        warn: _assign({}, props, {\n          value: prevWarn\n        }),\n        error: _assign({}, props, {\n          value: prevError\n        }),\n        group: _assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: _assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: _assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at ');\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_BLOCK_TYPE:\n        return describeFunctionComponentFrame(type._render);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(Object.prototype.hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentName(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentName(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentName(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentName(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (Array.isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentName(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentName(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (Array.isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentName(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (Array.isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (type === exports.Fragment) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV$1 =  jsxWithValidation ;\n\nexports.jsxDEV = jsxDEV$1;\n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,CAAC,YAAW;IACd,YAAY;;IAEZ,IAAIC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;IAC5B,IAAIC,OAAO,GAAGD,OAAO,CAAC,eAAe,CAAC;;IAEtC;IACA;IACA;IACA;IACA;IACA,IAAIE,kBAAkB,GAAG,MAAM;IAC/B,IAAIC,iBAAiB,GAAG,MAAM;IAC9BC,OAAO,CAACC,QAAQ,GAAG,MAAM;IACzB,IAAIC,sBAAsB,GAAG,MAAM;IACnC,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,kBAAkB,GAAG,MAAM;IAC/B,IAAIC,sBAAsB,GAAG,MAAM;IACnC,IAAIC,mBAAmB,GAAG,MAAM;IAChC,IAAIC,wBAAwB,GAAG,MAAM;IACrC,IAAIC,eAAe,GAAG,MAAM;IAC5B,IAAIC,eAAe,GAAG,MAAM;IAC5B,IAAIC,gBAAgB,GAAG,MAAM;IAC7B,IAAIC,uBAAuB,GAAG,MAAM;IACpC,IAAIC,sBAAsB,GAAG,MAAM;IACnC,IAAIC,gBAAgB,GAAG,MAAM;IAC7B,IAAIC,oBAAoB,GAAG,MAAM;IACjC,IAAIC,6BAA6B,GAAG,MAAM;IAC1C,IAAIC,oBAAoB,GAAG,MAAM;IACjC,IAAIC,wBAAwB,GAAG,MAAM;IAErC,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;MAC9C,IAAIC,SAAS,GAAGF,MAAM,CAACC,GAAG;MAC1BtB,kBAAkB,GAAGuB,SAAS,CAAC,eAAe,CAAC;MAC/CtB,iBAAiB,GAAGsB,SAAS,CAAC,cAAc,CAAC;MAC7CrB,OAAO,CAACC,QAAQ,GAAGoB,SAAS,CAAC,gBAAgB,CAAC;MAC9CnB,sBAAsB,GAAGmB,SAAS,CAAC,mBAAmB,CAAC;MACvDlB,mBAAmB,GAAGkB,SAAS,CAAC,gBAAgB,CAAC;MACjDjB,mBAAmB,GAAGiB,SAAS,CAAC,gBAAgB,CAAC;MACjDhB,kBAAkB,GAAGgB,SAAS,CAAC,eAAe,CAAC;MAC/Cf,sBAAsB,GAAGe,SAAS,CAAC,mBAAmB,CAAC;MACvDd,mBAAmB,GAAGc,SAAS,CAAC,gBAAgB,CAAC;MACjDb,wBAAwB,GAAGa,SAAS,CAAC,qBAAqB,CAAC;MAC3DZ,eAAe,GAAGY,SAAS,CAAC,YAAY,CAAC;MACzCX,eAAe,GAAGW,SAAS,CAAC,YAAY,CAAC;MACzCV,gBAAgB,GAAGU,SAAS,CAAC,aAAa,CAAC;MAC3CT,uBAAuB,GAAGS,SAAS,CAAC,oBAAoB,CAAC;MACzDR,sBAAsB,GAAGQ,SAAS,CAAC,mBAAmB,CAAC;MACvDP,gBAAgB,GAAGO,SAAS,CAAC,aAAa,CAAC;MAC3CN,oBAAoB,GAAGM,SAAS,CAAC,iBAAiB,CAAC;MACnDL,6BAA6B,GAAGK,SAAS,CAAC,wBAAwB,CAAC;MACnEJ,oBAAoB,GAAGI,SAAS,CAAC,iBAAiB,CAAC;MACnDH,wBAAwB,GAAGG,SAAS,CAAC,qBAAqB,CAAC;IAC7D;IAEA,IAAIC,qBAAqB,GAAG,OAAOH,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACI,QAAQ;IAC3E,IAAIC,oBAAoB,GAAG,YAAY;IACvC,SAASC,aAAaA,CAACC,aAAa,EAAE;MACpC,IAAIA,aAAa,KAAK,IAAI,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;QAC/D,OAAO,IAAI;MACb;MAEA,IAAIC,aAAa,GAAGL,qBAAqB,IAAII,aAAa,CAACJ,qBAAqB,CAAC,IAAII,aAAa,CAACF,oBAAoB,CAAC;MAExH,IAAI,OAAOG,aAAa,KAAK,UAAU,EAAE;QACvC,OAAOA,aAAa;MACtB;MAEA,OAAO,IAAI;IACb;IAEA,IAAIC,oBAAoB,GAAGjC,KAAK,CAACkC,kDAAkD;IAEnF,SAASC,KAAKA,CAACC,MAAM,EAAE;MACrB;QACE,KAAK,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGL,KAAK,EAAEK,KAAK,EAAE,EAAE;UACjHF,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,KAAK,CAAC;QACpC;QAEAC,YAAY,CAAC,OAAO,EAAEP,MAAM,EAAEI,IAAI,CAAC;MACrC;IACF;IAEA,SAASG,YAAYA,CAACC,KAAK,EAAER,MAAM,EAAEI,IAAI,EAAE;MACzC;MACA;MACA;QACE,IAAIK,sBAAsB,GAAGZ,oBAAoB,CAACY,sBAAsB;QACxE,IAAIC,KAAK,GAAGD,sBAAsB,CAACE,gBAAgB,CAAC,CAAC;QAErD,IAAID,KAAK,KAAK,EAAE,EAAE;UAChBV,MAAM,IAAI,IAAI;UACdI,IAAI,GAAGA,IAAI,CAACQ,MAAM,CAAC,CAACF,KAAK,CAAC,CAAC;QAC7B;QAEA,IAAIG,cAAc,GAAGT,IAAI,CAACU,GAAG,CAAC,UAAUC,IAAI,EAAE;UAC5C,OAAO,EAAE,GAAGA,IAAI;QAClB,CAAC,CAAC,CAAC,CAAC;;QAEJF,cAAc,CAACG,OAAO,CAAC,WAAW,GAAGhB,MAAM,CAAC,CAAC,CAAC;QAC9C;QACA;;QAEAiB,QAAQ,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACC,OAAO,CAACb,KAAK,CAAC,EAAEa,OAAO,EAAER,cAAc,CAAC;MACxE;IACF;;IAEA;;IAEA,IAAIS,cAAc,GAAG,KAAK,CAAC,CAAC;;IAE5B,SAASC,kBAAkBA,CAACC,IAAI,EAAE;MAChC,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;QAC1D,OAAO,IAAI;MACb,CAAC,CAAC;;MAGF,IAAIA,IAAI,KAAKvD,OAAO,CAACC,QAAQ,IAAIsD,IAAI,KAAKpD,mBAAmB,IAAIoD,IAAI,KAAKvC,6BAA6B,IAAIuC,IAAI,KAAKrD,sBAAsB,IAAIqD,IAAI,KAAKhD,mBAAmB,IAAIgD,IAAI,KAAK/C,wBAAwB,IAAI+C,IAAI,KAAKrC,wBAAwB,IAAImC,cAAc,EAAG;QACvQ,OAAO,IAAI;MACb;MAEA,IAAI,OAAOE,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;QAC7C,IAAIA,IAAI,CAACC,QAAQ,KAAK9C,eAAe,IAAI6C,IAAI,CAACC,QAAQ,KAAK/C,eAAe,IAAI8C,IAAI,CAACC,QAAQ,KAAKpD,mBAAmB,IAAImD,IAAI,CAACC,QAAQ,KAAKnD,kBAAkB,IAAIkD,IAAI,CAACC,QAAQ,KAAKlD,sBAAsB,IAAIiD,IAAI,CAACC,QAAQ,KAAK3C,sBAAsB,IAAI0C,IAAI,CAACC,QAAQ,KAAK7C,gBAAgB,IAAI4C,IAAI,CAAC,CAAC,CAAC,KAAK3C,uBAAuB,EAAE;UAChU,OAAO,IAAI;QACb;MACF;MAEA,OAAO,KAAK;IACd;IAEA,SAAS6C,cAAcA,CAACC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAE;MACzD,IAAIC,YAAY,GAAGF,SAAS,CAACG,WAAW,IAAIH,SAAS,CAACI,IAAI,IAAI,EAAE;MAChE,OAAOL,SAAS,CAACI,WAAW,KAAKD,YAAY,KAAK,EAAE,GAAGD,WAAW,GAAG,GAAG,GAAGC,YAAY,GAAG,GAAG,GAAGD,WAAW,CAAC;IAC9G;IAEA,SAASI,cAAcA,CAACT,IAAI,EAAE;MAC5B,OAAOA,IAAI,CAACO,WAAW,IAAI,SAAS;IACtC;IAEA,SAASG,gBAAgBA,CAACV,IAAI,EAAE;MAC9B,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChB;QACA,OAAO,IAAI;MACb;MAEA;QACE,IAAI,OAAOA,IAAI,CAACW,GAAG,KAAK,QAAQ,EAAE;UAChCpC,KAAK,CAAC,uDAAuD,GAAG,sDAAsD,CAAC;QACzH;MACF;MAEA,IAAI,OAAOyB,IAAI,KAAK,UAAU,EAAE;QAC9B,OAAOA,IAAI,CAACO,WAAW,IAAIP,IAAI,CAACQ,IAAI,IAAI,IAAI;MAC9C;MAEA,IAAI,OAAOR,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOA,IAAI;MACb;MAEA,QAAQA,IAAI;QACV,KAAKvD,OAAO,CAACC,QAAQ;UACnB,OAAO,UAAU;QAEnB,KAAKF,iBAAiB;UACpB,OAAO,QAAQ;QAEjB,KAAKI,mBAAmB;UACtB,OAAO,UAAU;QAEnB,KAAKD,sBAAsB;UACzB,OAAO,YAAY;QAErB,KAAKK,mBAAmB;UACtB,OAAO,UAAU;QAEnB,KAAKC,wBAAwB;UAC3B,OAAO,cAAc;MACzB;MAEA,IAAI,OAAO+C,IAAI,KAAK,QAAQ,EAAE;QAC5B,QAAQA,IAAI,CAACC,QAAQ;UACnB,KAAKnD,kBAAkB;YACrB,IAAI8D,OAAO,GAAGZ,IAAI;YAClB,OAAOS,cAAc,CAACG,OAAO,CAAC,GAAG,WAAW;UAE9C,KAAK/D,mBAAmB;YACtB,IAAIgE,QAAQ,GAAGb,IAAI;YACnB,OAAOS,cAAc,CAACI,QAAQ,CAACC,QAAQ,CAAC,GAAG,WAAW;UAExD,KAAK/D,sBAAsB;YACzB,OAAOmD,cAAc,CAACF,IAAI,EAAEA,IAAI,CAACe,MAAM,EAAE,YAAY,CAAC;UAExD,KAAK7D,eAAe;YAClB,OAAOwD,gBAAgB,CAACV,IAAI,CAACA,IAAI,CAAC;UAEpC,KAAK5C,gBAAgB;YACnB,OAAOsD,gBAAgB,CAACV,IAAI,CAACgB,OAAO,CAAC;UAEvC,KAAK7D,eAAe;YAClB;cACE,IAAI8D,aAAa,GAAGjB,IAAI;cACxB,IAAIkB,OAAO,GAAGD,aAAa,CAACE,QAAQ;cACpC,IAAIC,IAAI,GAAGH,aAAa,CAACI,KAAK;cAE9B,IAAI;gBACF,OAAOX,gBAAgB,CAACU,IAAI,CAACF,OAAO,CAAC,CAAC;cACxC,CAAC,CAAC,OAAOI,CAAC,EAAE;gBACV,OAAO,IAAI;cACb;YACF;QACJ;MACF;MAEA,OAAO,IAAI;IACb;;IAEA;IACA;IACA;IACA;IACA,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,OAAO;IACX,IAAIC,QAAQ;IACZ,IAAIC,QAAQ;IACZ,IAAIC,SAAS;IACb,IAAIC,SAAS;IACb,IAAIC,kBAAkB;IACtB,IAAIC,YAAY;IAEhB,SAASC,WAAWA,CAAA,EAAG,CAAC;IAExBA,WAAW,CAACC,kBAAkB,GAAG,IAAI;IACrC,SAASC,WAAWA,CAAA,EAAG;MACrB;QACE,IAAIV,aAAa,KAAK,CAAC,EAAE;UACvB;UACAC,OAAO,GAAG3B,OAAO,CAACqC,GAAG;UACrBT,QAAQ,GAAG5B,OAAO,CAACsC,IAAI;UACvBT,QAAQ,GAAG7B,OAAO,CAACuC,IAAI;UACvBT,SAAS,GAAG9B,OAAO,CAACtB,KAAK;UACzBqD,SAAS,GAAG/B,OAAO,CAACwC,KAAK;UACzBR,kBAAkB,GAAGhC,OAAO,CAACyC,cAAc;UAC3CR,YAAY,GAAGjC,OAAO,CAAC0C,QAAQ,CAAC,CAAC;;UAEjC,IAAIC,KAAK,GAAG;YACVC,YAAY,EAAE,IAAI;YAClBC,UAAU,EAAE,IAAI;YAChBC,KAAK,EAAEZ,WAAW;YAClBa,QAAQ,EAAE;UACZ,CAAC,CAAC,CAAC;;UAEHC,MAAM,CAACC,gBAAgB,CAACjD,OAAO,EAAE;YAC/BsC,IAAI,EAAEK,KAAK;YACXN,GAAG,EAAEM,KAAK;YACVJ,IAAI,EAAEI,KAAK;YACXjE,KAAK,EAAEiE,KAAK;YACZH,KAAK,EAAEG,KAAK;YACZF,cAAc,EAAEE,KAAK;YACrBD,QAAQ,EAAEC;UACZ,CAAC,CAAC;UACF;QACF;QAEAjB,aAAa,EAAE;MACjB;IACF;IACA,SAASwB,YAAYA,CAAA,EAAG;MACtB;QACExB,aAAa,EAAE;QAEf,IAAIA,aAAa,KAAK,CAAC,EAAE;UACvB;UACA,IAAIiB,KAAK,GAAG;YACVC,YAAY,EAAE,IAAI;YAClBC,UAAU,EAAE,IAAI;YAChBE,QAAQ,EAAE;UACZ,CAAC,CAAC,CAAC;;UAEHC,MAAM,CAACC,gBAAgB,CAACjD,OAAO,EAAE;YAC/BqC,GAAG,EAAE5F,OAAO,CAAC,CAAC,CAAC,EAAEkG,KAAK,EAAE;cACtBG,KAAK,EAAEnB;YACT,CAAC,CAAC;YACFW,IAAI,EAAE7F,OAAO,CAAC,CAAC,CAAC,EAAEkG,KAAK,EAAE;cACvBG,KAAK,EAAElB;YACT,CAAC,CAAC;YACFW,IAAI,EAAE9F,OAAO,CAAC,CAAC,CAAC,EAAEkG,KAAK,EAAE;cACvBG,KAAK,EAAEjB;YACT,CAAC,CAAC;YACFnD,KAAK,EAAEjC,OAAO,CAAC,CAAC,CAAC,EAAEkG,KAAK,EAAE;cACxBG,KAAK,EAAEhB;YACT,CAAC,CAAC;YACFU,KAAK,EAAE/F,OAAO,CAAC,CAAC,CAAC,EAAEkG,KAAK,EAAE;cACxBG,KAAK,EAAEf;YACT,CAAC,CAAC;YACFU,cAAc,EAAEhG,OAAO,CAAC,CAAC,CAAC,EAAEkG,KAAK,EAAE;cACjCG,KAAK,EAAEd;YACT,CAAC,CAAC;YACFU,QAAQ,EAAEjG,OAAO,CAAC,CAAC,CAAC,EAAEkG,KAAK,EAAE;cAC3BG,KAAK,EAAEb;YACT,CAAC;UACH,CAAC,CAAC;UACF;QACF;QAEA,IAAIP,aAAa,GAAG,CAAC,EAAE;UACrBhD,KAAK,CAAC,iCAAiC,GAAG,+CAA+C,CAAC;QAC5F;MACF;IACF;IAEA,IAAIyE,sBAAsB,GAAG3E,oBAAoB,CAAC2E,sBAAsB;IACxE,IAAIC,MAAM;IACV,SAASC,6BAA6BA,CAAC1C,IAAI,EAAE2C,MAAM,EAAEC,OAAO,EAAE;MAC5D;QACE,IAAIH,MAAM,KAAKI,SAAS,EAAE;UACxB;UACA,IAAI;YACF,MAAMC,KAAK,CAAC,CAAC;UACf,CAAC,CAAC,OAAOhC,CAAC,EAAE;YACV,IAAIiC,KAAK,GAAGjC,CAAC,CAACpC,KAAK,CAACsE,IAAI,CAAC,CAAC,CAACD,KAAK,CAAC,cAAc,CAAC;YAChDN,MAAM,GAAGM,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;UAClC;QACF,CAAC,CAAC;;QAGF,OAAO,IAAI,GAAGN,MAAM,GAAGzC,IAAI;MAC7B;IACF;IACA,IAAIiD,OAAO,GAAG,KAAK;IACnB,IAAIC,mBAAmB;IAEvB;MACE,IAAIC,eAAe,GAAG,OAAOC,OAAO,KAAK,UAAU,GAAGA,OAAO,GAAGC,GAAG;MACnEH,mBAAmB,GAAG,IAAIC,eAAe,CAAC,CAAC;IAC7C;IAEA,SAASG,4BAA4BA,CAACC,EAAE,EAAEC,SAAS,EAAE;MACnD;MACA,IAAI,CAACD,EAAE,IAAIN,OAAO,EAAE;QAClB,OAAO,EAAE;MACX;MAEA;QACE,IAAIQ,KAAK,GAAGP,mBAAmB,CAACQ,GAAG,CAACH,EAAE,CAAC;QAEvC,IAAIE,KAAK,KAAKZ,SAAS,EAAE;UACvB,OAAOY,KAAK;QACd;MACF;MAEA,IAAIE,OAAO;MACXV,OAAO,GAAG,IAAI;MACd,IAAIW,yBAAyB,GAAGd,KAAK,CAACe,iBAAiB,CAAC,CAAC;;MAEzDf,KAAK,CAACe,iBAAiB,GAAGhB,SAAS;MACnC,IAAIiB,kBAAkB;MAEtB;QACEA,kBAAkB,GAAGtB,sBAAsB,CAACuB,OAAO,CAAC,CAAC;QACrD;;QAEAvB,sBAAsB,CAACuB,OAAO,GAAG,IAAI;QACrCtC,WAAW,CAAC,CAAC;MACf;MAEA,IAAI;QACF;QACA,IAAI+B,SAAS,EAAE;UACb;UACA,IAAIQ,IAAI,GAAG,SAAAA,CAAA,EAAY;YACrB,MAAMlB,KAAK,CAAC,CAAC;UACf,CAAC,CAAC,CAAC;;UAGHT,MAAM,CAAC4B,cAAc,CAACD,IAAI,CAAC9E,SAAS,EAAE,OAAO,EAAE;YAC7CgF,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf;cACA;cACA,MAAMpB,KAAK,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UAEF,IAAI,OAAOqB,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACX,SAAS,EAAE;YACpD;YACA;YACA,IAAI;cACFW,OAAO,CAACX,SAAS,CAACQ,IAAI,EAAE,EAAE,CAAC;YAC7B,CAAC,CAAC,OAAOlD,CAAC,EAAE;cACV6C,OAAO,GAAG7C,CAAC;YACb;YAEAqD,OAAO,CAACX,SAAS,CAACD,EAAE,EAAE,EAAE,EAAES,IAAI,CAAC;UACjC,CAAC,MAAM;YACL,IAAI;cACFA,IAAI,CAAC5E,IAAI,CAAC,CAAC;YACb,CAAC,CAAC,OAAO0B,CAAC,EAAE;cACV6C,OAAO,GAAG7C,CAAC;YACb;YAEAyC,EAAE,CAACnE,IAAI,CAAC4E,IAAI,CAAC9E,SAAS,CAAC;UACzB;QACF,CAAC,MAAM;UACL,IAAI;YACF,MAAM4D,KAAK,CAAC,CAAC;UACf,CAAC,CAAC,OAAOhC,CAAC,EAAE;YACV6C,OAAO,GAAG7C,CAAC;UACb;UAEAyC,EAAE,CAAC,CAAC;QACN;MACF,CAAC,CAAC,OAAOa,MAAM,EAAE;QACf;QACA,IAAIA,MAAM,IAAIT,OAAO,IAAI,OAAOS,MAAM,CAAC1F,KAAK,KAAK,QAAQ,EAAE;UACzD;UACA;UACA,IAAI2F,WAAW,GAAGD,MAAM,CAAC1F,KAAK,CAAC4F,KAAK,CAAC,IAAI,CAAC;UAC1C,IAAIC,YAAY,GAAGZ,OAAO,CAACjF,KAAK,CAAC4F,KAAK,CAAC,IAAI,CAAC;UAC5C,IAAIE,CAAC,GAAGH,WAAW,CAAClG,MAAM,GAAG,CAAC;UAC9B,IAAIsG,CAAC,GAAGF,YAAY,CAACpG,MAAM,GAAG,CAAC;UAE/B,OAAOqG,CAAC,IAAI,CAAC,IAAIC,CAAC,IAAI,CAAC,IAAIJ,WAAW,CAACG,CAAC,CAAC,KAAKD,YAAY,CAACE,CAAC,CAAC,EAAE;YAC7D;YACA;YACA;YACA;YACA;YACA;YACAA,CAAC,EAAE;UACL;UAEA,OAAOD,CAAC,IAAI,CAAC,IAAIC,CAAC,IAAI,CAAC,EAAED,CAAC,EAAE,EAAEC,CAAC,EAAE,EAAE;YACjC;YACA;YACA,IAAIJ,WAAW,CAACG,CAAC,CAAC,KAAKD,YAAY,CAACE,CAAC,CAAC,EAAE;cACtC;cACA;cACA;cACA;cACA;cACA,IAAID,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,CAAC,EAAE;gBACtB,GAAG;kBACDD,CAAC,EAAE;kBACHC,CAAC,EAAE,CAAC,CAAC;kBACL;;kBAEA,IAAIA,CAAC,GAAG,CAAC,IAAIJ,WAAW,CAACG,CAAC,CAAC,KAAKD,YAAY,CAACE,CAAC,CAAC,EAAE;oBAC/C;oBACA,IAAIC,MAAM,GAAG,IAAI,GAAGL,WAAW,CAACG,CAAC,CAAC,CAACG,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;oBAE9D;sBACE,IAAI,OAAOpB,EAAE,KAAK,UAAU,EAAE;wBAC5BL,mBAAmB,CAACgB,GAAG,CAACX,EAAE,EAAEmB,MAAM,CAAC;sBACrC;oBACF,CAAC,CAAC;;oBAGF,OAAOA,MAAM;kBACf;gBACF,CAAC,QAAQF,CAAC,IAAI,CAAC,IAAIC,CAAC,IAAI,CAAC;cAC3B;cAEA;YACF;UACF;QACF;MACF,CAAC,SAAS;QACRxB,OAAO,GAAG,KAAK;QAEf;UACET,sBAAsB,CAACuB,OAAO,GAAGD,kBAAkB;UACnDvB,YAAY,CAAC,CAAC;QAChB;QAEAO,KAAK,CAACe,iBAAiB,GAAGD,yBAAyB;MACrD,CAAC,CAAC;;MAGF,IAAI5D,IAAI,GAAGuD,EAAE,GAAGA,EAAE,CAACxD,WAAW,IAAIwD,EAAE,CAACvD,IAAI,GAAG,EAAE;MAC9C,IAAI4E,cAAc,GAAG5E,IAAI,GAAG0C,6BAA6B,CAAC1C,IAAI,CAAC,GAAG,EAAE;MAEpE;QACE,IAAI,OAAOuD,EAAE,KAAK,UAAU,EAAE;UAC5BL,mBAAmB,CAACgB,GAAG,CAACX,EAAE,EAAEqB,cAAc,CAAC;QAC7C;MACF;MAEA,OAAOA,cAAc;IACvB;IACA,SAASC,8BAA8BA,CAACtB,EAAE,EAAEZ,MAAM,EAAEC,OAAO,EAAE;MAC3D;QACE,OAAOU,4BAA4B,CAACC,EAAE,EAAE,KAAK,CAAC;MAChD;IACF;IAEA,SAASuB,eAAeA,CAACC,SAAS,EAAE;MAClC,IAAI7F,SAAS,GAAG6F,SAAS,CAAC7F,SAAS;MACnC,OAAO,CAAC,EAAEA,SAAS,IAAIA,SAAS,CAAC8F,gBAAgB,CAAC;IACpD;IAEA,SAASC,oCAAoCA,CAACzF,IAAI,EAAEmD,MAAM,EAAEC,OAAO,EAAE;MAEnE,IAAIpD,IAAI,IAAI,IAAI,EAAE;QAChB,OAAO,EAAE;MACX;MAEA,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;QAC9B;UACE,OAAO8D,4BAA4B,CAAC9D,IAAI,EAAEsF,eAAe,CAACtF,IAAI,CAAC,CAAC;QAClE;MACF;MAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOkD,6BAA6B,CAAClD,IAAI,CAAC;MAC5C;MAEA,QAAQA,IAAI;QACV,KAAKhD,mBAAmB;UACtB,OAAOkG,6BAA6B,CAAC,UAAU,CAAC;QAElD,KAAKjG,wBAAwB;UAC3B,OAAOiG,6BAA6B,CAAC,cAAc,CAAC;MACxD;MAEA,IAAI,OAAOlD,IAAI,KAAK,QAAQ,EAAE;QAC5B,QAAQA,IAAI,CAACC,QAAQ;UACnB,KAAKlD,sBAAsB;YACzB,OAAOsI,8BAA8B,CAACrF,IAAI,CAACe,MAAM,CAAC;UAEpD,KAAK7D,eAAe;YAClB;YACA,OAAOuI,oCAAoC,CAACzF,IAAI,CAACA,IAAI,EAAEmD,MAAM,EAAEC,OAAO,CAAC;UAEzE,KAAKhG,gBAAgB;YACnB,OAAOiI,8BAA8B,CAACrF,IAAI,CAACgB,OAAO,CAAC;UAErD,KAAK7D,eAAe;YAClB;cACE,IAAI8D,aAAa,GAAGjB,IAAI;cACxB,IAAIkB,OAAO,GAAGD,aAAa,CAACE,QAAQ;cACpC,IAAIC,IAAI,GAAGH,aAAa,CAACI,KAAK;cAE9B,IAAI;gBACF;gBACA,OAAOoE,oCAAoC,CAACrE,IAAI,CAACF,OAAO,CAAC,EAAEiC,MAAM,EAAEC,OAAO,CAAC;cAC7E,CAAC,CAAC,OAAO9B,CAAC,EAAE,CAAC;YACf;QACJ;MACF;MAEA,OAAO,EAAE;IACX;IAEA,IAAIoE,kBAAkB,GAAG,CAAC,CAAC;IAC3B,IAAIzG,sBAAsB,GAAGZ,oBAAoB,CAACY,sBAAsB;IAExE,SAAS0G,6BAA6BA,CAACC,OAAO,EAAE;MAC9C;QACE,IAAIA,OAAO,EAAE;UACX,IAAIC,KAAK,GAAGD,OAAO,CAACE,MAAM;UAC1B,IAAI5G,KAAK,GAAGuG,oCAAoC,CAACG,OAAO,CAAC5F,IAAI,EAAE4F,OAAO,CAACG,OAAO,EAAEF,KAAK,GAAGA,KAAK,CAAC7F,IAAI,GAAG,IAAI,CAAC;UAC1Gf,sBAAsB,CAAC+G,kBAAkB,CAAC9G,KAAK,CAAC;QAClD,CAAC,MAAM;UACLD,sBAAsB,CAAC+G,kBAAkB,CAAC,IAAI,CAAC;QACjD;MACF;IACF;IAEA,SAASC,cAAcA,CAACC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAET,OAAO,EAAE;MAC3E;QACE;QACA,IAAIU,GAAG,GAAG7G,QAAQ,CAACG,IAAI,CAAC2G,IAAI,CAAC1D,MAAM,CAACnD,SAAS,CAAC8G,cAAc,CAAC;QAE7D,KAAK,IAAIC,YAAY,IAAIP,SAAS,EAAE;UAClC,IAAII,GAAG,CAACJ,SAAS,EAAEO,YAAY,CAAC,EAAE;YAChC,IAAIC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;YACtB;YACA;;YAEA,IAAI;cACF;cACA;cACA,IAAI,OAAOR,SAAS,CAACO,YAAY,CAAC,KAAK,UAAU,EAAE;gBACjD,IAAIE,GAAG,GAAGrD,KAAK,CAAC,CAAC+C,aAAa,IAAI,aAAa,IAAI,IAAI,GAAGD,QAAQ,GAAG,SAAS,GAAGK,YAAY,GAAG,gBAAgB,GAAG,8EAA8E,GAAG,OAAOP,SAAS,CAACO,YAAY,CAAC,GAAG,IAAI,GAAG,+FAA+F,CAAC;gBAC5UE,GAAG,CAACnG,IAAI,GAAG,qBAAqB;gBAChC,MAAMmG,GAAG;cACX;cAEAD,OAAO,GAAGR,SAAS,CAACO,YAAY,CAAC,CAACN,MAAM,EAAEM,YAAY,EAAEJ,aAAa,EAAED,QAAQ,EAAE,IAAI,EAAE,8CAA8C,CAAC;YACxI,CAAC,CAAC,OAAOQ,EAAE,EAAE;cACXF,OAAO,GAAGE,EAAE;YACd;YAEA,IAAIF,OAAO,IAAI,EAAEA,OAAO,YAAYpD,KAAK,CAAC,EAAE;cAC1CqC,6BAA6B,CAACC,OAAO,CAAC;cAEtCrH,KAAK,CAAC,8BAA8B,GAAG,qCAAqC,GAAG,+DAA+D,GAAG,iEAAiE,GAAG,gEAAgE,GAAG,iCAAiC,EAAE8H,aAAa,IAAI,aAAa,EAAED,QAAQ,EAAEK,YAAY,EAAE,OAAOC,OAAO,CAAC;cAElYf,6BAA6B,CAAC,IAAI,CAAC;YACrC;YAEA,IAAIe,OAAO,YAAYpD,KAAK,IAAI,EAAEoD,OAAO,CAACG,OAAO,IAAInB,kBAAkB,CAAC,EAAE;cACxE;cACA;cACAA,kBAAkB,CAACgB,OAAO,CAACG,OAAO,CAAC,GAAG,IAAI;cAC1ClB,6BAA6B,CAACC,OAAO,CAAC;cAEtCrH,KAAK,CAAC,oBAAoB,EAAE6H,QAAQ,EAAEM,OAAO,CAACG,OAAO,CAAC;cAEtDlB,6BAA6B,CAAC,IAAI,CAAC;YACrC;UACF;QACF;MACF;IACF;IAEA,IAAImB,iBAAiB,GAAGzI,oBAAoB,CAACyI,iBAAiB;IAC9D,IAAIN,cAAc,GAAG3D,MAAM,CAACnD,SAAS,CAAC8G,cAAc;IACpD,IAAIO,cAAc,GAAG;MACnBC,GAAG,EAAE,IAAI;MACTC,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIC,0BAA0B;IAC9B,IAAIC,0BAA0B;IAC9B,IAAIC,sBAAsB;IAE1B;MACEA,sBAAsB,GAAG,CAAC,CAAC;IAC7B;IAEA,SAASC,WAAWA,CAACC,MAAM,EAAE;MAC3B;QACE,IAAIhB,cAAc,CAAC5G,IAAI,CAAC4H,MAAM,EAAE,KAAK,CAAC,EAAE;UACtC,IAAIC,MAAM,GAAG5E,MAAM,CAAC6E,wBAAwB,CAACF,MAAM,EAAE,KAAK,CAAC,CAACtD,GAAG;UAE/D,IAAIuD,MAAM,IAAIA,MAAM,CAACE,cAAc,EAAE;YACnC,OAAO,KAAK;UACd;QACF;MACF;MAEA,OAAOH,MAAM,CAACP,GAAG,KAAK5D,SAAS;IACjC;IAEA,SAASuE,WAAWA,CAACJ,MAAM,EAAE;MAC3B;QACE,IAAIhB,cAAc,CAAC5G,IAAI,CAAC4H,MAAM,EAAE,KAAK,CAAC,EAAE;UACtC,IAAIC,MAAM,GAAG5E,MAAM,CAAC6E,wBAAwB,CAACF,MAAM,EAAE,KAAK,CAAC,CAACtD,GAAG;UAE/D,IAAIuD,MAAM,IAAIA,MAAM,CAACE,cAAc,EAAE;YACnC,OAAO,KAAK;UACd;QACF;MACF;MAEA,OAAOH,MAAM,CAACR,GAAG,KAAK3D,SAAS;IACjC;IAEA,SAASwE,oCAAoCA,CAACL,MAAM,EAAEM,IAAI,EAAE;MAC1D;QACE,IAAI,OAAON,MAAM,CAACP,GAAG,KAAK,QAAQ,IAAIH,iBAAiB,CAACvC,OAAO,IAAIuD,IAAI,IAAIhB,iBAAiB,CAACvC,OAAO,CAACwD,SAAS,KAAKD,IAAI,EAAE;UACvH,IAAIzB,aAAa,GAAG3F,gBAAgB,CAACoG,iBAAiB,CAACvC,OAAO,CAACvE,IAAI,CAAC;UAEpE,IAAI,CAACsH,sBAAsB,CAACjB,aAAa,CAAC,EAAE;YAC1C9H,KAAK,CAAC,+CAA+C,GAAG,qEAAqE,GAAG,oEAAoE,GAAG,iFAAiF,GAAG,2CAA2C,GAAG,iDAAiD,EAAEmC,gBAAgB,CAACoG,iBAAiB,CAACvC,OAAO,CAACvE,IAAI,CAAC,EAAEwH,MAAM,CAACP,GAAG,CAAC;YAEzbK,sBAAsB,CAACjB,aAAa,CAAC,GAAG,IAAI;UAC9C;QACF;MACF;IACF;IAEA,SAAS2B,0BAA0BA,CAACxF,KAAK,EAAEjC,WAAW,EAAE;MACtD;QACE,IAAI0H,qBAAqB,GAAG,SAAAA,CAAA,EAAY;UACtC,IAAI,CAACb,0BAA0B,EAAE;YAC/BA,0BAA0B,GAAG,IAAI;YAEjC7I,KAAK,CAAC,2DAA2D,GAAG,gEAAgE,GAAG,sEAAsE,GAAG,gDAAgD,EAAEgC,WAAW,CAAC;UAChR;QACF,CAAC;QAED0H,qBAAqB,CAACN,cAAc,GAAG,IAAI;QAC3C9E,MAAM,CAAC4B,cAAc,CAACjC,KAAK,EAAE,KAAK,EAAE;UAClC0B,GAAG,EAAE+D,qBAAqB;UAC1BxF,YAAY,EAAE;QAChB,CAAC,CAAC;MACJ;IACF;IAEA,SAASyF,0BAA0BA,CAAC1F,KAAK,EAAEjC,WAAW,EAAE;MACtD;QACE,IAAI4H,qBAAqB,GAAG,SAAAA,CAAA,EAAY;UACtC,IAAI,CAACd,0BAA0B,EAAE;YAC/BA,0BAA0B,GAAG,IAAI;YAEjC9I,KAAK,CAAC,2DAA2D,GAAG,gEAAgE,GAAG,sEAAsE,GAAG,gDAAgD,EAAEgC,WAAW,CAAC;UAChR;QACF,CAAC;QAED4H,qBAAqB,CAACR,cAAc,GAAG,IAAI;QAC3C9E,MAAM,CAAC4B,cAAc,CAACjC,KAAK,EAAE,KAAK,EAAE;UAClC0B,GAAG,EAAEiE,qBAAqB;UAC1B1F,YAAY,EAAE;QAChB,CAAC,CAAC;MACJ;IACF;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,IAAI2F,YAAY,GAAG,SAAAA,CAAUpI,IAAI,EAAEgH,GAAG,EAAEC,GAAG,EAAEa,IAAI,EAAE3E,MAAM,EAAE0C,KAAK,EAAErD,KAAK,EAAE;MACvE,IAAIoD,OAAO,GAAG;QACZ;QACA3F,QAAQ,EAAE1D,kBAAkB;QAC5B;QACAyD,IAAI,EAAEA,IAAI;QACVgH,GAAG,EAAEA,GAAG;QACRC,GAAG,EAAEA,GAAG;QACRzE,KAAK,EAAEA,KAAK;QACZ;QACAsD,MAAM,EAAED;MACV,CAAC;MAED;QACE;QACA;QACA;QACA;QACAD,OAAO,CAACyC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB;QACA;QACA;;QAEAxF,MAAM,CAAC4B,cAAc,CAACmB,OAAO,CAACyC,MAAM,EAAE,WAAW,EAAE;UACjD5F,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,KAAK;UACjBE,QAAQ,EAAE,IAAI;UACdD,KAAK,EAAE;QACT,CAAC,CAAC,CAAC,CAAC;;QAEJE,MAAM,CAAC4B,cAAc,CAACmB,OAAO,EAAE,OAAO,EAAE;UACtCnD,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,KAAK;UACjBE,QAAQ,EAAE,KAAK;UACfD,KAAK,EAAEmF;QACT,CAAC,CAAC,CAAC,CAAC;QACJ;;QAEAjF,MAAM,CAAC4B,cAAc,CAACmB,OAAO,EAAE,SAAS,EAAE;UACxCnD,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,KAAK;UACjBE,QAAQ,EAAE,KAAK;UACfD,KAAK,EAAEQ;QACT,CAAC,CAAC;QAEF,IAAIN,MAAM,CAACyF,MAAM,EAAE;UACjBzF,MAAM,CAACyF,MAAM,CAAC1C,OAAO,CAACpD,KAAK,CAAC;UAC5BK,MAAM,CAACyF,MAAM,CAAC1C,OAAO,CAAC;QACxB;MACF;MAEA,OAAOA,OAAO;IAChB,CAAC;IACD;AACA;AACA;AACA;AACA;AACA;;IAEA,SAAS2C,MAAMA,CAACvI,IAAI,EAAEwH,MAAM,EAAEgB,QAAQ,EAAErF,MAAM,EAAE2E,IAAI,EAAE;MACpD;QACE,IAAIW,QAAQ,CAAC,CAAC;;QAEd,IAAIjG,KAAK,GAAG,CAAC,CAAC;QACd,IAAIwE,GAAG,GAAG,IAAI;QACd,IAAIC,GAAG,GAAG,IAAI,CAAC,CAAC;QAChB;QACA;QACA;QACA;QACA;;QAEA,IAAIuB,QAAQ,KAAKnF,SAAS,EAAE;UAC1B2D,GAAG,GAAG,EAAE,GAAGwB,QAAQ;QACrB;QAEA,IAAIZ,WAAW,CAACJ,MAAM,CAAC,EAAE;UACvBR,GAAG,GAAG,EAAE,GAAGQ,MAAM,CAACR,GAAG;QACvB;QAEA,IAAIO,WAAW,CAACC,MAAM,CAAC,EAAE;UACvBP,GAAG,GAAGO,MAAM,CAACP,GAAG;UAChBY,oCAAoC,CAACL,MAAM,EAAEM,IAAI,CAAC;QACpD,CAAC,CAAC;;QAGF,KAAKW,QAAQ,IAAIjB,MAAM,EAAE;UACvB,IAAIhB,cAAc,CAAC5G,IAAI,CAAC4H,MAAM,EAAEiB,QAAQ,CAAC,IAAI,CAAC1B,cAAc,CAACP,cAAc,CAACiC,QAAQ,CAAC,EAAE;YACrFjG,KAAK,CAACiG,QAAQ,CAAC,GAAGjB,MAAM,CAACiB,QAAQ,CAAC;UACpC;QACF,CAAC,CAAC;;QAGF,IAAIzI,IAAI,IAAIA,IAAI,CAAC0I,YAAY,EAAE;UAC7B,IAAIA,YAAY,GAAG1I,IAAI,CAAC0I,YAAY;UAEpC,KAAKD,QAAQ,IAAIC,YAAY,EAAE;YAC7B,IAAIlG,KAAK,CAACiG,QAAQ,CAAC,KAAKpF,SAAS,EAAE;cACjCb,KAAK,CAACiG,QAAQ,CAAC,GAAGC,YAAY,CAACD,QAAQ,CAAC;YAC1C;UACF;QACF;QAEA,IAAIzB,GAAG,IAAIC,GAAG,EAAE;UACd,IAAI1G,WAAW,GAAG,OAAOP,IAAI,KAAK,UAAU,GAAGA,IAAI,CAACO,WAAW,IAAIP,IAAI,CAACQ,IAAI,IAAI,SAAS,GAAGR,IAAI;UAEhG,IAAIgH,GAAG,EAAE;YACPgB,0BAA0B,CAACxF,KAAK,EAAEjC,WAAW,CAAC;UAChD;UAEA,IAAI0G,GAAG,EAAE;YACPiB,0BAA0B,CAAC1F,KAAK,EAAEjC,WAAW,CAAC;UAChD;QACF;QAEA,OAAO6H,YAAY,CAACpI,IAAI,EAAEgH,GAAG,EAAEC,GAAG,EAAEa,IAAI,EAAE3E,MAAM,EAAE2D,iBAAiB,CAACvC,OAAO,EAAE/B,KAAK,CAAC;MACrF;IACF;IAEA,IAAImG,mBAAmB,GAAGtK,oBAAoB,CAACyI,iBAAiB;IAChE,IAAI8B,wBAAwB,GAAGvK,oBAAoB,CAACY,sBAAsB;IAE1E,SAAS4J,+BAA+BA,CAACjD,OAAO,EAAE;MAChD;QACE,IAAIA,OAAO,EAAE;UACX,IAAIC,KAAK,GAAGD,OAAO,CAACE,MAAM;UAC1B,IAAI5G,KAAK,GAAGuG,oCAAoC,CAACG,OAAO,CAAC5F,IAAI,EAAE4F,OAAO,CAACG,OAAO,EAAEF,KAAK,GAAGA,KAAK,CAAC7F,IAAI,GAAG,IAAI,CAAC;UAC1G4I,wBAAwB,CAAC5C,kBAAkB,CAAC9G,KAAK,CAAC;QACpD,CAAC,MAAM;UACL0J,wBAAwB,CAAC5C,kBAAkB,CAAC,IAAI,CAAC;QACnD;MACF;IACF;IAEA,IAAI8C,6BAA6B;IAEjC;MACEA,6BAA6B,GAAG,KAAK;IACvC;IACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA,SAASC,cAAcA,CAACC,MAAM,EAAE;MAC9B;QACE,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,CAAC/I,QAAQ,KAAK1D,kBAAkB;MAChG;IACF;IAEA,SAAS0M,2BAA2BA,CAAA,EAAG;MACrC;QACE,IAAIN,mBAAmB,CAACpE,OAAO,EAAE;UAC/B,IAAI/D,IAAI,GAAGE,gBAAgB,CAACiI,mBAAmB,CAACpE,OAAO,CAACvE,IAAI,CAAC;UAE7D,IAAIQ,IAAI,EAAE;YACR,OAAO,kCAAkC,GAAGA,IAAI,GAAG,IAAI;UACzD;QACF;QAEA,OAAO,EAAE;MACX;IACF;IAEA,SAAS0I,0BAA0BA,CAAC/F,MAAM,EAAE;MAC1C;QACE,IAAIA,MAAM,KAAKE,SAAS,EAAE;UACxB,IAAI8F,QAAQ,GAAGhG,MAAM,CAACgG,QAAQ,CAAChE,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;UACvD,IAAIiE,UAAU,GAAGjG,MAAM,CAACiG,UAAU;UAClC,OAAO,yBAAyB,GAAGD,QAAQ,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG;QACtE;QAEA,OAAO,EAAE;MACX;IACF;IACA;AACA;AACA;AACA;AACA;;IAGA,IAAIC,qBAAqB,GAAG,CAAC,CAAC;IAE9B,SAASC,4BAA4BA,CAACC,UAAU,EAAE;MAChD;QACE,IAAIpH,IAAI,GAAG8G,2BAA2B,CAAC,CAAC;QAExC,IAAI,CAAC9G,IAAI,EAAE;UACT,IAAIqH,UAAU,GAAG,OAAOD,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGA,UAAU,CAAChJ,WAAW,IAAIgJ,UAAU,CAAC/I,IAAI;UAExG,IAAIgJ,UAAU,EAAE;YACdrH,IAAI,GAAG,6CAA6C,GAAGqH,UAAU,GAAG,IAAI;UAC1E;QACF;QAEA,OAAOrH,IAAI;MACb;IACF;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASsH,mBAAmBA,CAAC7D,OAAO,EAAE2D,UAAU,EAAE;MAChD;QACE,IAAI,CAAC3D,OAAO,CAACyC,MAAM,IAAIzC,OAAO,CAACyC,MAAM,CAACqB,SAAS,IAAI9D,OAAO,CAACoB,GAAG,IAAI,IAAI,EAAE;UACtE;QACF;QAEApB,OAAO,CAACyC,MAAM,CAACqB,SAAS,GAAG,IAAI;QAC/B,IAAIC,yBAAyB,GAAGL,4BAA4B,CAACC,UAAU,CAAC;QAExE,IAAIF,qBAAqB,CAACM,yBAAyB,CAAC,EAAE;UACpD;QACF;QAEAN,qBAAqB,CAACM,yBAAyB,CAAC,GAAG,IAAI,CAAC,CAAC;QACzD;QACA;;QAEA,IAAIC,UAAU,GAAG,EAAE;QAEnB,IAAIhE,OAAO,IAAIA,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACE,MAAM,KAAK6C,mBAAmB,CAACpE,OAAO,EAAE;UAC/E;UACAqF,UAAU,GAAG,8BAA8B,GAAGlJ,gBAAgB,CAACkF,OAAO,CAACE,MAAM,CAAC9F,IAAI,CAAC,GAAG,GAAG;QAC3F;QAEA6I,+BAA+B,CAACjD,OAAO,CAAC;QAExCrH,KAAK,CAAC,uDAAuD,GAAG,sEAAsE,EAAEoL,yBAAyB,EAAEC,UAAU,CAAC;QAE9Kf,+BAA+B,CAAC,IAAI,CAAC;MACvC;IACF;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASgB,iBAAiBA,CAACC,IAAI,EAAEP,UAAU,EAAE;MAC3C;QACE,IAAI,OAAOO,IAAI,KAAK,QAAQ,EAAE;UAC5B;QACF;QAEA,IAAIjL,KAAK,CAACkL,OAAO,CAACD,IAAI,CAAC,EAAE;UACvB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACnL,MAAM,EAAEqL,CAAC,EAAE,EAAE;YACpC,IAAIC,KAAK,GAAGH,IAAI,CAACE,CAAC,CAAC;YAEnB,IAAIjB,cAAc,CAACkB,KAAK,CAAC,EAAE;cACzBR,mBAAmB,CAACQ,KAAK,EAAEV,UAAU,CAAC;YACxC;UACF;QACF,CAAC,MAAM,IAAIR,cAAc,CAACe,IAAI,CAAC,EAAE;UAC/B;UACA,IAAIA,IAAI,CAACzB,MAAM,EAAE;YACfyB,IAAI,CAACzB,MAAM,CAACqB,SAAS,GAAG,IAAI;UAC9B;QACF,CAAC,MAAM,IAAII,IAAI,EAAE;UACf,IAAII,UAAU,GAAGhM,aAAa,CAAC4L,IAAI,CAAC;UAEpC,IAAI,OAAOI,UAAU,KAAK,UAAU,EAAE;YACpC;YACA;YACA,IAAIA,UAAU,KAAKJ,IAAI,CAACK,OAAO,EAAE;cAC/B,IAAInM,QAAQ,GAAGkM,UAAU,CAACtK,IAAI,CAACkK,IAAI,CAAC;cACpC,IAAIM,IAAI;cAER,OAAO,CAAC,CAACA,IAAI,GAAGpM,QAAQ,CAACqM,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;gBACrC,IAAIvB,cAAc,CAACqB,IAAI,CAACzH,KAAK,CAAC,EAAE;kBAC9B8G,mBAAmB,CAACW,IAAI,CAACzH,KAAK,EAAE4G,UAAU,CAAC;gBAC7C;cACF;YACF;UACF;QACF;MACF;IACF;IACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASgB,iBAAiBA,CAAC3E,OAAO,EAAE;MAClC;QACE,IAAI5F,IAAI,GAAG4F,OAAO,CAAC5F,IAAI;QAEvB,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKqD,SAAS,IAAI,OAAOrD,IAAI,KAAK,QAAQ,EAAE;UACnE;QACF;QAEA,IAAIwK,SAAS;QAEb,IAAI,OAAOxK,IAAI,KAAK,UAAU,EAAE;UAC9BwK,SAAS,GAAGxK,IAAI,CAACwK,SAAS;QAC5B,CAAC,MAAM,IAAI,OAAOxK,IAAI,KAAK,QAAQ,KAAKA,IAAI,CAACC,QAAQ,KAAKlD,sBAAsB;QAAI;QACpF;QACAiD,IAAI,CAACC,QAAQ,KAAK/C,eAAe,CAAC,EAAE;UAClCsN,SAAS,GAAGxK,IAAI,CAACwK,SAAS;QAC5B,CAAC,MAAM;UACL;QACF;QAEA,IAAIA,SAAS,EAAE;UACb;UACA,IAAIhK,IAAI,GAAGE,gBAAgB,CAACV,IAAI,CAAC;UACjCiG,cAAc,CAACuE,SAAS,EAAE5E,OAAO,CAACpD,KAAK,EAAE,MAAM,EAAEhC,IAAI,EAAEoF,OAAO,CAAC;QACjE,CAAC,MAAM,IAAI5F,IAAI,CAACyK,SAAS,KAAKpH,SAAS,IAAI,CAACyF,6BAA6B,EAAE;UACzEA,6BAA6B,GAAG,IAAI,CAAC,CAAC;;UAEtC,IAAI4B,KAAK,GAAGhK,gBAAgB,CAACV,IAAI,CAAC;UAElCzB,KAAK,CAAC,qGAAqG,EAAEmM,KAAK,IAAI,SAAS,CAAC;QAClI;QAEA,IAAI,OAAO1K,IAAI,CAAC2K,eAAe,KAAK,UAAU,IAAI,CAAC3K,IAAI,CAAC2K,eAAe,CAACC,oBAAoB,EAAE;UAC5FrM,KAAK,CAAC,4DAA4D,GAAG,kEAAkE,CAAC;QAC1I;MACF;IACF;IACA;AACA;AACA;AACA;;IAGA,SAASsM,qBAAqBA,CAACC,QAAQ,EAAE;MACvC;QACE,IAAIC,IAAI,GAAGlI,MAAM,CAACkI,IAAI,CAACD,QAAQ,CAACtI,KAAK,CAAC;QAEtC,KAAK,IAAIwH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,IAAI,CAACpM,MAAM,EAAEqL,CAAC,EAAE,EAAE;UACpC,IAAIhD,GAAG,GAAG+D,IAAI,CAACf,CAAC,CAAC;UAEjB,IAAIhD,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,KAAK,EAAE;YACvC6B,+BAA+B,CAACiC,QAAQ,CAAC;YAEzCvM,KAAK,CAAC,kDAAkD,GAAG,0DAA0D,EAAEyI,GAAG,CAAC;YAE3H6B,+BAA+B,CAAC,IAAI,CAAC;YACrC;UACF;QACF;QAEA,IAAIiC,QAAQ,CAAC7D,GAAG,KAAK,IAAI,EAAE;UACzB4B,+BAA+B,CAACiC,QAAQ,CAAC;UAEzCvM,KAAK,CAAC,uDAAuD,CAAC;UAE9DsK,+BAA+B,CAAC,IAAI,CAAC;QACvC;MACF;IACF;IAEA,SAASmC,iBAAiBA,CAAChL,IAAI,EAAEwC,KAAK,EAAEwE,GAAG,EAAEiE,gBAAgB,EAAE9H,MAAM,EAAE2E,IAAI,EAAE;MAC3E;QACE,IAAIoD,SAAS,GAAGnL,kBAAkB,CAACC,IAAI,CAAC,CAAC,CAAC;QAC1C;;QAEA,IAAI,CAACkL,SAAS,EAAE;UACd,IAAI/I,IAAI,GAAG,EAAE;UAEb,IAAInC,IAAI,KAAKqD,SAAS,IAAI,OAAOrD,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI6C,MAAM,CAACkI,IAAI,CAAC/K,IAAI,CAAC,CAACrB,MAAM,KAAK,CAAC,EAAE;YACrGwD,IAAI,IAAI,4DAA4D,GAAG,wEAAwE;UACjJ;UAEA,IAAIgJ,UAAU,GAAGjC,0BAA0B,CAAC/F,MAAM,CAAC;UAEnD,IAAIgI,UAAU,EAAE;YACdhJ,IAAI,IAAIgJ,UAAU;UACpB,CAAC,MAAM;YACLhJ,IAAI,IAAI8G,2BAA2B,CAAC,CAAC;UACvC;UAEA,IAAImC,UAAU;UAEd,IAAIpL,IAAI,KAAK,IAAI,EAAE;YACjBoL,UAAU,GAAG,MAAM;UACrB,CAAC,MAAM,IAAIvM,KAAK,CAACkL,OAAO,CAAC/J,IAAI,CAAC,EAAE;YAC9BoL,UAAU,GAAG,OAAO;UACtB,CAAC,MAAM,IAAIpL,IAAI,KAAKqD,SAAS,IAAIrD,IAAI,CAACC,QAAQ,KAAK1D,kBAAkB,EAAE;YACrE6O,UAAU,GAAG,GAAG,IAAI1K,gBAAgB,CAACV,IAAI,CAACA,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;YACrEmC,IAAI,GAAG,oEAAoE;UAC7E,CAAC,MAAM;YACLiJ,UAAU,GAAG,OAAOpL,IAAI;UAC1B;UAEAzB,KAAK,CAAC,uDAAuD,GAAG,0DAA0D,GAAG,4BAA4B,EAAE6M,UAAU,EAAEjJ,IAAI,CAAC;QAC9K;QAEA,IAAIyD,OAAO,GAAG2C,MAAM,CAACvI,IAAI,EAAEwC,KAAK,EAAEwE,GAAG,EAAE7D,MAAM,EAAE2E,IAAI,CAAC,CAAC,CAAC;QACtD;;QAEA,IAAIlC,OAAO,IAAI,IAAI,EAAE;UACnB,OAAOA,OAAO;QAChB,CAAC,CAAC;QACF;QACA;QACA;QACA;;QAGA,IAAIsF,SAAS,EAAE;UACb,IAAIG,QAAQ,GAAG7I,KAAK,CAAC6I,QAAQ;UAE7B,IAAIA,QAAQ,KAAKhI,SAAS,EAAE;YAC1B,IAAI4H,gBAAgB,EAAE;cACpB,IAAIpM,KAAK,CAACkL,OAAO,CAACsB,QAAQ,CAAC,EAAE;gBAC3B,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,QAAQ,CAAC1M,MAAM,EAAEqL,CAAC,EAAE,EAAE;kBACxCH,iBAAiB,CAACwB,QAAQ,CAACrB,CAAC,CAAC,EAAEhK,IAAI,CAAC;gBACtC;gBAEA,IAAI6C,MAAM,CAACyF,MAAM,EAAE;kBACjBzF,MAAM,CAACyF,MAAM,CAAC+C,QAAQ,CAAC;gBACzB;cACF,CAAC,MAAM;gBACL9M,KAAK,CAAC,wDAAwD,GAAG,gEAAgE,GAAG,kCAAkC,CAAC;cACzK;YACF,CAAC,MAAM;cACLsL,iBAAiB,CAACwB,QAAQ,EAAErL,IAAI,CAAC;YACnC;UACF;QACF;QAEA,IAAIA,IAAI,KAAKvD,OAAO,CAACC,QAAQ,EAAE;UAC7BmO,qBAAqB,CAACjF,OAAO,CAAC;QAChC,CAAC,MAAM;UACL2E,iBAAiB,CAAC3E,OAAO,CAAC;QAC5B;QAEA,OAAOA,OAAO;MAChB;IACF,CAAC,CAAC;;IAEF,IAAI0F,QAAQ,GAAIN,iBAAiB;IAEjCvO,OAAO,CAAC8L,MAAM,GAAG+C,QAAQ;EACvB,CAAC,EAAE,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "script"}