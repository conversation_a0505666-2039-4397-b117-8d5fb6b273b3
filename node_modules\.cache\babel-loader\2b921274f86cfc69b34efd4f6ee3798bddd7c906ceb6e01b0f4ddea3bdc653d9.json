{"ast": null, "code": "import NoopTransition from '@restart/ui/NoopTransition';\nimport Fade from './Fade';\nexport default function getTabTransitionComponent(transition) {\n  if (typeof transition === 'boolean') {\n    return transition ? Fade : NoopTransition;\n  }\n  return transition;\n}", "map": {"version": 3, "names": ["NoopTransition", "Fade", "getTabTransitionComponent", "transition"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/getTabTransitionComponent.js"], "sourcesContent": ["import NoopTransition from '@restart/ui/NoopTransition';\nimport Fade from './Fade';\nexport default function getTabTransitionComponent(transition) {\n  if (typeof transition === 'boolean') {\n    return transition ? Fade : NoopTransition;\n  }\n  return transition;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,4BAA4B;AACvD,OAAOC,IAAI,MAAM,QAAQ;AACzB,eAAe,SAASC,yBAAyBA,CAACC,UAAU,EAAE;EAC5D,IAAI,OAAOA,UAAU,KAAK,SAAS,EAAE;IACnC,OAAOA,UAAU,GAAGF,IAAI,GAAGD,cAAc;EAC3C;EACA,OAAOG,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}