{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback } from 'react';\nimport { ENTERED, ENTERING } from 'react-transition-group/Transition';\nimport { getChildRef } from '@restart/ui/utils';\nimport transitionEndListener from './transitionEndListener';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst fadeStyles = {\n  [ENTERING]: 'show',\n  [ENTERED]: 'show'\n};\nconst Fade = /*#__PURE__*/React.forwardRef(({\n  className,\n  children,\n  transitionClasses = {},\n  onEnter,\n  ...rest\n}, ref) => {\n  const props = {\n    in: false,\n    timeout: 300,\n    mountOnEnter: false,\n    unmountOnExit: false,\n    appear: false,\n    ...rest\n  };\n  const handleEnter = useCallback((node, isAppearing) => {\n    triggerBrowserReflow(node);\n    onEnter == null || onEnter(node, isAppearing);\n  }, [onEnter]);\n  return /*#__PURE__*/_jsx(TransitionWrapper, {\n    ref: ref,\n    addEndListener: transitionEndListener,\n    ...props,\n    onEnter: handleEnter,\n    childRef: getChildRef(children),\n    children: (status, innerProps) => /*#__PURE__*/React.cloneElement(children, {\n      ...innerProps,\n      className: classNames('fade', className, children.props.className, fadeStyles[status], transitionClasses[status])\n    })\n  });\n});\nFade.displayName = 'Fade';\nexport default Fade;", "map": {"version": 3, "names": ["classNames", "React", "useCallback", "ENTERED", "ENTERING", "getChildRef", "transitionEndListener", "triggerBrowserReflow", "TransitionWrapper", "jsx", "_jsx", "fadeStyles", "Fade", "forwardRef", "className", "children", "transitionClasses", "onEnter", "rest", "ref", "props", "in", "timeout", "mountOnEnter", "unmountOnExit", "appear", "handleEnter", "node", "isAppearing", "addEndListener", "childRef", "status", "innerProps", "cloneElement", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/Fade.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback } from 'react';\nimport { ENTERED, ENTERING } from 'react-transition-group/Transition';\nimport { getChildRef } from '@restart/ui/utils';\nimport transitionEndListener from './transitionEndListener';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst fadeStyles = {\n  [ENTERING]: 'show',\n  [ENTERED]: 'show'\n};\nconst Fade = /*#__PURE__*/React.forwardRef(({\n  className,\n  children,\n  transitionClasses = {},\n  onEnter,\n  ...rest\n}, ref) => {\n  const props = {\n    in: false,\n    timeout: 300,\n    mountOnEnter: false,\n    unmountOnExit: false,\n    appear: false,\n    ...rest\n  };\n  const handleEnter = useCallback((node, isAppearing) => {\n    triggerBrowserReflow(node);\n    onEnter == null || onEnter(node, isAppearing);\n  }, [onEnter]);\n  return /*#__PURE__*/_jsx(TransitionWrapper, {\n    ref: ref,\n    addEndListener: transitionEndListener,\n    ...props,\n    onEnter: handleEnter,\n    childRef: getChildRef(children),\n    children: (status, innerProps) => /*#__PURE__*/React.cloneElement(children, {\n      ...innerProps,\n      className: classNames('fade', className, children.props.className, fadeStyles[status], transitionClasses[status])\n    })\n  });\n});\nFade.displayName = 'Fade';\nexport default Fade;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,OAAO;AACnC,SAASC,OAAO,EAAEC,QAAQ,QAAQ,mCAAmC;AACrE,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG;EACjB,CAACP,QAAQ,GAAG,MAAM;EAClB,CAACD,OAAO,GAAG;AACb,CAAC;AACD,MAAMS,IAAI,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,CAAC;EAC1CC,SAAS;EACTC,QAAQ;EACRC,iBAAiB,GAAG,CAAC,CAAC;EACtBC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,KAAK,GAAG;IACZC,EAAE,EAAE,KAAK;IACTC,OAAO,EAAE,GAAG;IACZC,YAAY,EAAE,KAAK;IACnBC,aAAa,EAAE,KAAK;IACpBC,MAAM,EAAE,KAAK;IACb,GAAGP;EACL,CAAC;EACD,MAAMQ,WAAW,GAAGxB,WAAW,CAAC,CAACyB,IAAI,EAAEC,WAAW,KAAK;IACrDrB,oBAAoB,CAACoB,IAAI,CAAC;IAC1BV,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACU,IAAI,EAAEC,WAAW,CAAC;EAC/C,CAAC,EAAE,CAACX,OAAO,CAAC,CAAC;EACb,OAAO,aAAaP,IAAI,CAACF,iBAAiB,EAAE;IAC1CW,GAAG,EAAEA,GAAG;IACRU,cAAc,EAAEvB,qBAAqB;IACrC,GAAGc,KAAK;IACRH,OAAO,EAAES,WAAW;IACpBI,QAAQ,EAAEzB,WAAW,CAACU,QAAQ,CAAC;IAC/BA,QAAQ,EAAEA,CAACgB,MAAM,EAAEC,UAAU,KAAK,aAAa/B,KAAK,CAACgC,YAAY,CAAClB,QAAQ,EAAE;MAC1E,GAAGiB,UAAU;MACblB,SAAS,EAAEd,UAAU,CAAC,MAAM,EAAEc,SAAS,EAAEC,QAAQ,CAACK,KAAK,CAACN,SAAS,EAAEH,UAAU,CAACoB,MAAM,CAAC,EAAEf,iBAAiB,CAACe,MAAM,CAAC;IAClH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFnB,IAAI,CAACsB,WAAW,GAAG,MAAM;AACzB,eAAetB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}