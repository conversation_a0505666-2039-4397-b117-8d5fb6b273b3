{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_github = register(\"github\", {\n  \"color\": \"#24292e\",\n  \"path\": \"M0 0v64h64V0zm37.1 47.2c-.8.2-1.1-.3-1.1-.8V42c0-1.5-.5-2.5-1.1-3 3.6-.4 7.3-1.7 7.3-7.9 0-1.7-.6-3.2-1.6-4.3.2-.4.7-2-.2-4.2 0 0-1.3-.4-4.4 1.6-1.3-.4-2.6-.5-4-.5s-2.7.2-4 .5c-3.1-2.1-4.4-1.6-4.4-1.6-.9 2.2-.3 3.8-.2 4.2-1 1.1-1.6 2.5-1.6 4.3 0 6.1 3.7 7.5 7.3 7.9-.5.4-.9 1.1-1 2.1-.9.4-3.2 1.1-4.7-1.3 0 0-.8-1.5-2.5-1.6 0 0-1.6 0-.1 1 0 0 1 .5 1.8 2.3 0 0 .9 3.1 5.4 2.1v2.7c0 .4-.3.9-1.1.8-6.3-2-10.9-8-10.9-15.1 0-8.8 7.2-16 16-16s16 7.2 16 16c0 7.1-4.6 13.1-10.9 15.2\"\n});\nexport { _socialIcons_github as default };", "map": {"version": 3, "names": ["register", "_socialIcons_github", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/github.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_github = register(\"github\", {\"color\":\"#24292e\",\"path\":\"M0 0v64h64V0zm37.1 47.2c-.8.2-1.1-.3-1.1-.8V42c0-1.5-.5-2.5-1.1-3 3.6-.4 7.3-1.7 7.3-7.9 0-1.7-.6-3.2-1.6-4.3.2-.4.7-2-.2-4.2 0 0-1.3-.4-4.4 1.6-1.3-.4-2.6-.5-4-.5s-2.7.2-4 .5c-3.1-2.1-4.4-1.6-4.4-1.6-.9 2.2-.3 3.8-.2 4.2-1 1.1-1.6 2.5-1.6 4.3 0 6.1 3.7 7.5 7.3 7.9-.5.4-.9 1.1-1 2.1-.9.4-3.2 1.1-4.7-1.3 0 0-.8-1.5-2.5-1.6 0 0-1.6 0-.1 1 0 0 1 .5 1.8 2.3 0 0 .9 3.1 5.4 2.1v2.7c0 .4-.3.9-1.1.8-6.3-2-10.9-8-10.9-15.1 0-8.8 7.2-16 16-16s16 7.2 16 16c0 7.1-4.6 13.1-10.9 15.2\"});\n\nexport { _socialIcons_github as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,mBAAmB,GAAGD,QAAQ,CAAC,QAAQ,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAA4d,CAAC,CAAC;AAEriB,SAASC,mBAAmB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}