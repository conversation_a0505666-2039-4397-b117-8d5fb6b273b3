{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport BaseDropdown from '@restart/ui/Dropdown';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport DropdownContext from './DropdownContext';\nimport DropdownDivider from './DropdownDivider';\nimport DropdownHeader from './DropdownHeader';\nimport DropdownItem from './DropdownItem';\nimport DropdownItemText from './DropdownItemText';\nimport DropdownMenu, { getDropdownMenuPlacement } from './DropdownMenu';\nimport DropdownToggle from './DropdownToggle';\nimport InputGroupContext from './InputGroupContext';\nimport { useBootstrapPrefix, useIsRTL } from './ThemeProvider';\nimport { alignPropType } from './types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Dropdown = /*#__PURE__*/React.forwardRef((pProps, ref) => {\n  const {\n    bsPrefix,\n    drop = 'down',\n    show,\n    className,\n    align = 'start',\n    onSelect,\n    onToggle,\n    focusFirstItemOnShow,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    navbar: _4,\n    autoClose = true,\n    ...props\n  } = useUncontrolled(pProps, {\n    show: 'onToggle'\n  });\n  const isInputGroup = useContext(InputGroupContext);\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown');\n  const isRTL = useIsRTL();\n  const isClosingPermitted = source => {\n    // autoClose=false only permits close on button click\n    if (autoClose === false) return source === 'click';\n\n    // autoClose=inside doesn't permit close on rootClose\n    if (autoClose === 'inside') return source !== 'rootClose';\n\n    // autoClose=outside doesn't permit close on select\n    if (autoClose === 'outside') return source !== 'select';\n    return true;\n  };\n  const handleToggle = useEventCallback((nextShow, meta) => {\n    var _meta$originalEvent;\n    /** Checking if target of event is ToggleButton,\n     * if it is then nullify mousedown event\n     */\n    const isToggleButton = (_meta$originalEvent = meta.originalEvent) == null || (_meta$originalEvent = _meta$originalEvent.target) == null ? void 0 : _meta$originalEvent.classList.contains('dropdown-toggle');\n    if (isToggleButton && meta.source === 'mousedown') {\n      return;\n    }\n    if (meta.originalEvent.currentTarget === document && (meta.source !== 'keydown' || meta.originalEvent.key === 'Escape')) meta.source = 'rootClose';\n    if (isClosingPermitted(meta.source)) onToggle == null || onToggle(nextShow, meta);\n  });\n  const alignEnd = align === 'end';\n  const placement = getDropdownMenuPlacement(alignEnd, drop, isRTL);\n  const contextValue = useMemo(() => ({\n    align,\n    drop,\n    isRTL\n  }), [align, drop, isRTL]);\n  const directionClasses = {\n    down: prefix,\n    'down-centered': `${prefix}-center`,\n    up: 'dropup',\n    'up-centered': 'dropup-center dropup',\n    end: 'dropend',\n    start: 'dropstart'\n  };\n  return /*#__PURE__*/_jsx(DropdownContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(BaseDropdown, {\n      placement: placement,\n      show: show,\n      onSelect: onSelect,\n      onToggle: handleToggle,\n      focusFirstItemOnShow: focusFirstItemOnShow,\n      itemSelector: `.${prefix}-item:not(.disabled):not(:disabled)`,\n      children: isInputGroup ? props.children : /*#__PURE__*/_jsx(Component, {\n        ...props,\n        ref: ref,\n        className: classNames(className, show && 'show', directionClasses[drop])\n      })\n    })\n  });\n});\nDropdown.displayName = 'Dropdown';\nexport default Object.assign(Dropdown, {\n  Toggle: DropdownToggle,\n  Menu: DropdownMenu,\n  Item: DropdownItem,\n  ItemText: DropdownItemText,\n  Divider: DropdownDivider,\n  Header: DropdownHeader\n});", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useMemo", "BaseDropdown", "useUncontrolled", "useEventCallback", "DropdownContext", "DropdownDivider", "DropdownHeader", "DropdownItem", "DropdownItemText", "DropdownMenu", "getDropdownMenuPlacement", "DropdownToggle", "InputGroupContext", "useBootstrapPrefix", "useIsRTL", "alignPropType", "jsx", "_jsx", "Dropdown", "forwardRef", "pProps", "ref", "bsPrefix", "drop", "show", "className", "align", "onSelect", "onToggle", "focusFirstItemOnShow", "as", "Component", "navbar", "_4", "autoClose", "props", "isInputGroup", "prefix", "isRTL", "isClosingPermitted", "source", "handleToggle", "nextShow", "meta", "_meta$originalEvent", "isToggleButton", "originalEvent", "target", "classList", "contains", "currentTarget", "document", "key", "alignEnd", "placement", "contextValue", "directionClasses", "down", "up", "end", "start", "Provider", "value", "children", "itemSelector", "displayName", "Object", "assign", "Toggle", "<PERSON><PERSON>", "<PERSON><PERSON>", "ItemText", "Divider", "Header"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/Dropdown.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport BaseDropdown from '@restart/ui/Dropdown';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport DropdownContext from './DropdownContext';\nimport DropdownDivider from './DropdownDivider';\nimport DropdownHeader from './DropdownHeader';\nimport DropdownItem from './DropdownItem';\nimport DropdownItemText from './DropdownItemText';\nimport DropdownMenu, { getDropdownMenuPlacement } from './DropdownMenu';\nimport DropdownToggle from './DropdownToggle';\nimport InputGroupContext from './InputGroupContext';\nimport { useBootstrapPrefix, useIsRTL } from './ThemeProvider';\nimport { alignPropType } from './types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Dropdown = /*#__PURE__*/React.forwardRef((pProps, ref) => {\n  const {\n    bsPrefix,\n    drop = 'down',\n    show,\n    className,\n    align = 'start',\n    onSelect,\n    onToggle,\n    focusFirstItemOnShow,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    navbar: _4,\n    autoClose = true,\n    ...props\n  } = useUncontrolled(pProps, {\n    show: 'onToggle'\n  });\n  const isInputGroup = useContext(InputGroupContext);\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown');\n  const isRTL = useIsRTL();\n  const isClosingPermitted = source => {\n    // autoClose=false only permits close on button click\n    if (autoClose === false) return source === 'click';\n\n    // autoClose=inside doesn't permit close on rootClose\n    if (autoClose === 'inside') return source !== 'rootClose';\n\n    // autoClose=outside doesn't permit close on select\n    if (autoClose === 'outside') return source !== 'select';\n    return true;\n  };\n  const handleToggle = useEventCallback((nextShow, meta) => {\n    var _meta$originalEvent;\n    /** Checking if target of event is ToggleButton,\n     * if it is then nullify mousedown event\n     */\n    const isToggleButton = (_meta$originalEvent = meta.originalEvent) == null || (_meta$originalEvent = _meta$originalEvent.target) == null ? void 0 : _meta$originalEvent.classList.contains('dropdown-toggle');\n    if (isToggleButton && meta.source === 'mousedown') {\n      return;\n    }\n    if (meta.originalEvent.currentTarget === document && (meta.source !== 'keydown' || meta.originalEvent.key === 'Escape')) meta.source = 'rootClose';\n    if (isClosingPermitted(meta.source)) onToggle == null || onToggle(nextShow, meta);\n  });\n  const alignEnd = align === 'end';\n  const placement = getDropdownMenuPlacement(alignEnd, drop, isRTL);\n  const contextValue = useMemo(() => ({\n    align,\n    drop,\n    isRTL\n  }), [align, drop, isRTL]);\n  const directionClasses = {\n    down: prefix,\n    'down-centered': `${prefix}-center`,\n    up: 'dropup',\n    'up-centered': 'dropup-center dropup',\n    end: 'dropend',\n    start: 'dropstart'\n  };\n  return /*#__PURE__*/_jsx(DropdownContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(BaseDropdown, {\n      placement: placement,\n      show: show,\n      onSelect: onSelect,\n      onToggle: handleToggle,\n      focusFirstItemOnShow: focusFirstItemOnShow,\n      itemSelector: `.${prefix}-item:not(.disabled):not(:disabled)`,\n      children: isInputGroup ? props.children : /*#__PURE__*/_jsx(Component, {\n        ...props,\n        ref: ref,\n        className: classNames(className, show && 'show', directionClasses[drop])\n      })\n    })\n  });\n});\nDropdown.displayName = 'Dropdown';\nexport default Object.assign(Dropdown, {\n  Toggle: DropdownToggle,\n  Menu: DropdownMenu,\n  Item: DropdownItem,\n  ItemText: DropdownItemText,\n  Divider: DropdownDivider,\n  Header: DropdownHeader\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,YAAY,IAAIC,wBAAwB,QAAQ,gBAAgB;AACvE,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,kBAAkB,EAAEC,QAAQ,QAAQ,iBAAiB;AAC9D,SAASC,aAAa,QAAQ,SAAS;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,QAAQ,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAAC,CAACC,MAAM,EAAEC,GAAG,KAAK;EAC9D,MAAM;IACJC,QAAQ;IACRC,IAAI,GAAG,MAAM;IACbC,IAAI;IACJC,SAAS;IACTC,KAAK,GAAG,OAAO;IACfC,QAAQ;IACRC,QAAQ;IACRC,oBAAoB;IACpB;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrBC,MAAM,EAAEC,EAAE;IACVC,SAAS,GAAG,IAAI;IAChB,GAAGC;EACL,CAAC,GAAGjC,eAAe,CAACkB,MAAM,EAAE;IAC1BI,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMY,YAAY,GAAGrC,UAAU,CAACa,iBAAiB,CAAC;EAClD,MAAMyB,MAAM,GAAGxB,kBAAkB,CAACS,QAAQ,EAAE,UAAU,CAAC;EACvD,MAAMgB,KAAK,GAAGxB,QAAQ,CAAC,CAAC;EACxB,MAAMyB,kBAAkB,GAAGC,MAAM,IAAI;IACnC;IACA,IAAIN,SAAS,KAAK,KAAK,EAAE,OAAOM,MAAM,KAAK,OAAO;;IAElD;IACA,IAAIN,SAAS,KAAK,QAAQ,EAAE,OAAOM,MAAM,KAAK,WAAW;;IAEzD;IACA,IAAIN,SAAS,KAAK,SAAS,EAAE,OAAOM,MAAM,KAAK,QAAQ;IACvD,OAAO,IAAI;EACb,CAAC;EACD,MAAMC,YAAY,GAAGtC,gBAAgB,CAAC,CAACuC,QAAQ,EAAEC,IAAI,KAAK;IACxD,IAAIC,mBAAmB;IACvB;AACJ;AACA;IACI,MAAMC,cAAc,GAAG,CAACD,mBAAmB,GAAGD,IAAI,CAACG,aAAa,KAAK,IAAI,IAAI,CAACF,mBAAmB,GAAGA,mBAAmB,CAACG,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,mBAAmB,CAACI,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC;IAC5M,IAAIJ,cAAc,IAAIF,IAAI,CAACH,MAAM,KAAK,WAAW,EAAE;MACjD;IACF;IACA,IAAIG,IAAI,CAACG,aAAa,CAACI,aAAa,KAAKC,QAAQ,KAAKR,IAAI,CAACH,MAAM,KAAK,SAAS,IAAIG,IAAI,CAACG,aAAa,CAACM,GAAG,KAAK,QAAQ,CAAC,EAAET,IAAI,CAACH,MAAM,GAAG,WAAW;IAClJ,IAAID,kBAAkB,CAACI,IAAI,CAACH,MAAM,CAAC,EAAEZ,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACc,QAAQ,EAAEC,IAAI,CAAC;EACnF,CAAC,CAAC;EACF,MAAMU,QAAQ,GAAG3B,KAAK,KAAK,KAAK;EAChC,MAAM4B,SAAS,GAAG5C,wBAAwB,CAAC2C,QAAQ,EAAE9B,IAAI,EAAEe,KAAK,CAAC;EACjE,MAAMiB,YAAY,GAAGvD,OAAO,CAAC,OAAO;IAClC0B,KAAK;IACLH,IAAI;IACJe;EACF,CAAC,CAAC,EAAE,CAACZ,KAAK,EAAEH,IAAI,EAAEe,KAAK,CAAC,CAAC;EACzB,MAAMkB,gBAAgB,GAAG;IACvBC,IAAI,EAAEpB,MAAM;IACZ,eAAe,EAAE,GAAGA,MAAM,SAAS;IACnCqB,EAAE,EAAE,QAAQ;IACZ,aAAa,EAAE,sBAAsB;IACrCC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACD,OAAO,aAAa3C,IAAI,CAACb,eAAe,CAACyD,QAAQ,EAAE;IACjDC,KAAK,EAAEP,YAAY;IACnBQ,QAAQ,EAAE,aAAa9C,IAAI,CAAChB,YAAY,EAAE;MACxCqD,SAAS,EAAEA,SAAS;MACpB9B,IAAI,EAAEA,IAAI;MACVG,QAAQ,EAAEA,QAAQ;MAClBC,QAAQ,EAAEa,YAAY;MACtBZ,oBAAoB,EAAEA,oBAAoB;MAC1CmC,YAAY,EAAE,IAAI3B,MAAM,qCAAqC;MAC7D0B,QAAQ,EAAE3B,YAAY,GAAGD,KAAK,CAAC4B,QAAQ,GAAG,aAAa9C,IAAI,CAACc,SAAS,EAAE;QACrE,GAAGI,KAAK;QACRd,GAAG,EAAEA,GAAG;QACRI,SAAS,EAAE5B,UAAU,CAAC4B,SAAS,EAAED,IAAI,IAAI,MAAM,EAAEgC,gBAAgB,CAACjC,IAAI,CAAC;MACzE,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,QAAQ,CAAC+C,WAAW,GAAG,UAAU;AACjC,eAAeC,MAAM,CAACC,MAAM,CAACjD,QAAQ,EAAE;EACrCkD,MAAM,EAAEzD,cAAc;EACtB0D,IAAI,EAAE5D,YAAY;EAClB6D,IAAI,EAAE/D,YAAY;EAClBgE,QAAQ,EAAE/D,gBAAgB;EAC1BgE,OAAO,EAAEnE,eAAe;EACxBoE,MAAM,EAAEnE;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}