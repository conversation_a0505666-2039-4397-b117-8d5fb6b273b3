{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormContext from './FormContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormSelect = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  htmlSize,\n  className,\n  isValid = false,\n  isInvalid = false,\n  id,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-select');\n  return /*#__PURE__*/_jsx(\"select\", {\n    ...props,\n    size: htmlSize,\n    ref: ref,\n    className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, isValid && `is-valid`, isInvalid && `is-invalid`),\n    id: id || controlId\n  });\n});\nFormSelect.displayName = 'FormSelect';\nexport default FormSelect;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useBootstrapPrefix", "FormContext", "jsx", "_jsx", "FormSelect", "forwardRef", "bsPrefix", "size", "htmlSize", "className", "<PERSON><PERSON><PERSON><PERSON>", "isInvalid", "id", "props", "ref", "controlId", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/FormSelect.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormContext from './FormContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormSelect = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  htmlSize,\n  className,\n  isValid = false,\n  isInvalid = false,\n  id,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-select');\n  return /*#__PURE__*/_jsx(\"select\", {\n    ...props,\n    size: htmlSize,\n    ref: ref,\n    className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, isValid && `is-valid`, isInvalid && `is-invalid`),\n    id: id || controlId\n  });\n});\nFormSelect.displayName = 'FormSelect';\nexport default FormSelect;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EAChDC,QAAQ;EACRC,IAAI;EACJC,QAAQ;EACRC,SAAS;EACTC,OAAO,GAAG,KAAK;EACfC,SAAS,GAAG,KAAK;EACjBC,EAAE;EACF,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC;EACF,CAAC,GAAGhB,UAAU,CAACE,WAAW,CAAC;EAC3BK,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,aAAa,CAAC;EACtD,OAAO,aAAaH,IAAI,CAAC,QAAQ,EAAE;IACjC,GAAGU,KAAK;IACRN,IAAI,EAAEC,QAAQ;IACdM,GAAG,EAAEA,GAAG;IACRL,SAAS,EAAEZ,UAAU,CAACY,SAAS,EAAEH,QAAQ,EAAEC,IAAI,IAAI,GAAGD,QAAQ,IAAIC,IAAI,EAAE,EAAEG,OAAO,IAAI,UAAU,EAAEC,SAAS,IAAI,YAAY,CAAC;IAC3HC,EAAE,EAAEA,EAAE,IAAIG;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFX,UAAU,CAACY,WAAW,GAAG,YAAY;AACrC,eAAeZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}