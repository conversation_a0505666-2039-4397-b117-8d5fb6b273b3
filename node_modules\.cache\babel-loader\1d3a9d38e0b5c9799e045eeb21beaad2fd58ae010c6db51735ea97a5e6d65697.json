{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ButtonGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  vertical = false,\n  className,\n  role = 'group',\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...rest\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn-group');\n  let baseClass = prefix;\n  if (vertical) baseClass = `${prefix}-vertical`;\n  return /*#__PURE__*/_jsx(Component, {\n    ...rest,\n    ref: ref,\n    role: role,\n    className: classNames(className, baseClass, size && `${prefix}-${size}`)\n  });\n});\nButtonGroup.displayName = 'ButtonGroup';\nexport default ButtonGroup;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "ButtonGroup", "forwardRef", "bsPrefix", "size", "vertical", "className", "role", "as", "Component", "rest", "ref", "prefix", "baseClass", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/ButtonGroup.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ButtonGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  vertical = false,\n  className,\n  role = 'group',\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...rest\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn-group');\n  let baseClass = prefix;\n  if (vertical) baseClass = `${prefix}-vertical`;\n  return /*#__PURE__*/_jsx(Component, {\n    ...rest,\n    ref: ref,\n    role: role,\n    className: classNames(className, baseClass, size && `${prefix}-${size}`)\n  });\n});\nButtonGroup.displayName = 'ButtonGroup';\nexport default ButtonGroup;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EACjDC,QAAQ;EACRC,IAAI;EACJC,QAAQ,GAAG,KAAK;EAChBC,SAAS;EACTC,IAAI,GAAG,OAAO;EACd;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGd,kBAAkB,CAACK,QAAQ,EAAE,WAAW,CAAC;EACxD,IAAIU,SAAS,GAAGD,MAAM;EACtB,IAAIP,QAAQ,EAAEQ,SAAS,GAAG,GAAGD,MAAM,WAAW;EAC9C,OAAO,aAAaZ,IAAI,CAACS,SAAS,EAAE;IAClC,GAAGC,IAAI;IACPC,GAAG,EAAEA,GAAG;IACRJ,IAAI,EAAEA,IAAI;IACVD,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAEO,SAAS,EAAET,IAAI,IAAI,GAAGQ,MAAM,IAAIR,IAAI,EAAE;EACzE,CAAC,CAAC;AACJ,CAAC,CAAC;AACFH,WAAW,CAACa,WAAW,GAAG,aAAa;AACvC,eAAeb,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}