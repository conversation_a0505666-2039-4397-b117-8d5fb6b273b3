{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_squarespace = register(\"squarespace\", {\n  \"color\": \"#1C1C1C\",\n  \"path\": \"M0 0v64h64V0zm39.6 21.1c.6.6.6 1.6 0 2.2s-1.6.6-2.2 0c-1.2-1.2-3.2-1.2-4.4 0l-9.8 9.8c-.6.6-1.6.6-2.2 0s-.6-1.6 0-2.2l9.8-9.8c2.5-2.4 6.4-2.4 8.8 0M17.8 36.4c-2.4-2.4-2.4-6.3 0-8.7l7.5-7.5c1.2-1.2 3.2-1.2 4.4 0L20 29.8c-1.2 1.2-1.2 3.2 0 4.4s3.2 1.2 4.4 0l9.8-9.8c.6-.6 1.6-.6 2.2 0s.6 1.6 0 2.2l-9.8 9.8c-2.5 2.4-6.4 2.4-8.8 0m6.6 6.5c-.6-.6-.6-1.6 0-2.2s1.6-.6 2.2 0c1.2 1.2 3.2 1.2 4.4 0l9.8-9.8c.6-.6 1.6-.6 2.2 0s.6 1.6 0 2.2l-9.8 9.8c-2.5 2.4-6.4 2.4-8.8 0m21.8-6.5-7.5 7.5c-1.2 1.2-3.2 1.2-4.4 0l9.6-9.6c1.2-1.2 1.2-3.2 0-4.4s-3.2-1.2-4.4 0l-9.8 9.8c-.6.6-1.6.6-2.2 0s-.6-1.6 0-2.2l9.8-9.8c2.4-2.4 6.3-2.4 8.7 0 2.6 2.3 2.6 6.3.2 8.7\"\n});\nexport { _socialIcons_squarespace as default };", "map": {"version": 3, "names": ["register", "_socialIcons_squarespace", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/squarespace.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_squarespace = register(\"squarespace\", {\"color\":\"#1C1C1C\",\"path\":\"M0 0v64h64V0zm39.6 21.1c.6.6.6 1.6 0 2.2s-1.6.6-2.2 0c-1.2-1.2-3.2-1.2-4.4 0l-9.8 9.8c-.6.6-1.6.6-2.2 0s-.6-1.6 0-2.2l9.8-9.8c2.5-2.4 6.4-2.4 8.8 0M17.8 36.4c-2.4-2.4-2.4-6.3 0-8.7l7.5-7.5c1.2-1.2 3.2-1.2 4.4 0L20 29.8c-1.2 1.2-1.2 3.2 0 4.4s3.2 1.2 4.4 0l9.8-9.8c.6-.6 1.6-.6 2.2 0s.6 1.6 0 2.2l-9.8 9.8c-2.5 2.4-6.4 2.4-8.8 0m6.6 6.5c-.6-.6-.6-1.6 0-2.2s1.6-.6 2.2 0c1.2 1.2 3.2 1.2 4.4 0l9.8-9.8c.6-.6 1.6-.6 2.2 0s.6 1.6 0 2.2l-9.8 9.8c-2.5 2.4-6.4 2.4-8.8 0m21.8-6.5-7.5 7.5c-1.2 1.2-3.2 1.2-4.4 0l9.6-9.6c1.2-1.2 1.2-3.2 0-4.4s-3.2-1.2-4.4 0l-9.8 9.8c-.6.6-1.6.6-2.2 0s-.6-1.6 0-2.2l9.8-9.8c2.4-2.4 6.3-2.4 8.7 0 2.6 2.3 2.6 6.3.2 8.7\"});\n\nexport { _socialIcons_squarespace as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,wBAAwB,GAAGD,QAAQ,CAAC,aAAa,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAkoB,CAAC,CAAC;AAErtB,SAASC,wBAAwB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}