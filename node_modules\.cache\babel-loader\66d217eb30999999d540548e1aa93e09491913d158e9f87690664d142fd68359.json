{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport classNames from 'classnames';\nimport AccordionContext, { isAccordionItemSelected } from './AccordionContext';\nimport AccordionItemContext from './AccordionItemContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useAccordionButton(eventKey, onClick) {\n  const {\n    activeEventKey,\n    onSelect,\n    alwaysOpen\n  } = useContext(AccordionContext);\n  return e => {\n    /*\n      Compare the event key in context with the given event key.\n      If they are the same, then collapse the component.\n    */\n    let eventKeyPassed = eventKey === activeEventKey ? null : eventKey;\n    if (alwaysOpen) {\n      if (Array.isArray(activeEventKey)) {\n        if (activeEventKey.includes(eventKey)) {\n          eventKeyPassed = activeEventKey.filter(k => k !== eventKey);\n        } else {\n          eventKeyPassed = [...activeEventKey, eventKey];\n        }\n      } else {\n        // activeEventKey is undefined.\n        eventKeyPassed = [eventKey];\n      }\n    }\n    onSelect == null || onSelect(eventKeyPassed, e);\n    onClick == null || onClick(e);\n  };\n}\nconst AccordionButton = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'button',\n  bsPrefix,\n  className,\n  onClick,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-button');\n  const {\n    eventKey\n  } = useContext(AccordionItemContext);\n  const accordionOnClick = useAccordionButton(eventKey, onClick);\n  const {\n    activeEventKey\n  } = useContext(AccordionContext);\n  if (Component === 'button') {\n    props.type = 'button';\n  }\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    onClick: accordionOnClick,\n    ...props,\n    \"aria-expanded\": Array.isArray(activeEventKey) ? activeEventKey.includes(eventKey) : eventKey === activeEventKey,\n    className: classNames(className, bsPrefix, !isAccordionItemSelected(activeEventKey, eventKey) && 'collapsed')\n  });\n});\nAccordionButton.displayName = 'AccordionButton';\nexport default AccordionButton;", "map": {"version": 3, "names": ["React", "useContext", "classNames", "AccordionContext", "isAccordionItemSelected", "AccordionItemContext", "useBootstrapPrefix", "jsx", "_jsx", "useAccordionButton", "eventKey", "onClick", "activeEventKey", "onSelect", "alwaysOpen", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "includes", "filter", "k", "Accordion<PERSON><PERSON><PERSON>", "forwardRef", "as", "Component", "bsPrefix", "className", "props", "ref", "accordionOnClick", "type", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/AccordionButton.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport classNames from 'classnames';\nimport AccordionContext, { isAccordionItemSelected } from './AccordionContext';\nimport AccordionItemContext from './AccordionItemContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useAccordionButton(eventKey, onClick) {\n  const {\n    activeEventKey,\n    onSelect,\n    alwaysOpen\n  } = useContext(AccordionContext);\n  return e => {\n    /*\n      Compare the event key in context with the given event key.\n      If they are the same, then collapse the component.\n    */\n    let eventKeyPassed = eventKey === activeEventKey ? null : eventKey;\n    if (alwaysOpen) {\n      if (Array.isArray(activeEventKey)) {\n        if (activeEventKey.includes(eventKey)) {\n          eventKeyPassed = activeEventKey.filter(k => k !== eventKey);\n        } else {\n          eventKeyPassed = [...activeEventKey, eventKey];\n        }\n      } else {\n        // activeEventKey is undefined.\n        eventKeyPassed = [eventKey];\n      }\n    }\n    onSelect == null || onSelect(eventKeyPassed, e);\n    onClick == null || onClick(e);\n  };\n}\nconst AccordionButton = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'button',\n  bsPrefix,\n  className,\n  onClick,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-button');\n  const {\n    eventKey\n  } = useContext(AccordionItemContext);\n  const accordionOnClick = useAccordionButton(eventKey, onClick);\n  const {\n    activeEventKey\n  } = useContext(AccordionContext);\n  if (Component === 'button') {\n    props.type = 'button';\n  }\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    onClick: accordionOnClick,\n    ...props,\n    \"aria-expanded\": Array.isArray(activeEventKey) ? activeEventKey.includes(eventKey) : eventKey === activeEventKey,\n    className: classNames(className, bsPrefix, !isAccordionItemSelected(activeEventKey, eventKey) && 'collapsed')\n  });\n});\nAccordionButton.displayName = 'AccordionButton';\nexport default AccordionButton;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,IAAIC,uBAAuB,QAAQ,oBAAoB;AAC9E,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACpD,MAAM;IACJC,cAAc;IACdC,QAAQ;IACRC;EACF,CAAC,GAAGb,UAAU,CAACE,gBAAgB,CAAC;EAChC,OAAOY,CAAC,IAAI;IACV;AACJ;AACA;AACA;IACI,IAAIC,cAAc,GAAGN,QAAQ,KAAKE,cAAc,GAAG,IAAI,GAAGF,QAAQ;IAClE,IAAII,UAAU,EAAE;MACd,IAAIG,KAAK,CAACC,OAAO,CAACN,cAAc,CAAC,EAAE;QACjC,IAAIA,cAAc,CAACO,QAAQ,CAACT,QAAQ,CAAC,EAAE;UACrCM,cAAc,GAAGJ,cAAc,CAACQ,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKX,QAAQ,CAAC;QAC7D,CAAC,MAAM;UACLM,cAAc,GAAG,CAAC,GAAGJ,cAAc,EAAEF,QAAQ,CAAC;QAChD;MACF,CAAC,MAAM;QACL;QACAM,cAAc,GAAG,CAACN,QAAQ,CAAC;MAC7B;IACF;IACAG,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACG,cAAc,EAAED,CAAC,CAAC;IAC/CJ,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACI,CAAC,CAAC;EAC/B,CAAC;AACH;AACA,MAAMO,eAAe,GAAG,aAAatB,KAAK,CAACuB,UAAU,CAAC,CAAC;EACrD;EACAC,EAAE,EAAEC,SAAS,GAAG,QAAQ;EACxBC,QAAQ;EACRC,SAAS;EACThB,OAAO;EACP,GAAGiB;AACL,CAAC,EAAEC,GAAG,KAAK;EACTH,QAAQ,GAAGpB,kBAAkB,CAACoB,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,MAAM;IACJhB;EACF,CAAC,GAAGT,UAAU,CAACI,oBAAoB,CAAC;EACpC,MAAMyB,gBAAgB,GAAGrB,kBAAkB,CAACC,QAAQ,EAAEC,OAAO,CAAC;EAC9D,MAAM;IACJC;EACF,CAAC,GAAGX,UAAU,CAACE,gBAAgB,CAAC;EAChC,IAAIsB,SAAS,KAAK,QAAQ,EAAE;IAC1BG,KAAK,CAACG,IAAI,GAAG,QAAQ;EACvB;EACA,OAAO,aAAavB,IAAI,CAACiB,SAAS,EAAE;IAClCI,GAAG,EAAEA,GAAG;IACRlB,OAAO,EAAEmB,gBAAgB;IACzB,GAAGF,KAAK;IACR,eAAe,EAAEX,KAAK,CAACC,OAAO,CAACN,cAAc,CAAC,GAAGA,cAAc,CAACO,QAAQ,CAACT,QAAQ,CAAC,GAAGA,QAAQ,KAAKE,cAAc;IAChHe,SAAS,EAAEzB,UAAU,CAACyB,SAAS,EAAED,QAAQ,EAAE,CAACtB,uBAAuB,CAACQ,cAAc,EAAEF,QAAQ,CAAC,IAAI,WAAW;EAC9G,CAAC,CAAC;AACJ,CAAC,CAAC;AACFY,eAAe,CAACU,WAAW,GAAG,iBAAiB;AAC/C,eAAeV,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}