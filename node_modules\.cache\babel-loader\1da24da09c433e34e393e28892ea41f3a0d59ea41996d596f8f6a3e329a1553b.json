{"ast": null, "code": "const _excluded = [\"show\", \"role\", \"className\", \"style\", \"children\", \"backdrop\", \"keyboard\", \"onBackdropClick\", \"onEscapeKeyDown\", \"transition\", \"runTransition\", \"backdropTransition\", \"runBackdropTransition\", \"autoFocus\", \"enforceFocus\", \"restoreFocus\", \"restoreFocusOptions\", \"renderDialog\", \"renderBackdrop\", \"manager\", \"container\", \"onShow\", \"onHide\", \"onExit\", \"onExited\", \"onExiting\", \"onEnter\", \"onEntering\", \"onEntered\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n/* eslint-disable @typescript-eslint/no-use-before-define, react/prop-types */\n\nimport activeElement from 'dom-helpers/activeElement';\nimport contains from 'dom-helpers/contains';\nimport canUseDOM from 'dom-helpers/canUseDOM';\nimport listen from 'dom-helpers/listen';\nimport { useState, useRef, useCallback, useImperativeHandle, forwardRef, useEffect } from 'react';\nimport * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport useMounted from '@restart/hooks/useMounted';\nimport useWillUnmount from '@restart/hooks/useWillUnmount';\nimport usePrevious from '@restart/hooks/usePrevious';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport ModalManager from './ModalManager';\nimport useWaitForDOMRef from './useWaitForDOMRef';\nimport useWindow from './useWindow';\nimport { renderTransition } from './ImperativeTransition';\nimport { isEscKey } from './utils';\nimport { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nlet manager;\n\n/*\n  Modal props are split into a version with and without index signature so that you can fully use them in another projects\n  This is due to Typescript not playing well with index signatures e.g. when using Omit\n*/\n\nfunction getManager(window) {\n  if (!manager) manager = new ModalManager({\n    ownerDocument: window == null ? void 0 : window.document\n  });\n  return manager;\n}\nfunction useModalManager(provided) {\n  const window = useWindow();\n  const modalManager = provided || getManager(window);\n  const modal = useRef({\n    dialog: null,\n    backdrop: null\n  });\n  return Object.assign(modal.current, {\n    add: () => modalManager.add(modal.current),\n    remove: () => modalManager.remove(modal.current),\n    isTopModal: () => modalManager.isTopModal(modal.current),\n    setDialogRef: useCallback(ref => {\n      modal.current.dialog = ref;\n    }, []),\n    setBackdropRef: useCallback(ref => {\n      modal.current.backdrop = ref;\n    }, [])\n  });\n}\nconst Modal = /*#__PURE__*/forwardRef((_ref, ref) => {\n  let {\n      show = false,\n      role = 'dialog',\n      className,\n      style,\n      children,\n      backdrop = true,\n      keyboard = true,\n      onBackdropClick,\n      onEscapeKeyDown,\n      transition,\n      runTransition,\n      backdropTransition,\n      runBackdropTransition,\n      autoFocus = true,\n      enforceFocus = true,\n      restoreFocus = true,\n      restoreFocusOptions,\n      renderDialog,\n      renderBackdrop = props => /*#__PURE__*/_jsx(\"div\", Object.assign({}, props)),\n      manager: providedManager,\n      container: containerRef,\n      onShow,\n      onHide = () => {},\n      onExit,\n      onExited,\n      onExiting,\n      onEnter,\n      onEntering,\n      onEntered\n    } = _ref,\n    rest = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const ownerWindow = useWindow();\n  const container = useWaitForDOMRef(containerRef);\n  const modal = useModalManager(providedManager);\n  const isMounted = useMounted();\n  const prevShow = usePrevious(show);\n  const [exited, setExited] = useState(!show);\n  const lastFocusRef = useRef(null);\n  useImperativeHandle(ref, () => modal, [modal]);\n  if (canUseDOM && !prevShow && show) {\n    lastFocusRef.current = activeElement(ownerWindow == null ? void 0 : ownerWindow.document);\n  }\n\n  // TODO: I think this needs to be in an effect\n  if (show && exited) {\n    setExited(false);\n  }\n  const handleShow = useEventCallback(() => {\n    modal.add();\n    removeKeydownListenerRef.current = listen(document, 'keydown', handleDocumentKeyDown);\n    removeFocusListenerRef.current = listen(document, 'focus',\n    // the timeout is necessary b/c this will run before the new modal is mounted\n    // and so steals focus from it\n    () => setTimeout(handleEnforceFocus), true);\n    if (onShow) {\n      onShow();\n    }\n\n    // autofocus after onShow to not trigger a focus event for previous\n    // modals before this one is shown.\n    if (autoFocus) {\n      var _modal$dialog$ownerDo, _modal$dialog;\n      const currentActiveElement = activeElement((_modal$dialog$ownerDo = (_modal$dialog = modal.dialog) == null ? void 0 : _modal$dialog.ownerDocument) != null ? _modal$dialog$ownerDo : ownerWindow == null ? void 0 : ownerWindow.document);\n      if (modal.dialog && currentActiveElement && !contains(modal.dialog, currentActiveElement)) {\n        lastFocusRef.current = currentActiveElement;\n        modal.dialog.focus();\n      }\n    }\n  });\n  const handleHide = useEventCallback(() => {\n    modal.remove();\n    removeKeydownListenerRef.current == null ? void 0 : removeKeydownListenerRef.current();\n    removeFocusListenerRef.current == null ? void 0 : removeFocusListenerRef.current();\n    if (restoreFocus) {\n      var _lastFocusRef$current;\n      // Support: <=IE11 doesn't support `focus()` on svg elements (RB: #917)\n      (_lastFocusRef$current = lastFocusRef.current) == null ? void 0 : _lastFocusRef$current.focus == null ? void 0 : _lastFocusRef$current.focus(restoreFocusOptions);\n      lastFocusRef.current = null;\n    }\n  });\n\n  // TODO: try and combine these effects: https://github.com/react-bootstrap/react-overlays/pull/794#discussion_r409954120\n\n  // Show logic when:\n  //  - show is `true` _and_ `container` has resolved\n  useEffect(() => {\n    if (!show || !container) return;\n    handleShow();\n  }, [show, container, /* should never change: */handleShow]);\n\n  // Hide cleanup logic when:\n  //  - `exited` switches to true\n  //  - component unmounts;\n  useEffect(() => {\n    if (!exited) return;\n    handleHide();\n  }, [exited, handleHide]);\n  useWillUnmount(() => {\n    handleHide();\n  });\n\n  // --------------------------------\n\n  const handleEnforceFocus = useEventCallback(() => {\n    if (!enforceFocus || !isMounted() || !modal.isTopModal()) {\n      return;\n    }\n    const currentActiveElement = activeElement(ownerWindow == null ? void 0 : ownerWindow.document);\n    if (modal.dialog && currentActiveElement && !contains(modal.dialog, currentActiveElement)) {\n      modal.dialog.focus();\n    }\n  });\n  const handleBackdropClick = useEventCallback(e => {\n    if (e.target !== e.currentTarget) {\n      return;\n    }\n    onBackdropClick == null ? void 0 : onBackdropClick(e);\n    if (backdrop === true) {\n      onHide();\n    }\n  });\n  const handleDocumentKeyDown = useEventCallback(e => {\n    if (keyboard && isEscKey(e) && modal.isTopModal()) {\n      onEscapeKeyDown == null ? void 0 : onEscapeKeyDown(e);\n      if (!e.defaultPrevented) {\n        onHide();\n      }\n    }\n  });\n  const removeFocusListenerRef = useRef();\n  const removeKeydownListenerRef = useRef();\n  const handleHidden = (...args) => {\n    setExited(true);\n    onExited == null ? void 0 : onExited(...args);\n  };\n  if (!container) {\n    return null;\n  }\n  const dialogProps = Object.assign({\n    role,\n    ref: modal.setDialogRef,\n    // apparently only works on the dialog role element\n    'aria-modal': role === 'dialog' ? true : undefined\n  }, rest, {\n    style,\n    className,\n    tabIndex: -1\n  });\n  let dialog = renderDialog ? renderDialog(dialogProps) : /*#__PURE__*/_jsx(\"div\", Object.assign({}, dialogProps, {\n    children: /*#__PURE__*/React.cloneElement(children, {\n      role: 'document'\n    })\n  }));\n  dialog = renderTransition(transition, runTransition, {\n    unmountOnExit: true,\n    mountOnEnter: true,\n    appear: true,\n    in: !!show,\n    onExit,\n    onExiting,\n    onExited: handleHidden,\n    onEnter,\n    onEntering,\n    onEntered,\n    children: dialog\n  });\n  let backdropElement = null;\n  if (backdrop) {\n    backdropElement = renderBackdrop({\n      ref: modal.setBackdropRef,\n      onClick: handleBackdropClick\n    });\n    backdropElement = renderTransition(backdropTransition, runBackdropTransition, {\n      in: !!show,\n      appear: true,\n      mountOnEnter: true,\n      unmountOnExit: true,\n      children: backdropElement\n    });\n  }\n  return /*#__PURE__*/_jsx(_Fragment, {\n    children: /*#__PURE__*/ReactDOM.createPortal(/*#__PURE__*/_jsxs(_Fragment, {\n      children: [backdropElement, dialog]\n    }), container)\n  });\n});\nModal.displayName = 'Modal';\nexport default Object.assign(Modal, {\n  Manager: ModalManager\n});", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "activeElement", "contains", "canUseDOM", "listen", "useState", "useRef", "useCallback", "useImperativeHandle", "forwardRef", "useEffect", "React", "ReactDOM", "useMounted", "useWillUnmount", "usePrevious", "useEventCallback", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useWaitForDOMRef", "useWindow", "renderTransition", "isEscKey", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "manager", "getManager", "window", "ownerDocument", "document", "useModalManager", "provided", "modalManager", "modal", "dialog", "backdrop", "Object", "assign", "current", "add", "remove", "isTopModal", "setDialogRef", "ref", "setBackdropRef", "Modal", "_ref", "show", "role", "className", "style", "children", "keyboard", "onBackdropClick", "onEscapeKeyDown", "transition", "runTransition", "backdropTransition", "runBackdropTransition", "autoFocus", "enforceFocus", "restoreFocus", "restoreFocusOptions", "renderDialog", "renderBackdrop", "props", "provided<PERSON><PERSON><PERSON>", "container", "containerRef", "onShow", "onHide", "onExit", "onExited", "onExiting", "onEnter", "onEntering", "onEntered", "rest", "ownerWindow", "isMounted", "prevShow", "exited", "setExited", "lastFocusRef", "handleShow", "removeKeydownListenerRef", "handleDocumentKeyDown", "removeFocusListenerRef", "setTimeout", "handleEnforceFocus", "_modal$dialog$ownerDo", "_modal$dialog", "currentActiveElement", "focus", "handleHide", "_lastFocusRef$current", "handleBackdropClick", "target", "currentTarget", "defaultPrevented", "handleHidden", "args", "dialogProps", "undefined", "tabIndex", "cloneElement", "unmountOnExit", "mountOnEnter", "appear", "in", "backdropElement", "onClick", "createPortal", "displayName", "Manager"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/@restart/ui/esm/Modal.js"], "sourcesContent": ["const _excluded = [\"show\", \"role\", \"className\", \"style\", \"children\", \"backdrop\", \"keyboard\", \"onBackdropClick\", \"onEscapeKeyDown\", \"transition\", \"runTransition\", \"backdropTransition\", \"runBackdropTransition\", \"autoFocus\", \"enforceFocus\", \"restoreFocus\", \"restoreFocusOptions\", \"renderDialog\", \"renderBackdrop\", \"manager\", \"container\", \"onShow\", \"onHide\", \"onExit\", \"onExited\", \"onExiting\", \"onEnter\", \"onEntering\", \"onEntered\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\n/* eslint-disable @typescript-eslint/no-use-before-define, react/prop-types */\n\nimport activeElement from 'dom-helpers/activeElement';\nimport contains from 'dom-helpers/contains';\nimport canUseDOM from 'dom-helpers/canUseDOM';\nimport listen from 'dom-helpers/listen';\nimport { useState, useRef, useCallback, useImperativeHandle, forwardRef, useEffect } from 'react';\nimport * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport useMounted from '@restart/hooks/useMounted';\nimport useWillUnmount from '@restart/hooks/useWillUnmount';\nimport usePrevious from '@restart/hooks/usePrevious';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport ModalManager from './ModalManager';\nimport useWaitForDOMRef from './useWaitForDOMRef';\nimport useWindow from './useWindow';\nimport { renderTransition } from './ImperativeTransition';\nimport { isEscKey } from './utils';\nimport { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nlet manager;\n\n/*\n  Modal props are split into a version with and without index signature so that you can fully use them in another projects\n  This is due to Typescript not playing well with index signatures e.g. when using Omit\n*/\n\nfunction getManager(window) {\n  if (!manager) manager = new ModalManager({\n    ownerDocument: window == null ? void 0 : window.document\n  });\n  return manager;\n}\nfunction useModalManager(provided) {\n  const window = useWindow();\n  const modalManager = provided || getManager(window);\n  const modal = useRef({\n    dialog: null,\n    backdrop: null\n  });\n  return Object.assign(modal.current, {\n    add: () => modalManager.add(modal.current),\n    remove: () => modalManager.remove(modal.current),\n    isTopModal: () => modalManager.isTopModal(modal.current),\n    setDialogRef: useCallback(ref => {\n      modal.current.dialog = ref;\n    }, []),\n    setBackdropRef: useCallback(ref => {\n      modal.current.backdrop = ref;\n    }, [])\n  });\n}\nconst Modal = /*#__PURE__*/forwardRef((_ref, ref) => {\n  let {\n      show = false,\n      role = 'dialog',\n      className,\n      style,\n      children,\n      backdrop = true,\n      keyboard = true,\n      onBackdropClick,\n      onEscapeKeyDown,\n      transition,\n      runTransition,\n      backdropTransition,\n      runBackdropTransition,\n      autoFocus = true,\n      enforceFocus = true,\n      restoreFocus = true,\n      restoreFocusOptions,\n      renderDialog,\n      renderBackdrop = props => /*#__PURE__*/_jsx(\"div\", Object.assign({}, props)),\n      manager: providedManager,\n      container: containerRef,\n      onShow,\n      onHide = () => {},\n      onExit,\n      onExited,\n      onExiting,\n      onEnter,\n      onEntering,\n      onEntered\n    } = _ref,\n    rest = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const ownerWindow = useWindow();\n  const container = useWaitForDOMRef(containerRef);\n  const modal = useModalManager(providedManager);\n  const isMounted = useMounted();\n  const prevShow = usePrevious(show);\n  const [exited, setExited] = useState(!show);\n  const lastFocusRef = useRef(null);\n  useImperativeHandle(ref, () => modal, [modal]);\n  if (canUseDOM && !prevShow && show) {\n    lastFocusRef.current = activeElement(ownerWindow == null ? void 0 : ownerWindow.document);\n  }\n\n  // TODO: I think this needs to be in an effect\n  if (show && exited) {\n    setExited(false);\n  }\n  const handleShow = useEventCallback(() => {\n    modal.add();\n    removeKeydownListenerRef.current = listen(document, 'keydown', handleDocumentKeyDown);\n    removeFocusListenerRef.current = listen(document, 'focus',\n    // the timeout is necessary b/c this will run before the new modal is mounted\n    // and so steals focus from it\n    () => setTimeout(handleEnforceFocus), true);\n    if (onShow) {\n      onShow();\n    }\n\n    // autofocus after onShow to not trigger a focus event for previous\n    // modals before this one is shown.\n    if (autoFocus) {\n      var _modal$dialog$ownerDo, _modal$dialog;\n      const currentActiveElement = activeElement((_modal$dialog$ownerDo = (_modal$dialog = modal.dialog) == null ? void 0 : _modal$dialog.ownerDocument) != null ? _modal$dialog$ownerDo : ownerWindow == null ? void 0 : ownerWindow.document);\n      if (modal.dialog && currentActiveElement && !contains(modal.dialog, currentActiveElement)) {\n        lastFocusRef.current = currentActiveElement;\n        modal.dialog.focus();\n      }\n    }\n  });\n  const handleHide = useEventCallback(() => {\n    modal.remove();\n    removeKeydownListenerRef.current == null ? void 0 : removeKeydownListenerRef.current();\n    removeFocusListenerRef.current == null ? void 0 : removeFocusListenerRef.current();\n    if (restoreFocus) {\n      var _lastFocusRef$current;\n      // Support: <=IE11 doesn't support `focus()` on svg elements (RB: #917)\n      (_lastFocusRef$current = lastFocusRef.current) == null ? void 0 : _lastFocusRef$current.focus == null ? void 0 : _lastFocusRef$current.focus(restoreFocusOptions);\n      lastFocusRef.current = null;\n    }\n  });\n\n  // TODO: try and combine these effects: https://github.com/react-bootstrap/react-overlays/pull/794#discussion_r409954120\n\n  // Show logic when:\n  //  - show is `true` _and_ `container` has resolved\n  useEffect(() => {\n    if (!show || !container) return;\n    handleShow();\n  }, [show, container, /* should never change: */handleShow]);\n\n  // Hide cleanup logic when:\n  //  - `exited` switches to true\n  //  - component unmounts;\n  useEffect(() => {\n    if (!exited) return;\n    handleHide();\n  }, [exited, handleHide]);\n  useWillUnmount(() => {\n    handleHide();\n  });\n\n  // --------------------------------\n\n  const handleEnforceFocus = useEventCallback(() => {\n    if (!enforceFocus || !isMounted() || !modal.isTopModal()) {\n      return;\n    }\n    const currentActiveElement = activeElement(ownerWindow == null ? void 0 : ownerWindow.document);\n    if (modal.dialog && currentActiveElement && !contains(modal.dialog, currentActiveElement)) {\n      modal.dialog.focus();\n    }\n  });\n  const handleBackdropClick = useEventCallback(e => {\n    if (e.target !== e.currentTarget) {\n      return;\n    }\n    onBackdropClick == null ? void 0 : onBackdropClick(e);\n    if (backdrop === true) {\n      onHide();\n    }\n  });\n  const handleDocumentKeyDown = useEventCallback(e => {\n    if (keyboard && isEscKey(e) && modal.isTopModal()) {\n      onEscapeKeyDown == null ? void 0 : onEscapeKeyDown(e);\n      if (!e.defaultPrevented) {\n        onHide();\n      }\n    }\n  });\n  const removeFocusListenerRef = useRef();\n  const removeKeydownListenerRef = useRef();\n  const handleHidden = (...args) => {\n    setExited(true);\n    onExited == null ? void 0 : onExited(...args);\n  };\n  if (!container) {\n    return null;\n  }\n  const dialogProps = Object.assign({\n    role,\n    ref: modal.setDialogRef,\n    // apparently only works on the dialog role element\n    'aria-modal': role === 'dialog' ? true : undefined\n  }, rest, {\n    style,\n    className,\n    tabIndex: -1\n  });\n  let dialog = renderDialog ? renderDialog(dialogProps) : /*#__PURE__*/_jsx(\"div\", Object.assign({}, dialogProps, {\n    children: /*#__PURE__*/React.cloneElement(children, {\n      role: 'document'\n    })\n  }));\n  dialog = renderTransition(transition, runTransition, {\n    unmountOnExit: true,\n    mountOnEnter: true,\n    appear: true,\n    in: !!show,\n    onExit,\n    onExiting,\n    onExited: handleHidden,\n    onEnter,\n    onEntering,\n    onEntered,\n    children: dialog\n  });\n  let backdropElement = null;\n  if (backdrop) {\n    backdropElement = renderBackdrop({\n      ref: modal.setBackdropRef,\n      onClick: handleBackdropClick\n    });\n    backdropElement = renderTransition(backdropTransition, runBackdropTransition, {\n      in: !!show,\n      appear: true,\n      mountOnEnter: true,\n      unmountOnExit: true,\n      children: backdropElement\n    });\n  }\n  return /*#__PURE__*/_jsx(_Fragment, {\n    children: /*#__PURE__*/ReactDOM.createPortal( /*#__PURE__*/_jsxs(_Fragment, {\n      children: [backdropElement, dialog]\n    }), container)\n  });\n});\nModal.displayName = 'Modal';\nexport default Object.assign(Modal, {\n  Manager: ModalManager\n});"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,YAAY,EAAE,eAAe,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,qBAAqB,EAAE,cAAc,EAAE,gBAAgB,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,CAAC;AAC3a,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IAAE,IAAIF,CAAC,CAACK,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,EAAE;IAAUD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACpM;;AAEA,OAAOK,aAAa,MAAM,2BAA2B;AACrD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACjG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,QAAQ,QAAQ,SAAS;AAClC,SAASC,GAAG,IAAIC,IAAI,EAAEC,QAAQ,IAAIC,SAAS,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACrF,IAAIC,OAAO;;AAEX;AACA;AACA;AACA;;AAEA,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC1B,IAAI,CAACF,OAAO,EAAEA,OAAO,GAAG,IAAIX,YAAY,CAAC;IACvCc,aAAa,EAAED,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE;EAClD,CAAC,CAAC;EACF,OAAOJ,OAAO;AAChB;AACA,SAASK,eAAeA,CAACC,QAAQ,EAAE;EACjC,MAAMJ,MAAM,GAAGX,SAAS,CAAC,CAAC;EAC1B,MAAMgB,YAAY,GAAGD,QAAQ,IAAIL,UAAU,CAACC,MAAM,CAAC;EACnD,MAAMM,KAAK,GAAG9B,MAAM,CAAC;IACnB+B,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,OAAOC,MAAM,CAACC,MAAM,CAACJ,KAAK,CAACK,OAAO,EAAE;IAClCC,GAAG,EAAEA,CAAA,KAAMP,YAAY,CAACO,GAAG,CAACN,KAAK,CAACK,OAAO,CAAC;IAC1CE,MAAM,EAAEA,CAAA,KAAMR,YAAY,CAACQ,MAAM,CAACP,KAAK,CAACK,OAAO,CAAC;IAChDG,UAAU,EAAEA,CAAA,KAAMT,YAAY,CAACS,UAAU,CAACR,KAAK,CAACK,OAAO,CAAC;IACxDI,YAAY,EAAEtC,WAAW,CAACuC,GAAG,IAAI;MAC/BV,KAAK,CAACK,OAAO,CAACJ,MAAM,GAAGS,GAAG;IAC5B,CAAC,EAAE,EAAE,CAAC;IACNC,cAAc,EAAExC,WAAW,CAACuC,GAAG,IAAI;MACjCV,KAAK,CAACK,OAAO,CAACH,QAAQ,GAAGQ,GAAG;IAC9B,CAAC,EAAE,EAAE;EACP,CAAC,CAAC;AACJ;AACA,MAAME,KAAK,GAAG,aAAavC,UAAU,CAAC,CAACwC,IAAI,EAAEH,GAAG,KAAK;EACnD,IAAI;MACAI,IAAI,GAAG,KAAK;MACZC,IAAI,GAAG,QAAQ;MACfC,SAAS;MACTC,KAAK;MACLC,QAAQ;MACRhB,QAAQ,GAAG,IAAI;MACfiB,QAAQ,GAAG,IAAI;MACfC,eAAe;MACfC,eAAe;MACfC,UAAU;MACVC,aAAa;MACbC,kBAAkB;MAClBC,qBAAqB;MACrBC,SAAS,GAAG,IAAI;MAChBC,YAAY,GAAG,IAAI;MACnBC,YAAY,GAAG,IAAI;MACnBC,mBAAmB;MACnBC,YAAY;MACZC,cAAc,GAAGC,KAAK,IAAI,aAAa7C,IAAI,CAAC,KAAK,EAAEgB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4B,KAAK,CAAC,CAAC;MAC5ExC,OAAO,EAAEyC,eAAe;MACxBC,SAAS,EAAEC,YAAY;MACvBC,MAAM;MACNC,MAAM,GAAGA,CAAA,KAAM,CAAC,CAAC;MACjBC,MAAM;MACNC,QAAQ;MACRC,SAAS;MACTC,OAAO;MACPC,UAAU;MACVC;IACF,CAAC,GAAG9B,IAAI;IACR+B,IAAI,GAAGvF,6BAA6B,CAACwD,IAAI,EAAEzD,SAAS,CAAC;EACvD,MAAMyF,WAAW,GAAG9D,SAAS,CAAC,CAAC;EAC/B,MAAMmD,SAAS,GAAGpD,gBAAgB,CAACqD,YAAY,CAAC;EAChD,MAAMnC,KAAK,GAAGH,eAAe,CAACoC,eAAe,CAAC;EAC9C,MAAMa,SAAS,GAAGrE,UAAU,CAAC,CAAC;EAC9B,MAAMsE,QAAQ,GAAGpE,WAAW,CAACmC,IAAI,CAAC;EAClC,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGhF,QAAQ,CAAC,CAAC6C,IAAI,CAAC;EAC3C,MAAMoC,YAAY,GAAGhF,MAAM,CAAC,IAAI,CAAC;EACjCE,mBAAmB,CAACsC,GAAG,EAAE,MAAMV,KAAK,EAAE,CAACA,KAAK,CAAC,CAAC;EAC9C,IAAIjC,SAAS,IAAI,CAACgF,QAAQ,IAAIjC,IAAI,EAAE;IAClCoC,YAAY,CAAC7C,OAAO,GAAGxC,aAAa,CAACgF,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACjD,QAAQ,CAAC;EAC3F;;EAEA;EACA,IAAIkB,IAAI,IAAIkC,MAAM,EAAE;IAClBC,SAAS,CAAC,KAAK,CAAC;EAClB;EACA,MAAME,UAAU,GAAGvE,gBAAgB,CAAC,MAAM;IACxCoB,KAAK,CAACM,GAAG,CAAC,CAAC;IACX8C,wBAAwB,CAAC/C,OAAO,GAAGrC,MAAM,CAAC4B,QAAQ,EAAE,SAAS,EAAEyD,qBAAqB,CAAC;IACrFC,sBAAsB,CAACjD,OAAO,GAAGrC,MAAM,CAAC4B,QAAQ,EAAE,OAAO;IACzD;IACA;IACA,MAAM2D,UAAU,CAACC,kBAAkB,CAAC,EAAE,IAAI,CAAC;IAC3C,IAAIpB,MAAM,EAAE;MACVA,MAAM,CAAC,CAAC;IACV;;IAEA;IACA;IACA,IAAIV,SAAS,EAAE;MACb,IAAI+B,qBAAqB,EAAEC,aAAa;MACxC,MAAMC,oBAAoB,GAAG9F,aAAa,CAAC,CAAC4F,qBAAqB,GAAG,CAACC,aAAa,GAAG1D,KAAK,CAACC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyD,aAAa,CAAC/D,aAAa,KAAK,IAAI,GAAG8D,qBAAqB,GAAGZ,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACjD,QAAQ,CAAC;MACzO,IAAII,KAAK,CAACC,MAAM,IAAI0D,oBAAoB,IAAI,CAAC7F,QAAQ,CAACkC,KAAK,CAACC,MAAM,EAAE0D,oBAAoB,CAAC,EAAE;QACzFT,YAAY,CAAC7C,OAAO,GAAGsD,oBAAoB;QAC3C3D,KAAK,CAACC,MAAM,CAAC2D,KAAK,CAAC,CAAC;MACtB;IACF;EACF,CAAC,CAAC;EACF,MAAMC,UAAU,GAAGjF,gBAAgB,CAAC,MAAM;IACxCoB,KAAK,CAACO,MAAM,CAAC,CAAC;IACd6C,wBAAwB,CAAC/C,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG+C,wBAAwB,CAAC/C,OAAO,CAAC,CAAC;IACtFiD,sBAAsB,CAACjD,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGiD,sBAAsB,CAACjD,OAAO,CAAC,CAAC;IAClF,IAAIuB,YAAY,EAAE;MAChB,IAAIkC,qBAAqB;MACzB;MACA,CAACA,qBAAqB,GAAGZ,YAAY,CAAC7C,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyD,qBAAqB,CAACF,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGE,qBAAqB,CAACF,KAAK,CAAC/B,mBAAmB,CAAC;MACjKqB,YAAY,CAAC7C,OAAO,GAAG,IAAI;IAC7B;EACF,CAAC,CAAC;;EAEF;;EAEA;EACA;EACA/B,SAAS,CAAC,MAAM;IACd,IAAI,CAACwC,IAAI,IAAI,CAACoB,SAAS,EAAE;IACzBiB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACrC,IAAI,EAAEoB,SAAS,EAAE,0BAA0BiB,UAAU,CAAC,CAAC;;EAE3D;EACA;EACA;EACA7E,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0E,MAAM,EAAE;IACba,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACb,MAAM,EAAEa,UAAU,CAAC,CAAC;EACxBnF,cAAc,CAAC,MAAM;IACnBmF,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;;EAEF;;EAEA,MAAML,kBAAkB,GAAG5E,gBAAgB,CAAC,MAAM;IAChD,IAAI,CAAC+C,YAAY,IAAI,CAACmB,SAAS,CAAC,CAAC,IAAI,CAAC9C,KAAK,CAACQ,UAAU,CAAC,CAAC,EAAE;MACxD;IACF;IACA,MAAMmD,oBAAoB,GAAG9F,aAAa,CAACgF,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACjD,QAAQ,CAAC;IAC/F,IAAII,KAAK,CAACC,MAAM,IAAI0D,oBAAoB,IAAI,CAAC7F,QAAQ,CAACkC,KAAK,CAACC,MAAM,EAAE0D,oBAAoB,CAAC,EAAE;MACzF3D,KAAK,CAACC,MAAM,CAAC2D,KAAK,CAAC,CAAC;IACtB;EACF,CAAC,CAAC;EACF,MAAMG,mBAAmB,GAAGnF,gBAAgB,CAACrB,CAAC,IAAI;IAChD,IAAIA,CAAC,CAACyG,MAAM,KAAKzG,CAAC,CAAC0G,aAAa,EAAE;MAChC;IACF;IACA7C,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC7D,CAAC,CAAC;IACrD,IAAI2C,QAAQ,KAAK,IAAI,EAAE;MACrBmC,MAAM,CAAC,CAAC;IACV;EACF,CAAC,CAAC;EACF,MAAMgB,qBAAqB,GAAGzE,gBAAgB,CAACrB,CAAC,IAAI;IAClD,IAAI4D,QAAQ,IAAIlC,QAAQ,CAAC1B,CAAC,CAAC,IAAIyC,KAAK,CAACQ,UAAU,CAAC,CAAC,EAAE;MACjDa,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC9D,CAAC,CAAC;MACrD,IAAI,CAACA,CAAC,CAAC2G,gBAAgB,EAAE;QACvB7B,MAAM,CAAC,CAAC;MACV;IACF;EACF,CAAC,CAAC;EACF,MAAMiB,sBAAsB,GAAGpF,MAAM,CAAC,CAAC;EACvC,MAAMkF,wBAAwB,GAAGlF,MAAM,CAAC,CAAC;EACzC,MAAMiG,YAAY,GAAGA,CAAC,GAAGC,IAAI,KAAK;IAChCnB,SAAS,CAAC,IAAI,CAAC;IACfV,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,GAAG6B,IAAI,CAAC;EAC/C,CAAC;EACD,IAAI,CAAClC,SAAS,EAAE;IACd,OAAO,IAAI;EACb;EACA,MAAMmC,WAAW,GAAGlE,MAAM,CAACC,MAAM,CAAC;IAChCW,IAAI;IACJL,GAAG,EAAEV,KAAK,CAACS,YAAY;IACvB;IACA,YAAY,EAAEM,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAGuD;EAC3C,CAAC,EAAE1B,IAAI,EAAE;IACP3B,KAAK;IACLD,SAAS;IACTuD,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC;EACF,IAAItE,MAAM,GAAG6B,YAAY,GAAGA,YAAY,CAACuC,WAAW,CAAC,GAAG,aAAalF,IAAI,CAAC,KAAK,EAAEgB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiE,WAAW,EAAE;IAC9GnD,QAAQ,EAAE,aAAa3C,KAAK,CAACiG,YAAY,CAACtD,QAAQ,EAAE;MAClDH,IAAI,EAAE;IACR,CAAC;EACH,CAAC,CAAC,CAAC;EACHd,MAAM,GAAGjB,gBAAgB,CAACsC,UAAU,EAAEC,aAAa,EAAE;IACnDkD,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,IAAI;IAClBC,MAAM,EAAE,IAAI;IACZC,EAAE,EAAE,CAAC,CAAC9D,IAAI;IACVwB,MAAM;IACNE,SAAS;IACTD,QAAQ,EAAE4B,YAAY;IACtB1B,OAAO;IACPC,UAAU;IACVC,SAAS;IACTzB,QAAQ,EAAEjB;EACZ,CAAC,CAAC;EACF,IAAI4E,eAAe,GAAG,IAAI;EAC1B,IAAI3E,QAAQ,EAAE;IACZ2E,eAAe,GAAG9C,cAAc,CAAC;MAC/BrB,GAAG,EAAEV,KAAK,CAACW,cAAc;MACzBmE,OAAO,EAAEf;IACX,CAAC,CAAC;IACFc,eAAe,GAAG7F,gBAAgB,CAACwC,kBAAkB,EAAEC,qBAAqB,EAAE;MAC5EmD,EAAE,EAAE,CAAC,CAAC9D,IAAI;MACV6D,MAAM,EAAE,IAAI;MACZD,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAE,IAAI;MACnBvD,QAAQ,EAAE2D;IACZ,CAAC,CAAC;EACJ;EACA,OAAO,aAAa1F,IAAI,CAACE,SAAS,EAAE;IAClC6B,QAAQ,EAAE,aAAa1C,QAAQ,CAACuG,YAAY,CAAE,aAAaxF,KAAK,CAACF,SAAS,EAAE;MAC1E6B,QAAQ,EAAE,CAAC2D,eAAe,EAAE5E,MAAM;IACpC,CAAC,CAAC,EAAEiC,SAAS;EACf,CAAC,CAAC;AACJ,CAAC,CAAC;AACFtB,KAAK,CAACoE,WAAW,GAAG,OAAO;AAC3B,eAAe7E,MAAM,CAACC,MAAM,CAACQ,KAAK,EAAE;EAClCqE,OAAO,EAAEpG;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}