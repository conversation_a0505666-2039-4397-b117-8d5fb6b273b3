{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionButton from './AccordionButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AccordionHeader = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'h2',\n  'aria-controls': ariaControls,\n  bsPrefix,\n  className,\n  children,\n  onClick,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-header');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix),\n    children: /*#__PURE__*/_jsx(AccordionButton, {\n      onClick: onClick,\n      \"aria-controls\": ariaControls,\n      children: children\n    })\n  });\n});\nAccordionHeader.displayName = 'AccordionHeader';\nexport default AccordionHeader;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "Accordion<PERSON><PERSON><PERSON>", "jsx", "_jsx", "Accordi<PERSON><PERSON><PERSON><PERSON>", "forwardRef", "as", "Component", "ariaControls", "bsPrefix", "className", "children", "onClick", "props", "ref", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-bootstrap/esm/AccordionHeader.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionButton from './AccordionButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AccordionHeader = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'h2',\n  'aria-controls': ariaControls,\n  bsPrefix,\n  className,\n  children,\n  onClick,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-header');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix),\n    children: /*#__PURE__*/_jsx(AccordionButton, {\n      onClick: onClick,\n      \"aria-controls\": ariaControls,\n      children: children\n    })\n  });\n});\nAccordionHeader.displayName = 'AccordionHeader';\nexport default AccordionHeader;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,eAAe,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EACrD;EACAC,EAAE,EAAEC,SAAS,GAAG,IAAI;EACpB,eAAe,EAAEC,YAAY;EAC7BC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACRC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTL,QAAQ,GAAGT,kBAAkB,CAACS,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaN,IAAI,CAACI,SAAS,EAAE;IAClCO,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRH,SAAS,EAAEZ,UAAU,CAACY,SAAS,EAAED,QAAQ,CAAC;IAC1CE,QAAQ,EAAE,aAAaR,IAAI,CAACF,eAAe,EAAE;MAC3CW,OAAO,EAAEA,OAAO;MAChB,eAAe,EAAEJ,YAAY;MAC7BG,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,eAAe,CAACW,WAAW,GAAG,iBAAiB;AAC/C,eAAeX,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}