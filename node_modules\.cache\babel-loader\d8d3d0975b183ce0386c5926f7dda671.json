{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport resolvePathname from 'resolve-pathname';\nimport valueEqual from 'value-equal';\nimport warning from 'tiny-warning';\nimport invariant from 'tiny-invariant';\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === '/' ? path : '/' + path;\n}\nfunction stripLeadingSlash(path) {\n  return path.charAt(0) === '/' ? path.substr(1) : path;\n}\nfunction hasBasename(path, prefix) {\n  return path.toLowerCase().indexOf(prefix.toLowerCase()) === 0 && '/?#'.indexOf(path.charAt(prefix.length)) !== -1;\n}\nfunction stripBasename(path, prefix) {\n  return hasBasename(path, prefix) ? path.substr(prefix.length) : path;\n}\nfunction stripTrailingSlash(path) {\n  return path.charAt(path.length - 1) === '/' ? path.slice(0, -1) : path;\n}\nfunction parsePath(path) {\n  var pathname = path || '/';\n  var search = '';\n  var hash = '';\n  var hashIndex = pathname.indexOf('#');\n  if (hashIndex !== -1) {\n    hash = pathname.substr(hashIndex);\n    pathname = pathname.substr(0, hashIndex);\n  }\n  var searchIndex = pathname.indexOf('?');\n  if (searchIndex !== -1) {\n    search = pathname.substr(searchIndex);\n    pathname = pathname.substr(0, searchIndex);\n  }\n  return {\n    pathname: pathname,\n    search: search === '?' ? '' : search,\n    hash: hash === '#' ? '' : hash\n  };\n}\nfunction createPath(location) {\n  var pathname = location.pathname,\n    search = location.search,\n    hash = location.hash;\n  var path = pathname || '/';\n  if (search && search !== '?') path += search.charAt(0) === '?' ? search : \"?\" + search;\n  if (hash && hash !== '#') path += hash.charAt(0) === '#' ? hash : \"#\" + hash;\n  return path;\n}\nfunction createLocation(path, state, key, currentLocation) {\n  var location;\n  if (typeof path === 'string') {\n    // Two-arg form: push(path, state)\n    location = parsePath(path);\n    location.state = state;\n  } else {\n    // One-arg form: push(location)\n    location = _extends({}, path);\n    if (location.pathname === undefined) location.pathname = '';\n    if (location.search) {\n      if (location.search.charAt(0) !== '?') location.search = '?' + location.search;\n    } else {\n      location.search = '';\n    }\n    if (location.hash) {\n      if (location.hash.charAt(0) !== '#') location.hash = '#' + location.hash;\n    } else {\n      location.hash = '';\n    }\n    if (state !== undefined && location.state === undefined) location.state = state;\n  }\n  try {\n    location.pathname = decodeURI(location.pathname);\n  } catch (e) {\n    if (e instanceof URIError) {\n      throw new URIError('Pathname \"' + location.pathname + '\" could not be decoded. ' + 'This is likely caused by an invalid percent-encoding.');\n    } else {\n      throw e;\n    }\n  }\n  if (key) location.key = key;\n  if (currentLocation) {\n    // Resolve incomplete/relative pathname relative to current location.\n    if (!location.pathname) {\n      location.pathname = currentLocation.pathname;\n    } else if (location.pathname.charAt(0) !== '/') {\n      location.pathname = resolvePathname(location.pathname, currentLocation.pathname);\n    }\n  } else {\n    // When there is no prior location and pathname is empty, set it to /\n    if (!location.pathname) {\n      location.pathname = '/';\n    }\n  }\n  return location;\n}\nfunction locationsAreEqual(a, b) {\n  return a.pathname === b.pathname && a.search === b.search && a.hash === b.hash && a.key === b.key && valueEqual(a.state, b.state);\n}\nfunction createTransitionManager() {\n  var prompt = null;\n  function setPrompt(nextPrompt) {\n    process.env.NODE_ENV !== \"production\" ? warning(prompt == null, 'A history supports only one prompt at a time') : void 0;\n    prompt = nextPrompt;\n    return function () {\n      if (prompt === nextPrompt) prompt = null;\n    };\n  }\n  function confirmTransitionTo(location, action, getUserConfirmation, callback) {\n    // TODO: If another transition starts while we're still confirming\n    // the previous one, we may end up in a weird state. Figure out the\n    // best way to handle this.\n    if (prompt != null) {\n      var result = typeof prompt === 'function' ? prompt(location, action) : prompt;\n      if (typeof result === 'string') {\n        if (typeof getUserConfirmation === 'function') {\n          getUserConfirmation(result, callback);\n        } else {\n          process.env.NODE_ENV !== \"production\" ? warning(false, 'A history needs a getUserConfirmation function in order to use a prompt message') : void 0;\n          callback(true);\n        }\n      } else {\n        // Return false from a transition hook to cancel the transition.\n        callback(result !== false);\n      }\n    } else {\n      callback(true);\n    }\n  }\n  var listeners = [];\n  function appendListener(fn) {\n    var isActive = true;\n    function listener() {\n      if (isActive) fn.apply(void 0, arguments);\n    }\n    listeners.push(listener);\n    return function () {\n      isActive = false;\n      listeners = listeners.filter(function (item) {\n        return item !== listener;\n      });\n    };\n  }\n  function notifyListeners() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    listeners.forEach(function (listener) {\n      return listener.apply(void 0, args);\n    });\n  }\n  return {\n    setPrompt: setPrompt,\n    confirmTransitionTo: confirmTransitionTo,\n    appendListener: appendListener,\n    notifyListeners: notifyListeners\n  };\n}\nvar canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nfunction getConfirmation(message, callback) {\n  callback(window.confirm(message)); // eslint-disable-line no-alert\n}\n/**\n * Returns true if the HTML5 history API is supported. Taken from Modernizr.\n *\n * https://github.com/Modernizr/Modernizr/blob/master/LICENSE\n * https://github.com/Modernizr/Modernizr/blob/master/feature-detects/history.js\n * changed to avoid false negatives for Windows Phones: https://github.com/reactjs/react-router/issues/586\n */\n\nfunction supportsHistory() {\n  var ua = window.navigator.userAgent;\n  if ((ua.indexOf('Android 2.') !== -1 || ua.indexOf('Android 4.0') !== -1) && ua.indexOf('Mobile Safari') !== -1 && ua.indexOf('Chrome') === -1 && ua.indexOf('Windows Phone') === -1) return false;\n  return window.history && 'pushState' in window.history;\n}\n/**\n * Returns true if browser fires popstate on hash change.\n * IE10 and IE11 do not.\n */\n\nfunction supportsPopStateOnHashChange() {\n  return window.navigator.userAgent.indexOf('Trident') === -1;\n}\n/**\n * Returns false if using go(n) with hash history causes a full page reload.\n */\n\nfunction supportsGoWithoutReloadUsingHash() {\n  return window.navigator.userAgent.indexOf('Firefox') === -1;\n}\n/**\n * Returns true if a given popstate event is an extraneous WebKit event.\n * Accounts for the fact that Chrome on iOS fires real popstate events\n * containing undefined state when pressing the back button.\n */\n\nfunction isExtraneousPopstateEvent(event) {\n  return event.state === undefined && navigator.userAgent.indexOf('CriOS') === -1;\n}\nvar PopStateEvent = 'popstate';\nvar HashChangeEvent = 'hashchange';\nfunction getHistoryState() {\n  try {\n    return window.history.state || {};\n  } catch (e) {\n    // IE 11 sometimes throws when accessing window.history.state\n    // See https://github.com/ReactTraining/history/pull/289\n    return {};\n  }\n}\n/**\n * Creates a history object that uses the HTML5 history API including\n * pushState, replaceState, and the popstate event.\n */\n\nfunction createBrowserHistory(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  !canUseDOM ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Browser history needs a DOM') : invariant(false) : void 0;\n  var globalHistory = window.history;\n  var canUseHistory = supportsHistory();\n  var needsHashChangeListener = !supportsPopStateOnHashChange();\n  var _props = props,\n    _props$forceRefresh = _props.forceRefresh,\n    forceRefresh = _props$forceRefresh === void 0 ? false : _props$forceRefresh,\n    _props$getUserConfirm = _props.getUserConfirmation,\n    getUserConfirmation = _props$getUserConfirm === void 0 ? getConfirmation : _props$getUserConfirm,\n    _props$keyLength = _props.keyLength,\n    keyLength = _props$keyLength === void 0 ? 6 : _props$keyLength;\n  var basename = props.basename ? stripTrailingSlash(addLeadingSlash(props.basename)) : '';\n  function getDOMLocation(historyState) {\n    var _ref = historyState || {},\n      key = _ref.key,\n      state = _ref.state;\n    var _window$location = window.location,\n      pathname = _window$location.pathname,\n      search = _window$location.search,\n      hash = _window$location.hash;\n    var path = pathname + search + hash;\n    process.env.NODE_ENV !== \"production\" ? warning(!basename || hasBasename(path, basename), 'You are attempting to use a basename on a page whose URL path does not begin ' + 'with the basename. Expected path \"' + path + '\" to begin with \"' + basename + '\".') : void 0;\n    if (basename) path = stripBasename(path, basename);\n    return createLocation(path, state, key);\n  }\n  function createKey() {\n    return Math.random().toString(36).substr(2, keyLength);\n  }\n  var transitionManager = createTransitionManager();\n  function setState(nextState) {\n    _extends(history, nextState);\n    history.length = globalHistory.length;\n    transitionManager.notifyListeners(history.location, history.action);\n  }\n  function handlePopState(event) {\n    // Ignore extraneous popstate events in WebKit.\n    if (isExtraneousPopstateEvent(event)) return;\n    handlePop(getDOMLocation(event.state));\n  }\n  function handleHashChange() {\n    handlePop(getDOMLocation(getHistoryState()));\n  }\n  var forceNextPop = false;\n  function handlePop(location) {\n    if (forceNextPop) {\n      forceNextPop = false;\n      setState();\n    } else {\n      var action = 'POP';\n      transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n        if (ok) {\n          setState({\n            action: action,\n            location: location\n          });\n        } else {\n          revertPop(location);\n        }\n      });\n    }\n  }\n  function revertPop(fromLocation) {\n    var toLocation = history.location; // TODO: We could probably make this more reliable by\n    // keeping a list of keys we've seen in sessionStorage.\n    // Instead, we just default to 0 for keys we don't know.\n\n    var toIndex = allKeys.indexOf(toLocation.key);\n    if (toIndex === -1) toIndex = 0;\n    var fromIndex = allKeys.indexOf(fromLocation.key);\n    if (fromIndex === -1) fromIndex = 0;\n    var delta = toIndex - fromIndex;\n    if (delta) {\n      forceNextPop = true;\n      go(delta);\n    }\n  }\n  var initialLocation = getDOMLocation(getHistoryState());\n  var allKeys = [initialLocation.key]; // Public interface\n\n  function createHref(location) {\n    return basename + createPath(location);\n  }\n  function push(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to push when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'PUSH';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var href = createHref(location);\n      var key = location.key,\n        state = location.state;\n      if (canUseHistory) {\n        globalHistory.pushState({\n          key: key,\n          state: state\n        }, null, href);\n        if (forceRefresh) {\n          window.location.href = href;\n        } else {\n          var prevIndex = allKeys.indexOf(history.location.key);\n          var nextKeys = allKeys.slice(0, prevIndex + 1);\n          nextKeys.push(location.key);\n          allKeys = nextKeys;\n          setState({\n            action: action,\n            location: location\n          });\n        }\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Browser history cannot push state in browsers that do not support HTML5 history') : void 0;\n        window.location.href = href;\n      }\n    });\n  }\n  function replace(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to replace when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'REPLACE';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var href = createHref(location);\n      var key = location.key,\n        state = location.state;\n      if (canUseHistory) {\n        globalHistory.replaceState({\n          key: key,\n          state: state\n        }, null, href);\n        if (forceRefresh) {\n          window.location.replace(href);\n        } else {\n          var prevIndex = allKeys.indexOf(history.location.key);\n          if (prevIndex !== -1) allKeys[prevIndex] = location.key;\n          setState({\n            action: action,\n            location: location\n          });\n        }\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Browser history cannot replace state in browsers that do not support HTML5 history') : void 0;\n        window.location.replace(href);\n      }\n    });\n  }\n  function go(n) {\n    globalHistory.go(n);\n  }\n  function goBack() {\n    go(-1);\n  }\n  function goForward() {\n    go(1);\n  }\n  var listenerCount = 0;\n  function checkDOMListeners(delta) {\n    listenerCount += delta;\n    if (listenerCount === 1 && delta === 1) {\n      window.addEventListener(PopStateEvent, handlePopState);\n      if (needsHashChangeListener) window.addEventListener(HashChangeEvent, handleHashChange);\n    } else if (listenerCount === 0) {\n      window.removeEventListener(PopStateEvent, handlePopState);\n      if (needsHashChangeListener) window.removeEventListener(HashChangeEvent, handleHashChange);\n    }\n  }\n  var isBlocked = false;\n  function block(prompt) {\n    if (prompt === void 0) {\n      prompt = false;\n    }\n    var unblock = transitionManager.setPrompt(prompt);\n    if (!isBlocked) {\n      checkDOMListeners(1);\n      isBlocked = true;\n    }\n    return function () {\n      if (isBlocked) {\n        isBlocked = false;\n        checkDOMListeners(-1);\n      }\n      return unblock();\n    };\n  }\n  function listen(listener) {\n    var unlisten = transitionManager.appendListener(listener);\n    checkDOMListeners(1);\n    return function () {\n      checkDOMListeners(-1);\n      unlisten();\n    };\n  }\n  var history = {\n    length: globalHistory.length,\n    action: 'POP',\n    location: initialLocation,\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    goBack: goBack,\n    goForward: goForward,\n    block: block,\n    listen: listen\n  };\n  return history;\n}\nvar HashChangeEvent$1 = 'hashchange';\nvar HashPathCoders = {\n  hashbang: {\n    encodePath: function encodePath(path) {\n      return path.charAt(0) === '!' ? path : '!/' + stripLeadingSlash(path);\n    },\n    decodePath: function decodePath(path) {\n      return path.charAt(0) === '!' ? path.substr(1) : path;\n    }\n  },\n  noslash: {\n    encodePath: stripLeadingSlash,\n    decodePath: addLeadingSlash\n  },\n  slash: {\n    encodePath: addLeadingSlash,\n    decodePath: addLeadingSlash\n  }\n};\nfunction stripHash(url) {\n  var hashIndex = url.indexOf('#');\n  return hashIndex === -1 ? url : url.slice(0, hashIndex);\n}\nfunction getHashPath() {\n  // We can't use window.location.hash here because it's not\n  // consistent across browsers - Firefox will pre-decode it!\n  var href = window.location.href;\n  var hashIndex = href.indexOf('#');\n  return hashIndex === -1 ? '' : href.substring(hashIndex + 1);\n}\nfunction pushHashPath(path) {\n  window.location.hash = path;\n}\nfunction replaceHashPath(path) {\n  window.location.replace(stripHash(window.location.href) + '#' + path);\n}\nfunction createHashHistory(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  !canUseDOM ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Hash history needs a DOM') : invariant(false) : void 0;\n  var globalHistory = window.history;\n  var canGoWithoutReload = supportsGoWithoutReloadUsingHash();\n  var _props = props,\n    _props$getUserConfirm = _props.getUserConfirmation,\n    getUserConfirmation = _props$getUserConfirm === void 0 ? getConfirmation : _props$getUserConfirm,\n    _props$hashType = _props.hashType,\n    hashType = _props$hashType === void 0 ? 'slash' : _props$hashType;\n  var basename = props.basename ? stripTrailingSlash(addLeadingSlash(props.basename)) : '';\n  var _HashPathCoders$hashT = HashPathCoders[hashType],\n    encodePath = _HashPathCoders$hashT.encodePath,\n    decodePath = _HashPathCoders$hashT.decodePath;\n  function getDOMLocation() {\n    var path = decodePath(getHashPath());\n    process.env.NODE_ENV !== \"production\" ? warning(!basename || hasBasename(path, basename), 'You are attempting to use a basename on a page whose URL path does not begin ' + 'with the basename. Expected path \"' + path + '\" to begin with \"' + basename + '\".') : void 0;\n    if (basename) path = stripBasename(path, basename);\n    return createLocation(path);\n  }\n  var transitionManager = createTransitionManager();\n  function setState(nextState) {\n    _extends(history, nextState);\n    history.length = globalHistory.length;\n    transitionManager.notifyListeners(history.location, history.action);\n  }\n  var forceNextPop = false;\n  var ignorePath = null;\n  function locationsAreEqual$$1(a, b) {\n    return a.pathname === b.pathname && a.search === b.search && a.hash === b.hash;\n  }\n  function handleHashChange() {\n    var path = getHashPath();\n    var encodedPath = encodePath(path);\n    if (path !== encodedPath) {\n      // Ensure we always have a properly-encoded hash.\n      replaceHashPath(encodedPath);\n    } else {\n      var location = getDOMLocation();\n      var prevLocation = history.location;\n      if (!forceNextPop && locationsAreEqual$$1(prevLocation, location)) return; // A hashchange doesn't always == location change.\n\n      if (ignorePath === createPath(location)) return; // Ignore this change; we already setState in push/replace.\n\n      ignorePath = null;\n      handlePop(location);\n    }\n  }\n  function handlePop(location) {\n    if (forceNextPop) {\n      forceNextPop = false;\n      setState();\n    } else {\n      var action = 'POP';\n      transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n        if (ok) {\n          setState({\n            action: action,\n            location: location\n          });\n        } else {\n          revertPop(location);\n        }\n      });\n    }\n  }\n  function revertPop(fromLocation) {\n    var toLocation = history.location; // TODO: We could probably make this more reliable by\n    // keeping a list of paths we've seen in sessionStorage.\n    // Instead, we just default to 0 for paths we don't know.\n\n    var toIndex = allPaths.lastIndexOf(createPath(toLocation));\n    if (toIndex === -1) toIndex = 0;\n    var fromIndex = allPaths.lastIndexOf(createPath(fromLocation));\n    if (fromIndex === -1) fromIndex = 0;\n    var delta = toIndex - fromIndex;\n    if (delta) {\n      forceNextPop = true;\n      go(delta);\n    }\n  } // Ensure the hash is encoded properly before doing anything else.\n\n  var path = getHashPath();\n  var encodedPath = encodePath(path);\n  if (path !== encodedPath) replaceHashPath(encodedPath);\n  var initialLocation = getDOMLocation();\n  var allPaths = [createPath(initialLocation)]; // Public interface\n\n  function createHref(location) {\n    var baseTag = document.querySelector('base');\n    var href = '';\n    if (baseTag && baseTag.getAttribute('href')) {\n      href = stripHash(window.location.href);\n    }\n    return href + '#' + encodePath(basename + createPath(location));\n  }\n  function push(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Hash history cannot push state; it is ignored') : void 0;\n    var action = 'PUSH';\n    var location = createLocation(path, undefined, undefined, history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var path = createPath(location);\n      var encodedPath = encodePath(basename + path);\n      var hashChanged = getHashPath() !== encodedPath;\n      if (hashChanged) {\n        // We cannot tell if a hashchange was caused by a PUSH, so we'd\n        // rather setState here and ignore the hashchange. The caveat here\n        // is that other hash histories in the page will consider it a POP.\n        ignorePath = path;\n        pushHashPath(encodedPath);\n        var prevIndex = allPaths.lastIndexOf(createPath(history.location));\n        var nextPaths = allPaths.slice(0, prevIndex + 1);\n        nextPaths.push(path);\n        allPaths = nextPaths;\n        setState({\n          action: action,\n          location: location\n        });\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(false, 'Hash history cannot PUSH the same path; a new entry will not be added to the history stack') : void 0;\n        setState();\n      }\n    });\n  }\n  function replace(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Hash history cannot replace state; it is ignored') : void 0;\n    var action = 'REPLACE';\n    var location = createLocation(path, undefined, undefined, history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var path = createPath(location);\n      var encodedPath = encodePath(basename + path);\n      var hashChanged = getHashPath() !== encodedPath;\n      if (hashChanged) {\n        // We cannot tell if a hashchange was caused by a REPLACE, so we'd\n        // rather setState here and ignore the hashchange. The caveat here\n        // is that other hash histories in the page will consider it a POP.\n        ignorePath = path;\n        replaceHashPath(encodedPath);\n      }\n      var prevIndex = allPaths.indexOf(createPath(history.location));\n      if (prevIndex !== -1) allPaths[prevIndex] = path;\n      setState({\n        action: action,\n        location: location\n      });\n    });\n  }\n  function go(n) {\n    process.env.NODE_ENV !== \"production\" ? warning(canGoWithoutReload, 'Hash history go(n) causes a full page reload in this browser') : void 0;\n    globalHistory.go(n);\n  }\n  function goBack() {\n    go(-1);\n  }\n  function goForward() {\n    go(1);\n  }\n  var listenerCount = 0;\n  function checkDOMListeners(delta) {\n    listenerCount += delta;\n    if (listenerCount === 1 && delta === 1) {\n      window.addEventListener(HashChangeEvent$1, handleHashChange);\n    } else if (listenerCount === 0) {\n      window.removeEventListener(HashChangeEvent$1, handleHashChange);\n    }\n  }\n  var isBlocked = false;\n  function block(prompt) {\n    if (prompt === void 0) {\n      prompt = false;\n    }\n    var unblock = transitionManager.setPrompt(prompt);\n    if (!isBlocked) {\n      checkDOMListeners(1);\n      isBlocked = true;\n    }\n    return function () {\n      if (isBlocked) {\n        isBlocked = false;\n        checkDOMListeners(-1);\n      }\n      return unblock();\n    };\n  }\n  function listen(listener) {\n    var unlisten = transitionManager.appendListener(listener);\n    checkDOMListeners(1);\n    return function () {\n      checkDOMListeners(-1);\n      unlisten();\n    };\n  }\n  var history = {\n    length: globalHistory.length,\n    action: 'POP',\n    location: initialLocation,\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    goBack: goBack,\n    goForward: goForward,\n    block: block,\n    listen: listen\n  };\n  return history;\n}\nfunction clamp(n, lowerBound, upperBound) {\n  return Math.min(Math.max(n, lowerBound), upperBound);\n}\n/**\n * Creates a history object that stores locations in memory.\n */\n\nfunction createMemoryHistory(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var _props = props,\n    getUserConfirmation = _props.getUserConfirmation,\n    _props$initialEntries = _props.initialEntries,\n    initialEntries = _props$initialEntries === void 0 ? ['/'] : _props$initialEntries,\n    _props$initialIndex = _props.initialIndex,\n    initialIndex = _props$initialIndex === void 0 ? 0 : _props$initialIndex,\n    _props$keyLength = _props.keyLength,\n    keyLength = _props$keyLength === void 0 ? 6 : _props$keyLength;\n  var transitionManager = createTransitionManager();\n  function setState(nextState) {\n    _extends(history, nextState);\n    history.length = history.entries.length;\n    transitionManager.notifyListeners(history.location, history.action);\n  }\n  function createKey() {\n    return Math.random().toString(36).substr(2, keyLength);\n  }\n  var index = clamp(initialIndex, 0, initialEntries.length - 1);\n  var entries = initialEntries.map(function (entry) {\n    return typeof entry === 'string' ? createLocation(entry, undefined, createKey()) : createLocation(entry, undefined, entry.key || createKey());\n  }); // Public interface\n\n  var createHref = createPath;\n  function push(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to push when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'PUSH';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var prevIndex = history.index;\n      var nextIndex = prevIndex + 1;\n      var nextEntries = history.entries.slice(0);\n      if (nextEntries.length > nextIndex) {\n        nextEntries.splice(nextIndex, nextEntries.length - nextIndex, location);\n      } else {\n        nextEntries.push(location);\n      }\n      setState({\n        action: action,\n        location: location,\n        index: nextIndex,\n        entries: nextEntries\n      });\n    });\n  }\n  function replace(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to replace when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'REPLACE';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      history.entries[history.index] = location;\n      setState({\n        action: action,\n        location: location\n      });\n    });\n  }\n  function go(n) {\n    var nextIndex = clamp(history.index + n, 0, history.entries.length - 1);\n    var action = 'POP';\n    var location = history.entries[nextIndex];\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (ok) {\n        setState({\n          action: action,\n          location: location,\n          index: nextIndex\n        });\n      } else {\n        // Mimic the behavior of DOM histories by\n        // causing a render after a cancelled POP.\n        setState();\n      }\n    });\n  }\n  function goBack() {\n    go(-1);\n  }\n  function goForward() {\n    go(1);\n  }\n  function canGo(n) {\n    var nextIndex = history.index + n;\n    return nextIndex >= 0 && nextIndex < history.entries.length;\n  }\n  function block(prompt) {\n    if (prompt === void 0) {\n      prompt = false;\n    }\n    return transitionManager.setPrompt(prompt);\n  }\n  function listen(listener) {\n    return transitionManager.appendListener(listener);\n  }\n  var history = {\n    length: entries.length,\n    action: 'POP',\n    location: entries[index],\n    index: index,\n    entries: entries,\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    goBack: goBack,\n    goForward: goForward,\n    canGo: canGo,\n    block: block,\n    listen: listen\n  };\n  return history;\n}\nexport { createBrowserHistory, createHashHistory, createMemoryHistory, createLocation, locationsAreEqual, parsePath, createPath };", "map": {"version": 3, "names": ["_extends", "resolvePathname", "valueEqual", "warning", "invariant", "addLeadingSlash", "path", "char<PERSON>t", "stripLeadingSlash", "substr", "hasBasename", "prefix", "toLowerCase", "indexOf", "length", "stripBasename", "stripTrailingSlash", "slice", "parsePath", "pathname", "search", "hash", "hashIndex", "searchIndex", "createPath", "location", "createLocation", "state", "key", "currentLocation", "undefined", "decodeURI", "e", "URIError", "locationsAreEqual", "a", "b", "createTransitionManager", "prompt", "setPrompt", "nextPrompt", "process", "env", "NODE_ENV", "confirmTransitionTo", "action", "getUserConfirmation", "callback", "result", "listeners", "appendListener", "fn", "isActive", "listener", "apply", "arguments", "push", "filter", "item", "notifyListeners", "_len", "args", "Array", "_key", "for<PERSON>ach", "canUseDOM", "window", "document", "createElement", "getConfirmation", "message", "confirm", "supportsHistory", "ua", "navigator", "userAgent", "history", "supportsPopStateOnHashChange", "supportsGoWithoutReloadUsingHash", "isExtraneousPopstateEvent", "event", "PopStateEvent", "HashChangeEvent", "getHistoryState", "createBrowserHistory", "props", "globalHistory", "canUseHistory", "needsHashChangeListener", "_props", "_props$forceRefresh", "forceRefresh", "_props$getUserConfirm", "_props$keyLength", "<PERSON><PERSON><PERSON><PERSON>", "basename", "getDOMLocation", "historyState", "_ref", "_window$location", "create<PERSON><PERSON>", "Math", "random", "toString", "transitionManager", "setState", "nextState", "handlePopState", "handlePop", "handleHashChange", "forceNextPop", "ok", "revertPop", "fromLocation", "toLocation", "toIndex", "allKeys", "fromIndex", "delta", "go", "initialLocation", "createHref", "href", "pushState", "prevIndex", "nextKeys", "replace", "replaceState", "n", "goBack", "goForward", "listenerCount", "checkDOMListeners", "addEventListener", "removeEventListener", "isBlocked", "block", "unblock", "listen", "unlisten", "HashChangeEvent$1", "HashPathCoders", "hashbang", "encodePath", "decodePath", "noslash", "slash", "stripHash", "url", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "substring", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createHashHistory", "canGoWithoutReload", "_props$hashType", "hashType", "_HashPathCoders$hashT", "ignore<PERSON><PERSON>", "locationsAreEqual$$1", "encodedPath", "prevLocation", "allPaths", "lastIndexOf", "baseTag", "querySelector", "getAttribute", "hash<PERSON><PERSON>ed", "nextPaths", "clamp", "lowerBound", "upperBound", "min", "max", "createMemoryHistory", "_props$initialEntries", "initialEntries", "_props$initialIndex", "initialIndex", "entries", "index", "map", "entry", "nextIndex", "nextEntries", "splice", "canGo"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/history/esm/history.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport resolvePathname from 'resolve-pathname';\nimport valueEqual from 'value-equal';\nimport warning from 'tiny-warning';\nimport invariant from 'tiny-invariant';\n\nfunction addLeadingSlash(path) {\n  return path.charAt(0) === '/' ? path : '/' + path;\n}\nfunction stripLeadingSlash(path) {\n  return path.charAt(0) === '/' ? path.substr(1) : path;\n}\nfunction hasBasename(path, prefix) {\n  return path.toLowerCase().indexOf(prefix.toLowerCase()) === 0 && '/?#'.indexOf(path.charAt(prefix.length)) !== -1;\n}\nfunction stripBasename(path, prefix) {\n  return hasBasename(path, prefix) ? path.substr(prefix.length) : path;\n}\nfunction stripTrailingSlash(path) {\n  return path.charAt(path.length - 1) === '/' ? path.slice(0, -1) : path;\n}\nfunction parsePath(path) {\n  var pathname = path || '/';\n  var search = '';\n  var hash = '';\n  var hashIndex = pathname.indexOf('#');\n\n  if (hashIndex !== -1) {\n    hash = pathname.substr(hashIndex);\n    pathname = pathname.substr(0, hashIndex);\n  }\n\n  var searchIndex = pathname.indexOf('?');\n\n  if (searchIndex !== -1) {\n    search = pathname.substr(searchIndex);\n    pathname = pathname.substr(0, searchIndex);\n  }\n\n  return {\n    pathname: pathname,\n    search: search === '?' ? '' : search,\n    hash: hash === '#' ? '' : hash\n  };\n}\nfunction createPath(location) {\n  var pathname = location.pathname,\n      search = location.search,\n      hash = location.hash;\n  var path = pathname || '/';\n  if (search && search !== '?') path += search.charAt(0) === '?' ? search : \"?\" + search;\n  if (hash && hash !== '#') path += hash.charAt(0) === '#' ? hash : \"#\" + hash;\n  return path;\n}\n\nfunction createLocation(path, state, key, currentLocation) {\n  var location;\n\n  if (typeof path === 'string') {\n    // Two-arg form: push(path, state)\n    location = parsePath(path);\n    location.state = state;\n  } else {\n    // One-arg form: push(location)\n    location = _extends({}, path);\n    if (location.pathname === undefined) location.pathname = '';\n\n    if (location.search) {\n      if (location.search.charAt(0) !== '?') location.search = '?' + location.search;\n    } else {\n      location.search = '';\n    }\n\n    if (location.hash) {\n      if (location.hash.charAt(0) !== '#') location.hash = '#' + location.hash;\n    } else {\n      location.hash = '';\n    }\n\n    if (state !== undefined && location.state === undefined) location.state = state;\n  }\n\n  try {\n    location.pathname = decodeURI(location.pathname);\n  } catch (e) {\n    if (e instanceof URIError) {\n      throw new URIError('Pathname \"' + location.pathname + '\" could not be decoded. ' + 'This is likely caused by an invalid percent-encoding.');\n    } else {\n      throw e;\n    }\n  }\n\n  if (key) location.key = key;\n\n  if (currentLocation) {\n    // Resolve incomplete/relative pathname relative to current location.\n    if (!location.pathname) {\n      location.pathname = currentLocation.pathname;\n    } else if (location.pathname.charAt(0) !== '/') {\n      location.pathname = resolvePathname(location.pathname, currentLocation.pathname);\n    }\n  } else {\n    // When there is no prior location and pathname is empty, set it to /\n    if (!location.pathname) {\n      location.pathname = '/';\n    }\n  }\n\n  return location;\n}\nfunction locationsAreEqual(a, b) {\n  return a.pathname === b.pathname && a.search === b.search && a.hash === b.hash && a.key === b.key && valueEqual(a.state, b.state);\n}\n\nfunction createTransitionManager() {\n  var prompt = null;\n\n  function setPrompt(nextPrompt) {\n    process.env.NODE_ENV !== \"production\" ? warning(prompt == null, 'A history supports only one prompt at a time') : void 0;\n    prompt = nextPrompt;\n    return function () {\n      if (prompt === nextPrompt) prompt = null;\n    };\n  }\n\n  function confirmTransitionTo(location, action, getUserConfirmation, callback) {\n    // TODO: If another transition starts while we're still confirming\n    // the previous one, we may end up in a weird state. Figure out the\n    // best way to handle this.\n    if (prompt != null) {\n      var result = typeof prompt === 'function' ? prompt(location, action) : prompt;\n\n      if (typeof result === 'string') {\n        if (typeof getUserConfirmation === 'function') {\n          getUserConfirmation(result, callback);\n        } else {\n          process.env.NODE_ENV !== \"production\" ? warning(false, 'A history needs a getUserConfirmation function in order to use a prompt message') : void 0;\n          callback(true);\n        }\n      } else {\n        // Return false from a transition hook to cancel the transition.\n        callback(result !== false);\n      }\n    } else {\n      callback(true);\n    }\n  }\n\n  var listeners = [];\n\n  function appendListener(fn) {\n    var isActive = true;\n\n    function listener() {\n      if (isActive) fn.apply(void 0, arguments);\n    }\n\n    listeners.push(listener);\n    return function () {\n      isActive = false;\n      listeners = listeners.filter(function (item) {\n        return item !== listener;\n      });\n    };\n  }\n\n  function notifyListeners() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    listeners.forEach(function (listener) {\n      return listener.apply(void 0, args);\n    });\n  }\n\n  return {\n    setPrompt: setPrompt,\n    confirmTransitionTo: confirmTransitionTo,\n    appendListener: appendListener,\n    notifyListeners: notifyListeners\n  };\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nfunction getConfirmation(message, callback) {\n  callback(window.confirm(message)); // eslint-disable-line no-alert\n}\n/**\n * Returns true if the HTML5 history API is supported. Taken from Modernizr.\n *\n * https://github.com/Modernizr/Modernizr/blob/master/LICENSE\n * https://github.com/Modernizr/Modernizr/blob/master/feature-detects/history.js\n * changed to avoid false negatives for Windows Phones: https://github.com/reactjs/react-router/issues/586\n */\n\nfunction supportsHistory() {\n  var ua = window.navigator.userAgent;\n  if ((ua.indexOf('Android 2.') !== -1 || ua.indexOf('Android 4.0') !== -1) && ua.indexOf('Mobile Safari') !== -1 && ua.indexOf('Chrome') === -1 && ua.indexOf('Windows Phone') === -1) return false;\n  return window.history && 'pushState' in window.history;\n}\n/**\n * Returns true if browser fires popstate on hash change.\n * IE10 and IE11 do not.\n */\n\nfunction supportsPopStateOnHashChange() {\n  return window.navigator.userAgent.indexOf('Trident') === -1;\n}\n/**\n * Returns false if using go(n) with hash history causes a full page reload.\n */\n\nfunction supportsGoWithoutReloadUsingHash() {\n  return window.navigator.userAgent.indexOf('Firefox') === -1;\n}\n/**\n * Returns true if a given popstate event is an extraneous WebKit event.\n * Accounts for the fact that Chrome on iOS fires real popstate events\n * containing undefined state when pressing the back button.\n */\n\nfunction isExtraneousPopstateEvent(event) {\n  return event.state === undefined && navigator.userAgent.indexOf('CriOS') === -1;\n}\n\nvar PopStateEvent = 'popstate';\nvar HashChangeEvent = 'hashchange';\n\nfunction getHistoryState() {\n  try {\n    return window.history.state || {};\n  } catch (e) {\n    // IE 11 sometimes throws when accessing window.history.state\n    // See https://github.com/ReactTraining/history/pull/289\n    return {};\n  }\n}\n/**\n * Creates a history object that uses the HTML5 history API including\n * pushState, replaceState, and the popstate event.\n */\n\n\nfunction createBrowserHistory(props) {\n  if (props === void 0) {\n    props = {};\n  }\n\n  !canUseDOM ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Browser history needs a DOM') : invariant(false) : void 0;\n  var globalHistory = window.history;\n  var canUseHistory = supportsHistory();\n  var needsHashChangeListener = !supportsPopStateOnHashChange();\n  var _props = props,\n      _props$forceRefresh = _props.forceRefresh,\n      forceRefresh = _props$forceRefresh === void 0 ? false : _props$forceRefresh,\n      _props$getUserConfirm = _props.getUserConfirmation,\n      getUserConfirmation = _props$getUserConfirm === void 0 ? getConfirmation : _props$getUserConfirm,\n      _props$keyLength = _props.keyLength,\n      keyLength = _props$keyLength === void 0 ? 6 : _props$keyLength;\n  var basename = props.basename ? stripTrailingSlash(addLeadingSlash(props.basename)) : '';\n\n  function getDOMLocation(historyState) {\n    var _ref = historyState || {},\n        key = _ref.key,\n        state = _ref.state;\n\n    var _window$location = window.location,\n        pathname = _window$location.pathname,\n        search = _window$location.search,\n        hash = _window$location.hash;\n    var path = pathname + search + hash;\n    process.env.NODE_ENV !== \"production\" ? warning(!basename || hasBasename(path, basename), 'You are attempting to use a basename on a page whose URL path does not begin ' + 'with the basename. Expected path \"' + path + '\" to begin with \"' + basename + '\".') : void 0;\n    if (basename) path = stripBasename(path, basename);\n    return createLocation(path, state, key);\n  }\n\n  function createKey() {\n    return Math.random().toString(36).substr(2, keyLength);\n  }\n\n  var transitionManager = createTransitionManager();\n\n  function setState(nextState) {\n    _extends(history, nextState);\n\n    history.length = globalHistory.length;\n    transitionManager.notifyListeners(history.location, history.action);\n  }\n\n  function handlePopState(event) {\n    // Ignore extraneous popstate events in WebKit.\n    if (isExtraneousPopstateEvent(event)) return;\n    handlePop(getDOMLocation(event.state));\n  }\n\n  function handleHashChange() {\n    handlePop(getDOMLocation(getHistoryState()));\n  }\n\n  var forceNextPop = false;\n\n  function handlePop(location) {\n    if (forceNextPop) {\n      forceNextPop = false;\n      setState();\n    } else {\n      var action = 'POP';\n      transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n        if (ok) {\n          setState({\n            action: action,\n            location: location\n          });\n        } else {\n          revertPop(location);\n        }\n      });\n    }\n  }\n\n  function revertPop(fromLocation) {\n    var toLocation = history.location; // TODO: We could probably make this more reliable by\n    // keeping a list of keys we've seen in sessionStorage.\n    // Instead, we just default to 0 for keys we don't know.\n\n    var toIndex = allKeys.indexOf(toLocation.key);\n    if (toIndex === -1) toIndex = 0;\n    var fromIndex = allKeys.indexOf(fromLocation.key);\n    if (fromIndex === -1) fromIndex = 0;\n    var delta = toIndex - fromIndex;\n\n    if (delta) {\n      forceNextPop = true;\n      go(delta);\n    }\n  }\n\n  var initialLocation = getDOMLocation(getHistoryState());\n  var allKeys = [initialLocation.key]; // Public interface\n\n  function createHref(location) {\n    return basename + createPath(location);\n  }\n\n  function push(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to push when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'PUSH';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var href = createHref(location);\n      var key = location.key,\n          state = location.state;\n\n      if (canUseHistory) {\n        globalHistory.pushState({\n          key: key,\n          state: state\n        }, null, href);\n\n        if (forceRefresh) {\n          window.location.href = href;\n        } else {\n          var prevIndex = allKeys.indexOf(history.location.key);\n          var nextKeys = allKeys.slice(0, prevIndex + 1);\n          nextKeys.push(location.key);\n          allKeys = nextKeys;\n          setState({\n            action: action,\n            location: location\n          });\n        }\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Browser history cannot push state in browsers that do not support HTML5 history') : void 0;\n        window.location.href = href;\n      }\n    });\n  }\n\n  function replace(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to replace when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'REPLACE';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var href = createHref(location);\n      var key = location.key,\n          state = location.state;\n\n      if (canUseHistory) {\n        globalHistory.replaceState({\n          key: key,\n          state: state\n        }, null, href);\n\n        if (forceRefresh) {\n          window.location.replace(href);\n        } else {\n          var prevIndex = allKeys.indexOf(history.location.key);\n          if (prevIndex !== -1) allKeys[prevIndex] = location.key;\n          setState({\n            action: action,\n            location: location\n          });\n        }\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Browser history cannot replace state in browsers that do not support HTML5 history') : void 0;\n        window.location.replace(href);\n      }\n    });\n  }\n\n  function go(n) {\n    globalHistory.go(n);\n  }\n\n  function goBack() {\n    go(-1);\n  }\n\n  function goForward() {\n    go(1);\n  }\n\n  var listenerCount = 0;\n\n  function checkDOMListeners(delta) {\n    listenerCount += delta;\n\n    if (listenerCount === 1 && delta === 1) {\n      window.addEventListener(PopStateEvent, handlePopState);\n      if (needsHashChangeListener) window.addEventListener(HashChangeEvent, handleHashChange);\n    } else if (listenerCount === 0) {\n      window.removeEventListener(PopStateEvent, handlePopState);\n      if (needsHashChangeListener) window.removeEventListener(HashChangeEvent, handleHashChange);\n    }\n  }\n\n  var isBlocked = false;\n\n  function block(prompt) {\n    if (prompt === void 0) {\n      prompt = false;\n    }\n\n    var unblock = transitionManager.setPrompt(prompt);\n\n    if (!isBlocked) {\n      checkDOMListeners(1);\n      isBlocked = true;\n    }\n\n    return function () {\n      if (isBlocked) {\n        isBlocked = false;\n        checkDOMListeners(-1);\n      }\n\n      return unblock();\n    };\n  }\n\n  function listen(listener) {\n    var unlisten = transitionManager.appendListener(listener);\n    checkDOMListeners(1);\n    return function () {\n      checkDOMListeners(-1);\n      unlisten();\n    };\n  }\n\n  var history = {\n    length: globalHistory.length,\n    action: 'POP',\n    location: initialLocation,\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    goBack: goBack,\n    goForward: goForward,\n    block: block,\n    listen: listen\n  };\n  return history;\n}\n\nvar HashChangeEvent$1 = 'hashchange';\nvar HashPathCoders = {\n  hashbang: {\n    encodePath: function encodePath(path) {\n      return path.charAt(0) === '!' ? path : '!/' + stripLeadingSlash(path);\n    },\n    decodePath: function decodePath(path) {\n      return path.charAt(0) === '!' ? path.substr(1) : path;\n    }\n  },\n  noslash: {\n    encodePath: stripLeadingSlash,\n    decodePath: addLeadingSlash\n  },\n  slash: {\n    encodePath: addLeadingSlash,\n    decodePath: addLeadingSlash\n  }\n};\n\nfunction stripHash(url) {\n  var hashIndex = url.indexOf('#');\n  return hashIndex === -1 ? url : url.slice(0, hashIndex);\n}\n\nfunction getHashPath() {\n  // We can't use window.location.hash here because it's not\n  // consistent across browsers - Firefox will pre-decode it!\n  var href = window.location.href;\n  var hashIndex = href.indexOf('#');\n  return hashIndex === -1 ? '' : href.substring(hashIndex + 1);\n}\n\nfunction pushHashPath(path) {\n  window.location.hash = path;\n}\n\nfunction replaceHashPath(path) {\n  window.location.replace(stripHash(window.location.href) + '#' + path);\n}\n\nfunction createHashHistory(props) {\n  if (props === void 0) {\n    props = {};\n  }\n\n  !canUseDOM ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Hash history needs a DOM') : invariant(false) : void 0;\n  var globalHistory = window.history;\n  var canGoWithoutReload = supportsGoWithoutReloadUsingHash();\n  var _props = props,\n      _props$getUserConfirm = _props.getUserConfirmation,\n      getUserConfirmation = _props$getUserConfirm === void 0 ? getConfirmation : _props$getUserConfirm,\n      _props$hashType = _props.hashType,\n      hashType = _props$hashType === void 0 ? 'slash' : _props$hashType;\n  var basename = props.basename ? stripTrailingSlash(addLeadingSlash(props.basename)) : '';\n  var _HashPathCoders$hashT = HashPathCoders[hashType],\n      encodePath = _HashPathCoders$hashT.encodePath,\n      decodePath = _HashPathCoders$hashT.decodePath;\n\n  function getDOMLocation() {\n    var path = decodePath(getHashPath());\n    process.env.NODE_ENV !== \"production\" ? warning(!basename || hasBasename(path, basename), 'You are attempting to use a basename on a page whose URL path does not begin ' + 'with the basename. Expected path \"' + path + '\" to begin with \"' + basename + '\".') : void 0;\n    if (basename) path = stripBasename(path, basename);\n    return createLocation(path);\n  }\n\n  var transitionManager = createTransitionManager();\n\n  function setState(nextState) {\n    _extends(history, nextState);\n\n    history.length = globalHistory.length;\n    transitionManager.notifyListeners(history.location, history.action);\n  }\n\n  var forceNextPop = false;\n  var ignorePath = null;\n\n  function locationsAreEqual$$1(a, b) {\n    return a.pathname === b.pathname && a.search === b.search && a.hash === b.hash;\n  }\n\n  function handleHashChange() {\n    var path = getHashPath();\n    var encodedPath = encodePath(path);\n\n    if (path !== encodedPath) {\n      // Ensure we always have a properly-encoded hash.\n      replaceHashPath(encodedPath);\n    } else {\n      var location = getDOMLocation();\n      var prevLocation = history.location;\n      if (!forceNextPop && locationsAreEqual$$1(prevLocation, location)) return; // A hashchange doesn't always == location change.\n\n      if (ignorePath === createPath(location)) return; // Ignore this change; we already setState in push/replace.\n\n      ignorePath = null;\n      handlePop(location);\n    }\n  }\n\n  function handlePop(location) {\n    if (forceNextPop) {\n      forceNextPop = false;\n      setState();\n    } else {\n      var action = 'POP';\n      transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n        if (ok) {\n          setState({\n            action: action,\n            location: location\n          });\n        } else {\n          revertPop(location);\n        }\n      });\n    }\n  }\n\n  function revertPop(fromLocation) {\n    var toLocation = history.location; // TODO: We could probably make this more reliable by\n    // keeping a list of paths we've seen in sessionStorage.\n    // Instead, we just default to 0 for paths we don't know.\n\n    var toIndex = allPaths.lastIndexOf(createPath(toLocation));\n    if (toIndex === -1) toIndex = 0;\n    var fromIndex = allPaths.lastIndexOf(createPath(fromLocation));\n    if (fromIndex === -1) fromIndex = 0;\n    var delta = toIndex - fromIndex;\n\n    if (delta) {\n      forceNextPop = true;\n      go(delta);\n    }\n  } // Ensure the hash is encoded properly before doing anything else.\n\n\n  var path = getHashPath();\n  var encodedPath = encodePath(path);\n  if (path !== encodedPath) replaceHashPath(encodedPath);\n  var initialLocation = getDOMLocation();\n  var allPaths = [createPath(initialLocation)]; // Public interface\n\n  function createHref(location) {\n    var baseTag = document.querySelector('base');\n    var href = '';\n\n    if (baseTag && baseTag.getAttribute('href')) {\n      href = stripHash(window.location.href);\n    }\n\n    return href + '#' + encodePath(basename + createPath(location));\n  }\n\n  function push(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Hash history cannot push state; it is ignored') : void 0;\n    var action = 'PUSH';\n    var location = createLocation(path, undefined, undefined, history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var path = createPath(location);\n      var encodedPath = encodePath(basename + path);\n      var hashChanged = getHashPath() !== encodedPath;\n\n      if (hashChanged) {\n        // We cannot tell if a hashchange was caused by a PUSH, so we'd\n        // rather setState here and ignore the hashchange. The caveat here\n        // is that other hash histories in the page will consider it a POP.\n        ignorePath = path;\n        pushHashPath(encodedPath);\n        var prevIndex = allPaths.lastIndexOf(createPath(history.location));\n        var nextPaths = allPaths.slice(0, prevIndex + 1);\n        nextPaths.push(path);\n        allPaths = nextPaths;\n        setState({\n          action: action,\n          location: location\n        });\n      } else {\n        process.env.NODE_ENV !== \"production\" ? warning(false, 'Hash history cannot PUSH the same path; a new entry will not be added to the history stack') : void 0;\n        setState();\n      }\n    });\n  }\n\n  function replace(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(state === undefined, 'Hash history cannot replace state; it is ignored') : void 0;\n    var action = 'REPLACE';\n    var location = createLocation(path, undefined, undefined, history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var path = createPath(location);\n      var encodedPath = encodePath(basename + path);\n      var hashChanged = getHashPath() !== encodedPath;\n\n      if (hashChanged) {\n        // We cannot tell if a hashchange was caused by a REPLACE, so we'd\n        // rather setState here and ignore the hashchange. The caveat here\n        // is that other hash histories in the page will consider it a POP.\n        ignorePath = path;\n        replaceHashPath(encodedPath);\n      }\n\n      var prevIndex = allPaths.indexOf(createPath(history.location));\n      if (prevIndex !== -1) allPaths[prevIndex] = path;\n      setState({\n        action: action,\n        location: location\n      });\n    });\n  }\n\n  function go(n) {\n    process.env.NODE_ENV !== \"production\" ? warning(canGoWithoutReload, 'Hash history go(n) causes a full page reload in this browser') : void 0;\n    globalHistory.go(n);\n  }\n\n  function goBack() {\n    go(-1);\n  }\n\n  function goForward() {\n    go(1);\n  }\n\n  var listenerCount = 0;\n\n  function checkDOMListeners(delta) {\n    listenerCount += delta;\n\n    if (listenerCount === 1 && delta === 1) {\n      window.addEventListener(HashChangeEvent$1, handleHashChange);\n    } else if (listenerCount === 0) {\n      window.removeEventListener(HashChangeEvent$1, handleHashChange);\n    }\n  }\n\n  var isBlocked = false;\n\n  function block(prompt) {\n    if (prompt === void 0) {\n      prompt = false;\n    }\n\n    var unblock = transitionManager.setPrompt(prompt);\n\n    if (!isBlocked) {\n      checkDOMListeners(1);\n      isBlocked = true;\n    }\n\n    return function () {\n      if (isBlocked) {\n        isBlocked = false;\n        checkDOMListeners(-1);\n      }\n\n      return unblock();\n    };\n  }\n\n  function listen(listener) {\n    var unlisten = transitionManager.appendListener(listener);\n    checkDOMListeners(1);\n    return function () {\n      checkDOMListeners(-1);\n      unlisten();\n    };\n  }\n\n  var history = {\n    length: globalHistory.length,\n    action: 'POP',\n    location: initialLocation,\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    goBack: goBack,\n    goForward: goForward,\n    block: block,\n    listen: listen\n  };\n  return history;\n}\n\nfunction clamp(n, lowerBound, upperBound) {\n  return Math.min(Math.max(n, lowerBound), upperBound);\n}\n/**\n * Creates a history object that stores locations in memory.\n */\n\n\nfunction createMemoryHistory(props) {\n  if (props === void 0) {\n    props = {};\n  }\n\n  var _props = props,\n      getUserConfirmation = _props.getUserConfirmation,\n      _props$initialEntries = _props.initialEntries,\n      initialEntries = _props$initialEntries === void 0 ? ['/'] : _props$initialEntries,\n      _props$initialIndex = _props.initialIndex,\n      initialIndex = _props$initialIndex === void 0 ? 0 : _props$initialIndex,\n      _props$keyLength = _props.keyLength,\n      keyLength = _props$keyLength === void 0 ? 6 : _props$keyLength;\n  var transitionManager = createTransitionManager();\n\n  function setState(nextState) {\n    _extends(history, nextState);\n\n    history.length = history.entries.length;\n    transitionManager.notifyListeners(history.location, history.action);\n  }\n\n  function createKey() {\n    return Math.random().toString(36).substr(2, keyLength);\n  }\n\n  var index = clamp(initialIndex, 0, initialEntries.length - 1);\n  var entries = initialEntries.map(function (entry) {\n    return typeof entry === 'string' ? createLocation(entry, undefined, createKey()) : createLocation(entry, undefined, entry.key || createKey());\n  }); // Public interface\n\n  var createHref = createPath;\n\n  function push(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to push when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'PUSH';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      var prevIndex = history.index;\n      var nextIndex = prevIndex + 1;\n      var nextEntries = history.entries.slice(0);\n\n      if (nextEntries.length > nextIndex) {\n        nextEntries.splice(nextIndex, nextEntries.length - nextIndex, location);\n      } else {\n        nextEntries.push(location);\n      }\n\n      setState({\n        action: action,\n        location: location,\n        index: nextIndex,\n        entries: nextEntries\n      });\n    });\n  }\n\n  function replace(path, state) {\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof path === 'object' && path.state !== undefined && state !== undefined), 'You should avoid providing a 2nd state argument to replace when the 1st ' + 'argument is a location-like object that already has state; it is ignored') : void 0;\n    var action = 'REPLACE';\n    var location = createLocation(path, state, createKey(), history.location);\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (!ok) return;\n      history.entries[history.index] = location;\n      setState({\n        action: action,\n        location: location\n      });\n    });\n  }\n\n  function go(n) {\n    var nextIndex = clamp(history.index + n, 0, history.entries.length - 1);\n    var action = 'POP';\n    var location = history.entries[nextIndex];\n    transitionManager.confirmTransitionTo(location, action, getUserConfirmation, function (ok) {\n      if (ok) {\n        setState({\n          action: action,\n          location: location,\n          index: nextIndex\n        });\n      } else {\n        // Mimic the behavior of DOM histories by\n        // causing a render after a cancelled POP.\n        setState();\n      }\n    });\n  }\n\n  function goBack() {\n    go(-1);\n  }\n\n  function goForward() {\n    go(1);\n  }\n\n  function canGo(n) {\n    var nextIndex = history.index + n;\n    return nextIndex >= 0 && nextIndex < history.entries.length;\n  }\n\n  function block(prompt) {\n    if (prompt === void 0) {\n      prompt = false;\n    }\n\n    return transitionManager.setPrompt(prompt);\n  }\n\n  function listen(listener) {\n    return transitionManager.appendListener(listener);\n  }\n\n  var history = {\n    length: entries.length,\n    action: 'POP',\n    location: entries[index],\n    index: index,\n    entries: entries,\n    createHref: createHref,\n    push: push,\n    replace: replace,\n    go: go,\n    goBack: goBack,\n    goForward: goForward,\n    canGo: canGo,\n    block: block,\n    listen: listen\n  };\n  return history;\n}\n\nexport { createBrowserHistory, createHashHistory, createMemoryHistory, createLocation, locationsAreEqual, parsePath, createPath };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,kBAAkB;AAC9C,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,SAAS,MAAM,gBAAgB;AAEtC,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,OAAOA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGD,IAAI,GAAG,GAAG,GAAGA,IAAI;AACnD;AACA,SAASE,iBAAiBA,CAACF,IAAI,EAAE;EAC/B,OAAOA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGD,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,GAAGH,IAAI;AACvD;AACA,SAASI,WAAWA,CAACJ,IAAI,EAAEK,MAAM,EAAE;EACjC,OAAOL,IAAI,CAACM,WAAW,CAAC,CAAC,CAACC,OAAO,CAACF,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAACC,OAAO,CAACP,IAAI,CAACC,MAAM,CAACI,MAAM,CAACG,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;AACnH;AACA,SAASC,aAAaA,CAACT,IAAI,EAAEK,MAAM,EAAE;EACnC,OAAOD,WAAW,CAACJ,IAAI,EAAEK,MAAM,CAAC,GAAGL,IAAI,CAACG,MAAM,CAACE,MAAM,CAACG,MAAM,CAAC,GAAGR,IAAI;AACtE;AACA,SAASU,kBAAkBA,CAACV,IAAI,EAAE;EAChC,OAAOA,IAAI,CAACC,MAAM,CAACD,IAAI,CAACQ,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAGR,IAAI,CAACW,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGX,IAAI;AACxE;AACA,SAASY,SAASA,CAACZ,IAAI,EAAE;EACvB,IAAIa,QAAQ,GAAGb,IAAI,IAAI,GAAG;EAC1B,IAAIc,MAAM,GAAG,EAAE;EACf,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,SAAS,GAAGH,QAAQ,CAACN,OAAO,CAAC,GAAG,CAAC;EAErC,IAAIS,SAAS,KAAK,CAAC,CAAC,EAAE;IACpBD,IAAI,GAAGF,QAAQ,CAACV,MAAM,CAACa,SAAS,CAAC;IACjCH,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAAC,CAAC,EAAEa,SAAS,CAAC;EAC1C;EAEA,IAAIC,WAAW,GAAGJ,QAAQ,CAACN,OAAO,CAAC,GAAG,CAAC;EAEvC,IAAIU,WAAW,KAAK,CAAC,CAAC,EAAE;IACtBH,MAAM,GAAGD,QAAQ,CAACV,MAAM,CAACc,WAAW,CAAC;IACrCJ,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAAC,CAAC,EAAEc,WAAW,CAAC;EAC5C;EAEA,OAAO;IACLJ,QAAQ,EAAEA,QAAQ;IAClBC,MAAM,EAAEA,MAAM,KAAK,GAAG,GAAG,EAAE,GAAGA,MAAM;IACpCC,IAAI,EAAEA,IAAI,KAAK,GAAG,GAAG,EAAE,GAAGA;EAC5B,CAAC;AACH;AACA,SAASG,UAAUA,CAACC,QAAQ,EAAE;EAC5B,IAAIN,QAAQ,GAAGM,QAAQ,CAACN,QAAQ;IAC5BC,MAAM,GAAGK,QAAQ,CAACL,MAAM;IACxBC,IAAI,GAAGI,QAAQ,CAACJ,IAAI;EACxB,IAAIf,IAAI,GAAGa,QAAQ,IAAI,GAAG;EAC1B,IAAIC,MAAM,IAAIA,MAAM,KAAK,GAAG,EAAEd,IAAI,IAAIc,MAAM,CAACb,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGa,MAAM,GAAG,GAAG,GAAGA,MAAM;EACtF,IAAIC,IAAI,IAAIA,IAAI,KAAK,GAAG,EAAEf,IAAI,IAAIe,IAAI,CAACd,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGc,IAAI,GAAG,GAAG,GAAGA,IAAI;EAC5E,OAAOf,IAAI;AACb;AAEA,SAASoB,cAAcA,CAACpB,IAAI,EAAEqB,KAAK,EAAEC,GAAG,EAAEC,eAAe,EAAE;EACzD,IAAIJ,QAAQ;EAEZ,IAAI,OAAOnB,IAAI,KAAK,QAAQ,EAAE;IAC5B;IACAmB,QAAQ,GAAGP,SAAS,CAACZ,IAAI,CAAC;IAC1BmB,QAAQ,CAACE,KAAK,GAAGA,KAAK;EACxB,CAAC,MAAM;IACL;IACAF,QAAQ,GAAGzB,QAAQ,CAAC,CAAC,CAAC,EAAEM,IAAI,CAAC;IAC7B,IAAImB,QAAQ,CAACN,QAAQ,KAAKW,SAAS,EAAEL,QAAQ,CAACN,QAAQ,GAAG,EAAE;IAE3D,IAAIM,QAAQ,CAACL,MAAM,EAAE;MACnB,IAAIK,QAAQ,CAACL,MAAM,CAACb,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEkB,QAAQ,CAACL,MAAM,GAAG,GAAG,GAAGK,QAAQ,CAACL,MAAM;IAChF,CAAC,MAAM;MACLK,QAAQ,CAACL,MAAM,GAAG,EAAE;IACtB;IAEA,IAAIK,QAAQ,CAACJ,IAAI,EAAE;MACjB,IAAII,QAAQ,CAACJ,IAAI,CAACd,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEkB,QAAQ,CAACJ,IAAI,GAAG,GAAG,GAAGI,QAAQ,CAACJ,IAAI;IAC1E,CAAC,MAAM;MACLI,QAAQ,CAACJ,IAAI,GAAG,EAAE;IACpB;IAEA,IAAIM,KAAK,KAAKG,SAAS,IAAIL,QAAQ,CAACE,KAAK,KAAKG,SAAS,EAAEL,QAAQ,CAACE,KAAK,GAAGA,KAAK;EACjF;EAEA,IAAI;IACFF,QAAQ,CAACN,QAAQ,GAAGY,SAAS,CAACN,QAAQ,CAACN,QAAQ,CAAC;EAClD,CAAC,CAAC,OAAOa,CAAC,EAAE;IACV,IAAIA,CAAC,YAAYC,QAAQ,EAAE;MACzB,MAAM,IAAIA,QAAQ,CAAC,YAAY,GAAGR,QAAQ,CAACN,QAAQ,GAAG,0BAA0B,GAAG,uDAAuD,CAAC;IAC7I,CAAC,MAAM;MACL,MAAMa,CAAC;IACT;EACF;EAEA,IAAIJ,GAAG,EAAEH,QAAQ,CAACG,GAAG,GAAGA,GAAG;EAE3B,IAAIC,eAAe,EAAE;IACnB;IACA,IAAI,CAACJ,QAAQ,CAACN,QAAQ,EAAE;MACtBM,QAAQ,CAACN,QAAQ,GAAGU,eAAe,CAACV,QAAQ;IAC9C,CAAC,MAAM,IAAIM,QAAQ,CAACN,QAAQ,CAACZ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC9CkB,QAAQ,CAACN,QAAQ,GAAGlB,eAAe,CAACwB,QAAQ,CAACN,QAAQ,EAAEU,eAAe,CAACV,QAAQ,CAAC;IAClF;EACF,CAAC,MAAM;IACL;IACA,IAAI,CAACM,QAAQ,CAACN,QAAQ,EAAE;MACtBM,QAAQ,CAACN,QAAQ,GAAG,GAAG;IACzB;EACF;EAEA,OAAOM,QAAQ;AACjB;AACA,SAASS,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC/B,OAAOD,CAAC,CAAChB,QAAQ,KAAKiB,CAAC,CAACjB,QAAQ,IAAIgB,CAAC,CAACf,MAAM,KAAKgB,CAAC,CAAChB,MAAM,IAAIe,CAAC,CAACd,IAAI,KAAKe,CAAC,CAACf,IAAI,IAAIc,CAAC,CAACP,GAAG,KAAKQ,CAAC,CAACR,GAAG,IAAI1B,UAAU,CAACiC,CAAC,CAACR,KAAK,EAAES,CAAC,CAACT,KAAK,CAAC;AACnI;AAEA,SAASU,uBAAuBA,CAAA,EAAG;EACjC,IAAIC,MAAM,GAAG,IAAI;EAEjB,SAASC,SAASA,CAACC,UAAU,EAAE;IAC7BC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAACmC,MAAM,IAAI,IAAI,EAAE,8CAA8C,CAAC,GAAG,KAAK,CAAC;IACxHA,MAAM,GAAGE,UAAU;IACnB,OAAO,YAAY;MACjB,IAAIF,MAAM,KAAKE,UAAU,EAAEF,MAAM,GAAG,IAAI;IAC1C,CAAC;EACH;EAEA,SAASM,mBAAmBA,CAACnB,QAAQ,EAAEoB,MAAM,EAAEC,mBAAmB,EAAEC,QAAQ,EAAE;IAC5E;IACA;IACA;IACA,IAAIT,MAAM,IAAI,IAAI,EAAE;MAClB,IAAIU,MAAM,GAAG,OAAOV,MAAM,KAAK,UAAU,GAAGA,MAAM,CAACb,QAAQ,EAAEoB,MAAM,CAAC,GAAGP,MAAM;MAE7E,IAAI,OAAOU,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAI,OAAOF,mBAAmB,KAAK,UAAU,EAAE;UAC7CA,mBAAmB,CAACE,MAAM,EAAED,QAAQ,CAAC;QACvC,CAAC,MAAM;UACLN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAAC,KAAK,EAAE,iFAAiF,CAAC,GAAG,KAAK,CAAC;UAClJ4C,QAAQ,CAAC,IAAI,CAAC;QAChB;MACF,CAAC,MAAM;QACL;QACAA,QAAQ,CAACC,MAAM,KAAK,KAAK,CAAC;MAC5B;IACF,CAAC,MAAM;MACLD,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF;EAEA,IAAIE,SAAS,GAAG,EAAE;EAElB,SAASC,cAAcA,CAACC,EAAE,EAAE;IAC1B,IAAIC,QAAQ,GAAG,IAAI;IAEnB,SAASC,QAAQA,CAAA,EAAG;MAClB,IAAID,QAAQ,EAAED,EAAE,CAACG,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;IAC3C;IAEAN,SAAS,CAACO,IAAI,CAACH,QAAQ,CAAC;IACxB,OAAO,YAAY;MACjBD,QAAQ,GAAG,KAAK;MAChBH,SAAS,GAAGA,SAAS,CAACQ,MAAM,CAAC,UAAUC,IAAI,EAAE;QAC3C,OAAOA,IAAI,KAAKL,QAAQ;MAC1B,CAAC,CAAC;IACJ,CAAC;EACH;EAEA,SAASM,eAAeA,CAAA,EAAG;IACzB,KAAK,IAAIC,IAAI,GAAGL,SAAS,CAACzC,MAAM,EAAE+C,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGR,SAAS,CAACQ,IAAI,CAAC;IAC9B;IAEAd,SAAS,CAACe,OAAO,CAAC,UAAUX,QAAQ,EAAE;MACpC,OAAOA,QAAQ,CAACC,KAAK,CAAC,KAAK,CAAC,EAAEO,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA,OAAO;IACLtB,SAAS,EAAEA,SAAS;IACpBK,mBAAmB,EAAEA,mBAAmB;IACxCM,cAAc,EAAEA,cAAc;IAC9BS,eAAe,EAAEA;EACnB,CAAC;AACH;AAEA,IAAIM,SAAS,GAAG,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC;AACrG,SAASC,eAAeA,CAACC,OAAO,EAAEvB,QAAQ,EAAE;EAC1CA,QAAQ,CAACmB,MAAM,CAACK,OAAO,CAACD,OAAO,CAAC,CAAC,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,eAAeA,CAAA,EAAG;EACzB,IAAIC,EAAE,GAAGP,MAAM,CAACQ,SAAS,CAACC,SAAS;EACnC,IAAI,CAACF,EAAE,CAAC5D,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI4D,EAAE,CAAC5D,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK4D,EAAE,CAAC5D,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI4D,EAAE,CAAC5D,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI4D,EAAE,CAAC5D,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;EAClM,OAAOqD,MAAM,CAACU,OAAO,IAAI,WAAW,IAAIV,MAAM,CAACU,OAAO;AACxD;AACA;AACA;AACA;AACA;;AAEA,SAASC,4BAA4BA,CAAA,EAAG;EACtC,OAAOX,MAAM,CAACQ,SAAS,CAACC,SAAS,CAAC9D,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7D;AACA;AACA;AACA;;AAEA,SAASiE,gCAAgCA,CAAA,EAAG;EAC1C,OAAOZ,MAAM,CAACQ,SAAS,CAACC,SAAS,CAAC9D,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASkE,yBAAyBA,CAACC,KAAK,EAAE;EACxC,OAAOA,KAAK,CAACrD,KAAK,KAAKG,SAAS,IAAI4C,SAAS,CAACC,SAAS,CAAC9D,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjF;AAEA,IAAIoE,aAAa,GAAG,UAAU;AAC9B,IAAIC,eAAe,GAAG,YAAY;AAElC,SAASC,eAAeA,CAAA,EAAG;EACzB,IAAI;IACF,OAAOjB,MAAM,CAACU,OAAO,CAACjD,KAAK,IAAI,CAAC,CAAC;EACnC,CAAC,CAAC,OAAOK,CAAC,EAAE;IACV;IACA;IACA,OAAO,CAAC,CAAC;EACX;AACF;AACA;AACA;AACA;AACA;;AAGA,SAASoD,oBAAoBA,CAACC,KAAK,EAAE;EACnC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC,CAAC;EACZ;EAEA,CAACpB,SAAS,GAAGxB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvC,SAAS,CAAC,KAAK,EAAE,6BAA6B,CAAC,GAAGA,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EAChI,IAAIkF,aAAa,GAAGpB,MAAM,CAACU,OAAO;EAClC,IAAIW,aAAa,GAAGf,eAAe,CAAC,CAAC;EACrC,IAAIgB,uBAAuB,GAAG,CAACX,4BAA4B,CAAC,CAAC;EAC7D,IAAIY,MAAM,GAAGJ,KAAK;IACdK,mBAAmB,GAAGD,MAAM,CAACE,YAAY;IACzCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,mBAAmB;IAC3EE,qBAAqB,GAAGH,MAAM,CAAC3C,mBAAmB;IAClDA,mBAAmB,GAAG8C,qBAAqB,KAAK,KAAK,CAAC,GAAGvB,eAAe,GAAGuB,qBAAqB;IAChGC,gBAAgB,GAAGJ,MAAM,CAACK,SAAS;IACnCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,gBAAgB;EAClE,IAAIE,QAAQ,GAAGV,KAAK,CAACU,QAAQ,GAAG/E,kBAAkB,CAACX,eAAe,CAACgF,KAAK,CAACU,QAAQ,CAAC,CAAC,GAAG,EAAE;EAExF,SAASC,cAAcA,CAACC,YAAY,EAAE;IACpC,IAAIC,IAAI,GAAGD,YAAY,IAAI,CAAC,CAAC;MACzBrE,GAAG,GAAGsE,IAAI,CAACtE,GAAG;MACdD,KAAK,GAAGuE,IAAI,CAACvE,KAAK;IAEtB,IAAIwE,gBAAgB,GAAGjC,MAAM,CAACzC,QAAQ;MAClCN,QAAQ,GAAGgF,gBAAgB,CAAChF,QAAQ;MACpCC,MAAM,GAAG+E,gBAAgB,CAAC/E,MAAM;MAChCC,IAAI,GAAG8E,gBAAgB,CAAC9E,IAAI;IAChC,IAAIf,IAAI,GAAGa,QAAQ,GAAGC,MAAM,GAAGC,IAAI;IACnCoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAAC,CAAC4F,QAAQ,IAAIrF,WAAW,CAACJ,IAAI,EAAEyF,QAAQ,CAAC,EAAE,+EAA+E,GAAG,oCAAoC,GAAGzF,IAAI,GAAG,mBAAmB,GAAGyF,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;IACzQ,IAAIA,QAAQ,EAAEzF,IAAI,GAAGS,aAAa,CAACT,IAAI,EAAEyF,QAAQ,CAAC;IAClD,OAAOrE,cAAc,CAACpB,IAAI,EAAEqB,KAAK,EAAEC,GAAG,CAAC;EACzC;EAEA,SAASwE,SAASA,CAAA,EAAG;IACnB,OAAOC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC9F,MAAM,CAAC,CAAC,EAAEqF,SAAS,CAAC;EACxD;EAEA,IAAIU,iBAAiB,GAAGnE,uBAAuB,CAAC,CAAC;EAEjD,SAASoE,QAAQA,CAACC,SAAS,EAAE;IAC3B1G,QAAQ,CAAC4E,OAAO,EAAE8B,SAAS,CAAC;IAE5B9B,OAAO,CAAC9D,MAAM,GAAGwE,aAAa,CAACxE,MAAM;IACrC0F,iBAAiB,CAAC7C,eAAe,CAACiB,OAAO,CAACnD,QAAQ,EAAEmD,OAAO,CAAC/B,MAAM,CAAC;EACrE;EAEA,SAAS8D,cAAcA,CAAC3B,KAAK,EAAE;IAC7B;IACA,IAAID,yBAAyB,CAACC,KAAK,CAAC,EAAE;IACtC4B,SAAS,CAACZ,cAAc,CAAChB,KAAK,CAACrD,KAAK,CAAC,CAAC;EACxC;EAEA,SAASkF,gBAAgBA,CAAA,EAAG;IAC1BD,SAAS,CAACZ,cAAc,CAACb,eAAe,CAAC,CAAC,CAAC,CAAC;EAC9C;EAEA,IAAI2B,YAAY,GAAG,KAAK;EAExB,SAASF,SAASA,CAACnF,QAAQ,EAAE;IAC3B,IAAIqF,YAAY,EAAE;MAChBA,YAAY,GAAG,KAAK;MACpBL,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAI5D,MAAM,GAAG,KAAK;MAClB2D,iBAAiB,CAAC5D,mBAAmB,CAACnB,QAAQ,EAAEoB,MAAM,EAAEC,mBAAmB,EAAE,UAAUiE,EAAE,EAAE;QACzF,IAAIA,EAAE,EAAE;UACNN,QAAQ,CAAC;YACP5D,MAAM,EAAEA,MAAM;YACdpB,QAAQ,EAAEA;UACZ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLuF,SAAS,CAACvF,QAAQ,CAAC;QACrB;MACF,CAAC,CAAC;IACJ;EACF;EAEA,SAASuF,SAASA,CAACC,YAAY,EAAE;IAC/B,IAAIC,UAAU,GAAGtC,OAAO,CAACnD,QAAQ,CAAC,CAAC;IACnC;IACA;;IAEA,IAAI0F,OAAO,GAAGC,OAAO,CAACvG,OAAO,CAACqG,UAAU,CAACtF,GAAG,CAAC;IAC7C,IAAIuF,OAAO,KAAK,CAAC,CAAC,EAAEA,OAAO,GAAG,CAAC;IAC/B,IAAIE,SAAS,GAAGD,OAAO,CAACvG,OAAO,CAACoG,YAAY,CAACrF,GAAG,CAAC;IACjD,IAAIyF,SAAS,KAAK,CAAC,CAAC,EAAEA,SAAS,GAAG,CAAC;IACnC,IAAIC,KAAK,GAAGH,OAAO,GAAGE,SAAS;IAE/B,IAAIC,KAAK,EAAE;MACTR,YAAY,GAAG,IAAI;MACnBS,EAAE,CAACD,KAAK,CAAC;IACX;EACF;EAEA,IAAIE,eAAe,GAAGxB,cAAc,CAACb,eAAe,CAAC,CAAC,CAAC;EACvD,IAAIiC,OAAO,GAAG,CAACI,eAAe,CAAC5F,GAAG,CAAC,CAAC,CAAC;;EAErC,SAAS6F,UAAUA,CAAChG,QAAQ,EAAE;IAC5B,OAAOsE,QAAQ,GAAGvE,UAAU,CAACC,QAAQ,CAAC;EACxC;EAEA,SAAS+B,IAAIA,CAAClD,IAAI,EAAEqB,KAAK,EAAE;IACzBc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAAC,EAAE,OAAOG,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACqB,KAAK,KAAKG,SAAS,IAAIH,KAAK,KAAKG,SAAS,CAAC,EAAE,uEAAuE,GAAG,0EAA0E,CAAC,GAAG,KAAK,CAAC;IAC9R,IAAIe,MAAM,GAAG,MAAM;IACnB,IAAIpB,QAAQ,GAAGC,cAAc,CAACpB,IAAI,EAAEqB,KAAK,EAAEyE,SAAS,CAAC,CAAC,EAAExB,OAAO,CAACnD,QAAQ,CAAC;IACzE+E,iBAAiB,CAAC5D,mBAAmB,CAACnB,QAAQ,EAAEoB,MAAM,EAAEC,mBAAmB,EAAE,UAAUiE,EAAE,EAAE;MACzF,IAAI,CAACA,EAAE,EAAE;MACT,IAAIW,IAAI,GAAGD,UAAU,CAAChG,QAAQ,CAAC;MAC/B,IAAIG,GAAG,GAAGH,QAAQ,CAACG,GAAG;QAClBD,KAAK,GAAGF,QAAQ,CAACE,KAAK;MAE1B,IAAI4D,aAAa,EAAE;QACjBD,aAAa,CAACqC,SAAS,CAAC;UACtB/F,GAAG,EAAEA,GAAG;UACRD,KAAK,EAAEA;QACT,CAAC,EAAE,IAAI,EAAE+F,IAAI,CAAC;QAEd,IAAI/B,YAAY,EAAE;UAChBzB,MAAM,CAACzC,QAAQ,CAACiG,IAAI,GAAGA,IAAI;QAC7B,CAAC,MAAM;UACL,IAAIE,SAAS,GAAGR,OAAO,CAACvG,OAAO,CAAC+D,OAAO,CAACnD,QAAQ,CAACG,GAAG,CAAC;UACrD,IAAIiG,QAAQ,GAAGT,OAAO,CAACnG,KAAK,CAAC,CAAC,EAAE2G,SAAS,GAAG,CAAC,CAAC;UAC9CC,QAAQ,CAACrE,IAAI,CAAC/B,QAAQ,CAACG,GAAG,CAAC;UAC3BwF,OAAO,GAAGS,QAAQ;UAClBpB,QAAQ,CAAC;YACP5D,MAAM,EAAEA,MAAM;YACdpB,QAAQ,EAAEA;UACZ,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAACwB,KAAK,KAAKG,SAAS,EAAE,iFAAiF,CAAC,GAAG,KAAK,CAAC;QAChKoC,MAAM,CAACzC,QAAQ,CAACiG,IAAI,GAAGA,IAAI;MAC7B;IACF,CAAC,CAAC;EACJ;EAEA,SAASI,OAAOA,CAACxH,IAAI,EAAEqB,KAAK,EAAE;IAC5Bc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAAC,EAAE,OAAOG,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACqB,KAAK,KAAKG,SAAS,IAAIH,KAAK,KAAKG,SAAS,CAAC,EAAE,0EAA0E,GAAG,0EAA0E,CAAC,GAAG,KAAK,CAAC;IACjS,IAAIe,MAAM,GAAG,SAAS;IACtB,IAAIpB,QAAQ,GAAGC,cAAc,CAACpB,IAAI,EAAEqB,KAAK,EAAEyE,SAAS,CAAC,CAAC,EAAExB,OAAO,CAACnD,QAAQ,CAAC;IACzE+E,iBAAiB,CAAC5D,mBAAmB,CAACnB,QAAQ,EAAEoB,MAAM,EAAEC,mBAAmB,EAAE,UAAUiE,EAAE,EAAE;MACzF,IAAI,CAACA,EAAE,EAAE;MACT,IAAIW,IAAI,GAAGD,UAAU,CAAChG,QAAQ,CAAC;MAC/B,IAAIG,GAAG,GAAGH,QAAQ,CAACG,GAAG;QAClBD,KAAK,GAAGF,QAAQ,CAACE,KAAK;MAE1B,IAAI4D,aAAa,EAAE;QACjBD,aAAa,CAACyC,YAAY,CAAC;UACzBnG,GAAG,EAAEA,GAAG;UACRD,KAAK,EAAEA;QACT,CAAC,EAAE,IAAI,EAAE+F,IAAI,CAAC;QAEd,IAAI/B,YAAY,EAAE;UAChBzB,MAAM,CAACzC,QAAQ,CAACqG,OAAO,CAACJ,IAAI,CAAC;QAC/B,CAAC,MAAM;UACL,IAAIE,SAAS,GAAGR,OAAO,CAACvG,OAAO,CAAC+D,OAAO,CAACnD,QAAQ,CAACG,GAAG,CAAC;UACrD,IAAIgG,SAAS,KAAK,CAAC,CAAC,EAAER,OAAO,CAACQ,SAAS,CAAC,GAAGnG,QAAQ,CAACG,GAAG;UACvD6E,QAAQ,CAAC;YACP5D,MAAM,EAAEA,MAAM;YACdpB,QAAQ,EAAEA;UACZ,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAACwB,KAAK,KAAKG,SAAS,EAAE,oFAAoF,CAAC,GAAG,KAAK,CAAC;QACnKoC,MAAM,CAACzC,QAAQ,CAACqG,OAAO,CAACJ,IAAI,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ;EAEA,SAASH,EAAEA,CAACS,CAAC,EAAE;IACb1C,aAAa,CAACiC,EAAE,CAACS,CAAC,CAAC;EACrB;EAEA,SAASC,MAAMA,CAAA,EAAG;IAChBV,EAAE,CAAC,CAAC,CAAC,CAAC;EACR;EAEA,SAASW,SAASA,CAAA,EAAG;IACnBX,EAAE,CAAC,CAAC,CAAC;EACP;EAEA,IAAIY,aAAa,GAAG,CAAC;EAErB,SAASC,iBAAiBA,CAACd,KAAK,EAAE;IAChCa,aAAa,IAAIb,KAAK;IAEtB,IAAIa,aAAa,KAAK,CAAC,IAAIb,KAAK,KAAK,CAAC,EAAE;MACtCpD,MAAM,CAACmE,gBAAgB,CAACpD,aAAa,EAAE0B,cAAc,CAAC;MACtD,IAAInB,uBAAuB,EAAEtB,MAAM,CAACmE,gBAAgB,CAACnD,eAAe,EAAE2B,gBAAgB,CAAC;IACzF,CAAC,MAAM,IAAIsB,aAAa,KAAK,CAAC,EAAE;MAC9BjE,MAAM,CAACoE,mBAAmB,CAACrD,aAAa,EAAE0B,cAAc,CAAC;MACzD,IAAInB,uBAAuB,EAAEtB,MAAM,CAACoE,mBAAmB,CAACpD,eAAe,EAAE2B,gBAAgB,CAAC;IAC5F;EACF;EAEA,IAAI0B,SAAS,GAAG,KAAK;EAErB,SAASC,KAAKA,CAAClG,MAAM,EAAE;IACrB,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MACrBA,MAAM,GAAG,KAAK;IAChB;IAEA,IAAImG,OAAO,GAAGjC,iBAAiB,CAACjE,SAAS,CAACD,MAAM,CAAC;IAEjD,IAAI,CAACiG,SAAS,EAAE;MACdH,iBAAiB,CAAC,CAAC,CAAC;MACpBG,SAAS,GAAG,IAAI;IAClB;IAEA,OAAO,YAAY;MACjB,IAAIA,SAAS,EAAE;QACbA,SAAS,GAAG,KAAK;QACjBH,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACvB;MAEA,OAAOK,OAAO,CAAC,CAAC;IAClB,CAAC;EACH;EAEA,SAASC,MAAMA,CAACrF,QAAQ,EAAE;IACxB,IAAIsF,QAAQ,GAAGnC,iBAAiB,CAACtD,cAAc,CAACG,QAAQ,CAAC;IACzD+E,iBAAiB,CAAC,CAAC,CAAC;IACpB,OAAO,YAAY;MACjBA,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACrBO,QAAQ,CAAC,CAAC;IACZ,CAAC;EACH;EAEA,IAAI/D,OAAO,GAAG;IACZ9D,MAAM,EAAEwE,aAAa,CAACxE,MAAM;IAC5B+B,MAAM,EAAE,KAAK;IACbpB,QAAQ,EAAE+F,eAAe;IACzBC,UAAU,EAAEA,UAAU;IACtBjE,IAAI,EAAEA,IAAI;IACVsE,OAAO,EAAEA,OAAO;IAChBP,EAAE,EAAEA,EAAE;IACNU,MAAM,EAAEA,MAAM;IACdC,SAAS,EAAEA,SAAS;IACpBM,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA;EACV,CAAC;EACD,OAAO9D,OAAO;AAChB;AAEA,IAAIgE,iBAAiB,GAAG,YAAY;AACpC,IAAIC,cAAc,GAAG;EACnBC,QAAQ,EAAE;IACRC,UAAU,EAAE,SAASA,UAAUA,CAACzI,IAAI,EAAE;MACpC,OAAOA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGD,IAAI,GAAG,IAAI,GAAGE,iBAAiB,CAACF,IAAI,CAAC;IACvE,CAAC;IACD0I,UAAU,EAAE,SAASA,UAAUA,CAAC1I,IAAI,EAAE;MACpC,OAAOA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGD,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,GAAGH,IAAI;IACvD;EACF,CAAC;EACD2I,OAAO,EAAE;IACPF,UAAU,EAAEvI,iBAAiB;IAC7BwI,UAAU,EAAE3I;EACd,CAAC;EACD6I,KAAK,EAAE;IACLH,UAAU,EAAE1I,eAAe;IAC3B2I,UAAU,EAAE3I;EACd;AACF,CAAC;AAED,SAAS8I,SAASA,CAACC,GAAG,EAAE;EACtB,IAAI9H,SAAS,GAAG8H,GAAG,CAACvI,OAAO,CAAC,GAAG,CAAC;EAChC,OAAOS,SAAS,KAAK,CAAC,CAAC,GAAG8H,GAAG,GAAGA,GAAG,CAACnI,KAAK,CAAC,CAAC,EAAEK,SAAS,CAAC;AACzD;AAEA,SAAS+H,WAAWA,CAAA,EAAG;EACrB;EACA;EACA,IAAI3B,IAAI,GAAGxD,MAAM,CAACzC,QAAQ,CAACiG,IAAI;EAC/B,IAAIpG,SAAS,GAAGoG,IAAI,CAAC7G,OAAO,CAAC,GAAG,CAAC;EACjC,OAAOS,SAAS,KAAK,CAAC,CAAC,GAAG,EAAE,GAAGoG,IAAI,CAAC4B,SAAS,CAAChI,SAAS,GAAG,CAAC,CAAC;AAC9D;AAEA,SAASiI,YAAYA,CAACjJ,IAAI,EAAE;EAC1B4D,MAAM,CAACzC,QAAQ,CAACJ,IAAI,GAAGf,IAAI;AAC7B;AAEA,SAASkJ,eAAeA,CAAClJ,IAAI,EAAE;EAC7B4D,MAAM,CAACzC,QAAQ,CAACqG,OAAO,CAACqB,SAAS,CAACjF,MAAM,CAACzC,QAAQ,CAACiG,IAAI,CAAC,GAAG,GAAG,GAAGpH,IAAI,CAAC;AACvE;AAEA,SAASmJ,iBAAiBA,CAACpE,KAAK,EAAE;EAChC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC,CAAC;EACZ;EAEA,CAACpB,SAAS,GAAGxB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvC,SAAS,CAAC,KAAK,EAAE,0BAA0B,CAAC,GAAGA,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EAC7H,IAAIkF,aAAa,GAAGpB,MAAM,CAACU,OAAO;EAClC,IAAI8E,kBAAkB,GAAG5E,gCAAgC,CAAC,CAAC;EAC3D,IAAIW,MAAM,GAAGJ,KAAK;IACdO,qBAAqB,GAAGH,MAAM,CAAC3C,mBAAmB;IAClDA,mBAAmB,GAAG8C,qBAAqB,KAAK,KAAK,CAAC,GAAGvB,eAAe,GAAGuB,qBAAqB;IAChG+D,eAAe,GAAGlE,MAAM,CAACmE,QAAQ;IACjCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,eAAe;EACrE,IAAI5D,QAAQ,GAAGV,KAAK,CAACU,QAAQ,GAAG/E,kBAAkB,CAACX,eAAe,CAACgF,KAAK,CAACU,QAAQ,CAAC,CAAC,GAAG,EAAE;EACxF,IAAI8D,qBAAqB,GAAGhB,cAAc,CAACe,QAAQ,CAAC;IAChDb,UAAU,GAAGc,qBAAqB,CAACd,UAAU;IAC7CC,UAAU,GAAGa,qBAAqB,CAACb,UAAU;EAEjD,SAAShD,cAAcA,CAAA,EAAG;IACxB,IAAI1F,IAAI,GAAG0I,UAAU,CAACK,WAAW,CAAC,CAAC,CAAC;IACpC5G,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAAC,CAAC4F,QAAQ,IAAIrF,WAAW,CAACJ,IAAI,EAAEyF,QAAQ,CAAC,EAAE,+EAA+E,GAAG,oCAAoC,GAAGzF,IAAI,GAAG,mBAAmB,GAAGyF,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;IACzQ,IAAIA,QAAQ,EAAEzF,IAAI,GAAGS,aAAa,CAACT,IAAI,EAAEyF,QAAQ,CAAC;IAClD,OAAOrE,cAAc,CAACpB,IAAI,CAAC;EAC7B;EAEA,IAAIkG,iBAAiB,GAAGnE,uBAAuB,CAAC,CAAC;EAEjD,SAASoE,QAAQA,CAACC,SAAS,EAAE;IAC3B1G,QAAQ,CAAC4E,OAAO,EAAE8B,SAAS,CAAC;IAE5B9B,OAAO,CAAC9D,MAAM,GAAGwE,aAAa,CAACxE,MAAM;IACrC0F,iBAAiB,CAAC7C,eAAe,CAACiB,OAAO,CAACnD,QAAQ,EAAEmD,OAAO,CAAC/B,MAAM,CAAC;EACrE;EAEA,IAAIiE,YAAY,GAAG,KAAK;EACxB,IAAIgD,UAAU,GAAG,IAAI;EAErB,SAASC,oBAAoBA,CAAC5H,CAAC,EAAEC,CAAC,EAAE;IAClC,OAAOD,CAAC,CAAChB,QAAQ,KAAKiB,CAAC,CAACjB,QAAQ,IAAIgB,CAAC,CAACf,MAAM,KAAKgB,CAAC,CAAChB,MAAM,IAAIe,CAAC,CAACd,IAAI,KAAKe,CAAC,CAACf,IAAI;EAChF;EAEA,SAASwF,gBAAgBA,CAAA,EAAG;IAC1B,IAAIvG,IAAI,GAAG+I,WAAW,CAAC,CAAC;IACxB,IAAIW,WAAW,GAAGjB,UAAU,CAACzI,IAAI,CAAC;IAElC,IAAIA,IAAI,KAAK0J,WAAW,EAAE;MACxB;MACAR,eAAe,CAACQ,WAAW,CAAC;IAC9B,CAAC,MAAM;MACL,IAAIvI,QAAQ,GAAGuE,cAAc,CAAC,CAAC;MAC/B,IAAIiE,YAAY,GAAGrF,OAAO,CAACnD,QAAQ;MACnC,IAAI,CAACqF,YAAY,IAAIiD,oBAAoB,CAACE,YAAY,EAAExI,QAAQ,CAAC,EAAE,OAAO,CAAC;;MAE3E,IAAIqI,UAAU,KAAKtI,UAAU,CAACC,QAAQ,CAAC,EAAE,OAAO,CAAC;;MAEjDqI,UAAU,GAAG,IAAI;MACjBlD,SAAS,CAACnF,QAAQ,CAAC;IACrB;EACF;EAEA,SAASmF,SAASA,CAACnF,QAAQ,EAAE;IAC3B,IAAIqF,YAAY,EAAE;MAChBA,YAAY,GAAG,KAAK;MACpBL,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAI5D,MAAM,GAAG,KAAK;MAClB2D,iBAAiB,CAAC5D,mBAAmB,CAACnB,QAAQ,EAAEoB,MAAM,EAAEC,mBAAmB,EAAE,UAAUiE,EAAE,EAAE;QACzF,IAAIA,EAAE,EAAE;UACNN,QAAQ,CAAC;YACP5D,MAAM,EAAEA,MAAM;YACdpB,QAAQ,EAAEA;UACZ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLuF,SAAS,CAACvF,QAAQ,CAAC;QACrB;MACF,CAAC,CAAC;IACJ;EACF;EAEA,SAASuF,SAASA,CAACC,YAAY,EAAE;IAC/B,IAAIC,UAAU,GAAGtC,OAAO,CAACnD,QAAQ,CAAC,CAAC;IACnC;IACA;;IAEA,IAAI0F,OAAO,GAAG+C,QAAQ,CAACC,WAAW,CAAC3I,UAAU,CAAC0F,UAAU,CAAC,CAAC;IAC1D,IAAIC,OAAO,KAAK,CAAC,CAAC,EAAEA,OAAO,GAAG,CAAC;IAC/B,IAAIE,SAAS,GAAG6C,QAAQ,CAACC,WAAW,CAAC3I,UAAU,CAACyF,YAAY,CAAC,CAAC;IAC9D,IAAII,SAAS,KAAK,CAAC,CAAC,EAAEA,SAAS,GAAG,CAAC;IACnC,IAAIC,KAAK,GAAGH,OAAO,GAAGE,SAAS;IAE/B,IAAIC,KAAK,EAAE;MACTR,YAAY,GAAG,IAAI;MACnBS,EAAE,CAACD,KAAK,CAAC;IACX;EACF,CAAC,CAAC;;EAGF,IAAIhH,IAAI,GAAG+I,WAAW,CAAC,CAAC;EACxB,IAAIW,WAAW,GAAGjB,UAAU,CAACzI,IAAI,CAAC;EAClC,IAAIA,IAAI,KAAK0J,WAAW,EAAER,eAAe,CAACQ,WAAW,CAAC;EACtD,IAAIxC,eAAe,GAAGxB,cAAc,CAAC,CAAC;EACtC,IAAIkE,QAAQ,GAAG,CAAC1I,UAAU,CAACgG,eAAe,CAAC,CAAC,CAAC,CAAC;;EAE9C,SAASC,UAAUA,CAAChG,QAAQ,EAAE;IAC5B,IAAI2I,OAAO,GAAGjG,QAAQ,CAACkG,aAAa,CAAC,MAAM,CAAC;IAC5C,IAAI3C,IAAI,GAAG,EAAE;IAEb,IAAI0C,OAAO,IAAIA,OAAO,CAACE,YAAY,CAAC,MAAM,CAAC,EAAE;MAC3C5C,IAAI,GAAGyB,SAAS,CAACjF,MAAM,CAACzC,QAAQ,CAACiG,IAAI,CAAC;IACxC;IAEA,OAAOA,IAAI,GAAG,GAAG,GAAGqB,UAAU,CAAChD,QAAQ,GAAGvE,UAAU,CAACC,QAAQ,CAAC,CAAC;EACjE;EAEA,SAAS+B,IAAIA,CAAClD,IAAI,EAAEqB,KAAK,EAAE;IACzBc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAACwB,KAAK,KAAKG,SAAS,EAAE,+CAA+C,CAAC,GAAG,KAAK,CAAC;IAC9H,IAAIe,MAAM,GAAG,MAAM;IACnB,IAAIpB,QAAQ,GAAGC,cAAc,CAACpB,IAAI,EAAEwB,SAAS,EAAEA,SAAS,EAAE8C,OAAO,CAACnD,QAAQ,CAAC;IAC3E+E,iBAAiB,CAAC5D,mBAAmB,CAACnB,QAAQ,EAAEoB,MAAM,EAAEC,mBAAmB,EAAE,UAAUiE,EAAE,EAAE;MACzF,IAAI,CAACA,EAAE,EAAE;MACT,IAAIzG,IAAI,GAAGkB,UAAU,CAACC,QAAQ,CAAC;MAC/B,IAAIuI,WAAW,GAAGjB,UAAU,CAAChD,QAAQ,GAAGzF,IAAI,CAAC;MAC7C,IAAIiK,WAAW,GAAGlB,WAAW,CAAC,CAAC,KAAKW,WAAW;MAE/C,IAAIO,WAAW,EAAE;QACf;QACA;QACA;QACAT,UAAU,GAAGxJ,IAAI;QACjBiJ,YAAY,CAACS,WAAW,CAAC;QACzB,IAAIpC,SAAS,GAAGsC,QAAQ,CAACC,WAAW,CAAC3I,UAAU,CAACoD,OAAO,CAACnD,QAAQ,CAAC,CAAC;QAClE,IAAI+I,SAAS,GAAGN,QAAQ,CAACjJ,KAAK,CAAC,CAAC,EAAE2G,SAAS,GAAG,CAAC,CAAC;QAChD4C,SAAS,CAAChH,IAAI,CAAClD,IAAI,CAAC;QACpB4J,QAAQ,GAAGM,SAAS;QACpB/D,QAAQ,CAAC;UACP5D,MAAM,EAAEA,MAAM;UACdpB,QAAQ,EAAEA;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM;QACLgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAAC,KAAK,EAAE,4FAA4F,CAAC,GAAG,KAAK,CAAC;QAC7JsG,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;EACJ;EAEA,SAASqB,OAAOA,CAACxH,IAAI,EAAEqB,KAAK,EAAE;IAC5Bc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAACwB,KAAK,KAAKG,SAAS,EAAE,kDAAkD,CAAC,GAAG,KAAK,CAAC;IACjI,IAAIe,MAAM,GAAG,SAAS;IACtB,IAAIpB,QAAQ,GAAGC,cAAc,CAACpB,IAAI,EAAEwB,SAAS,EAAEA,SAAS,EAAE8C,OAAO,CAACnD,QAAQ,CAAC;IAC3E+E,iBAAiB,CAAC5D,mBAAmB,CAACnB,QAAQ,EAAEoB,MAAM,EAAEC,mBAAmB,EAAE,UAAUiE,EAAE,EAAE;MACzF,IAAI,CAACA,EAAE,EAAE;MACT,IAAIzG,IAAI,GAAGkB,UAAU,CAACC,QAAQ,CAAC;MAC/B,IAAIuI,WAAW,GAAGjB,UAAU,CAAChD,QAAQ,GAAGzF,IAAI,CAAC;MAC7C,IAAIiK,WAAW,GAAGlB,WAAW,CAAC,CAAC,KAAKW,WAAW;MAE/C,IAAIO,WAAW,EAAE;QACf;QACA;QACA;QACAT,UAAU,GAAGxJ,IAAI;QACjBkJ,eAAe,CAACQ,WAAW,CAAC;MAC9B;MAEA,IAAIpC,SAAS,GAAGsC,QAAQ,CAACrJ,OAAO,CAACW,UAAU,CAACoD,OAAO,CAACnD,QAAQ,CAAC,CAAC;MAC9D,IAAImG,SAAS,KAAK,CAAC,CAAC,EAAEsC,QAAQ,CAACtC,SAAS,CAAC,GAAGtH,IAAI;MAChDmG,QAAQ,CAAC;QACP5D,MAAM,EAAEA,MAAM;QACdpB,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA,SAAS8F,EAAEA,CAACS,CAAC,EAAE;IACbvF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAACuJ,kBAAkB,EAAE,8DAA8D,CAAC,GAAG,KAAK,CAAC;IAC5IpE,aAAa,CAACiC,EAAE,CAACS,CAAC,CAAC;EACrB;EAEA,SAASC,MAAMA,CAAA,EAAG;IAChBV,EAAE,CAAC,CAAC,CAAC,CAAC;EACR;EAEA,SAASW,SAASA,CAAA,EAAG;IACnBX,EAAE,CAAC,CAAC,CAAC;EACP;EAEA,IAAIY,aAAa,GAAG,CAAC;EAErB,SAASC,iBAAiBA,CAACd,KAAK,EAAE;IAChCa,aAAa,IAAIb,KAAK;IAEtB,IAAIa,aAAa,KAAK,CAAC,IAAIb,KAAK,KAAK,CAAC,EAAE;MACtCpD,MAAM,CAACmE,gBAAgB,CAACO,iBAAiB,EAAE/B,gBAAgB,CAAC;IAC9D,CAAC,MAAM,IAAIsB,aAAa,KAAK,CAAC,EAAE;MAC9BjE,MAAM,CAACoE,mBAAmB,CAACM,iBAAiB,EAAE/B,gBAAgB,CAAC;IACjE;EACF;EAEA,IAAI0B,SAAS,GAAG,KAAK;EAErB,SAASC,KAAKA,CAAClG,MAAM,EAAE;IACrB,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MACrBA,MAAM,GAAG,KAAK;IAChB;IAEA,IAAImG,OAAO,GAAGjC,iBAAiB,CAACjE,SAAS,CAACD,MAAM,CAAC;IAEjD,IAAI,CAACiG,SAAS,EAAE;MACdH,iBAAiB,CAAC,CAAC,CAAC;MACpBG,SAAS,GAAG,IAAI;IAClB;IAEA,OAAO,YAAY;MACjB,IAAIA,SAAS,EAAE;QACbA,SAAS,GAAG,KAAK;QACjBH,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACvB;MAEA,OAAOK,OAAO,CAAC,CAAC;IAClB,CAAC;EACH;EAEA,SAASC,MAAMA,CAACrF,QAAQ,EAAE;IACxB,IAAIsF,QAAQ,GAAGnC,iBAAiB,CAACtD,cAAc,CAACG,QAAQ,CAAC;IACzD+E,iBAAiB,CAAC,CAAC,CAAC;IACpB,OAAO,YAAY;MACjBA,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACrBO,QAAQ,CAAC,CAAC;IACZ,CAAC;EACH;EAEA,IAAI/D,OAAO,GAAG;IACZ9D,MAAM,EAAEwE,aAAa,CAACxE,MAAM;IAC5B+B,MAAM,EAAE,KAAK;IACbpB,QAAQ,EAAE+F,eAAe;IACzBC,UAAU,EAAEA,UAAU;IACtBjE,IAAI,EAAEA,IAAI;IACVsE,OAAO,EAAEA,OAAO;IAChBP,EAAE,EAAEA,EAAE;IACNU,MAAM,EAAEA,MAAM;IACdC,SAAS,EAAEA,SAAS;IACpBM,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA;EACV,CAAC;EACD,OAAO9D,OAAO;AAChB;AAEA,SAAS6F,KAAKA,CAACzC,CAAC,EAAE0C,UAAU,EAAEC,UAAU,EAAE;EACxC,OAAOtE,IAAI,CAACuE,GAAG,CAACvE,IAAI,CAACwE,GAAG,CAAC7C,CAAC,EAAE0C,UAAU,CAAC,EAAEC,UAAU,CAAC;AACtD;AACA;AACA;AACA;;AAGA,SAASG,mBAAmBA,CAACzF,KAAK,EAAE;EAClC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC,CAAC;EACZ;EAEA,IAAII,MAAM,GAAGJ,KAAK;IACdvC,mBAAmB,GAAG2C,MAAM,CAAC3C,mBAAmB;IAChDiI,qBAAqB,GAAGtF,MAAM,CAACuF,cAAc;IAC7CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IACjFE,mBAAmB,GAAGxF,MAAM,CAACyF,YAAY;IACzCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,mBAAmB;IACvEpF,gBAAgB,GAAGJ,MAAM,CAACK,SAAS;IACnCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,gBAAgB;EAClE,IAAIW,iBAAiB,GAAGnE,uBAAuB,CAAC,CAAC;EAEjD,SAASoE,QAAQA,CAACC,SAAS,EAAE;IAC3B1G,QAAQ,CAAC4E,OAAO,EAAE8B,SAAS,CAAC;IAE5B9B,OAAO,CAAC9D,MAAM,GAAG8D,OAAO,CAACuG,OAAO,CAACrK,MAAM;IACvC0F,iBAAiB,CAAC7C,eAAe,CAACiB,OAAO,CAACnD,QAAQ,EAAEmD,OAAO,CAAC/B,MAAM,CAAC;EACrE;EAEA,SAASuD,SAASA,CAAA,EAAG;IACnB,OAAOC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC9F,MAAM,CAAC,CAAC,EAAEqF,SAAS,CAAC;EACxD;EAEA,IAAIsF,KAAK,GAAGX,KAAK,CAACS,YAAY,EAAE,CAAC,EAAEF,cAAc,CAAClK,MAAM,GAAG,CAAC,CAAC;EAC7D,IAAIqK,OAAO,GAAGH,cAAc,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE;IAChD,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAG5J,cAAc,CAAC4J,KAAK,EAAExJ,SAAS,EAAEsE,SAAS,CAAC,CAAC,CAAC,GAAG1E,cAAc,CAAC4J,KAAK,EAAExJ,SAAS,EAAEwJ,KAAK,CAAC1J,GAAG,IAAIwE,SAAS,CAAC,CAAC,CAAC;EAC/I,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIqB,UAAU,GAAGjG,UAAU;EAE3B,SAASgC,IAAIA,CAAClD,IAAI,EAAEqB,KAAK,EAAE;IACzBc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAAC,EAAE,OAAOG,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACqB,KAAK,KAAKG,SAAS,IAAIH,KAAK,KAAKG,SAAS,CAAC,EAAE,uEAAuE,GAAG,0EAA0E,CAAC,GAAG,KAAK,CAAC;IAC9R,IAAIe,MAAM,GAAG,MAAM;IACnB,IAAIpB,QAAQ,GAAGC,cAAc,CAACpB,IAAI,EAAEqB,KAAK,EAAEyE,SAAS,CAAC,CAAC,EAAExB,OAAO,CAACnD,QAAQ,CAAC;IACzE+E,iBAAiB,CAAC5D,mBAAmB,CAACnB,QAAQ,EAAEoB,MAAM,EAAEC,mBAAmB,EAAE,UAAUiE,EAAE,EAAE;MACzF,IAAI,CAACA,EAAE,EAAE;MACT,IAAIa,SAAS,GAAGhD,OAAO,CAACwG,KAAK;MAC7B,IAAIG,SAAS,GAAG3D,SAAS,GAAG,CAAC;MAC7B,IAAI4D,WAAW,GAAG5G,OAAO,CAACuG,OAAO,CAAClK,KAAK,CAAC,CAAC,CAAC;MAE1C,IAAIuK,WAAW,CAAC1K,MAAM,GAAGyK,SAAS,EAAE;QAClCC,WAAW,CAACC,MAAM,CAACF,SAAS,EAAEC,WAAW,CAAC1K,MAAM,GAAGyK,SAAS,EAAE9J,QAAQ,CAAC;MACzE,CAAC,MAAM;QACL+J,WAAW,CAAChI,IAAI,CAAC/B,QAAQ,CAAC;MAC5B;MAEAgF,QAAQ,CAAC;QACP5D,MAAM,EAAEA,MAAM;QACdpB,QAAQ,EAAEA,QAAQ;QAClB2J,KAAK,EAAEG,SAAS;QAChBJ,OAAO,EAAEK;MACX,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA,SAAS1D,OAAOA,CAACxH,IAAI,EAAEqB,KAAK,EAAE;IAC5Bc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,OAAO,CAAC,EAAE,OAAOG,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACqB,KAAK,KAAKG,SAAS,IAAIH,KAAK,KAAKG,SAAS,CAAC,EAAE,0EAA0E,GAAG,0EAA0E,CAAC,GAAG,KAAK,CAAC;IACjS,IAAIe,MAAM,GAAG,SAAS;IACtB,IAAIpB,QAAQ,GAAGC,cAAc,CAACpB,IAAI,EAAEqB,KAAK,EAAEyE,SAAS,CAAC,CAAC,EAAExB,OAAO,CAACnD,QAAQ,CAAC;IACzE+E,iBAAiB,CAAC5D,mBAAmB,CAACnB,QAAQ,EAAEoB,MAAM,EAAEC,mBAAmB,EAAE,UAAUiE,EAAE,EAAE;MACzF,IAAI,CAACA,EAAE,EAAE;MACTnC,OAAO,CAACuG,OAAO,CAACvG,OAAO,CAACwG,KAAK,CAAC,GAAG3J,QAAQ;MACzCgF,QAAQ,CAAC;QACP5D,MAAM,EAAEA,MAAM;QACdpB,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA,SAAS8F,EAAEA,CAACS,CAAC,EAAE;IACb,IAAIuD,SAAS,GAAGd,KAAK,CAAC7F,OAAO,CAACwG,KAAK,GAAGpD,CAAC,EAAE,CAAC,EAAEpD,OAAO,CAACuG,OAAO,CAACrK,MAAM,GAAG,CAAC,CAAC;IACvE,IAAI+B,MAAM,GAAG,KAAK;IAClB,IAAIpB,QAAQ,GAAGmD,OAAO,CAACuG,OAAO,CAACI,SAAS,CAAC;IACzC/E,iBAAiB,CAAC5D,mBAAmB,CAACnB,QAAQ,EAAEoB,MAAM,EAAEC,mBAAmB,EAAE,UAAUiE,EAAE,EAAE;MACzF,IAAIA,EAAE,EAAE;QACNN,QAAQ,CAAC;UACP5D,MAAM,EAAEA,MAAM;UACdpB,QAAQ,EAAEA,QAAQ;UAClB2J,KAAK,EAAEG;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;QACA9E,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;EACJ;EAEA,SAASwB,MAAMA,CAAA,EAAG;IAChBV,EAAE,CAAC,CAAC,CAAC,CAAC;EACR;EAEA,SAASW,SAASA,CAAA,EAAG;IACnBX,EAAE,CAAC,CAAC,CAAC;EACP;EAEA,SAASmE,KAAKA,CAAC1D,CAAC,EAAE;IAChB,IAAIuD,SAAS,GAAG3G,OAAO,CAACwG,KAAK,GAAGpD,CAAC;IACjC,OAAOuD,SAAS,IAAI,CAAC,IAAIA,SAAS,GAAG3G,OAAO,CAACuG,OAAO,CAACrK,MAAM;EAC7D;EAEA,SAAS0H,KAAKA,CAAClG,MAAM,EAAE;IACrB,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MACrBA,MAAM,GAAG,KAAK;IAChB;IAEA,OAAOkE,iBAAiB,CAACjE,SAAS,CAACD,MAAM,CAAC;EAC5C;EAEA,SAASoG,MAAMA,CAACrF,QAAQ,EAAE;IACxB,OAAOmD,iBAAiB,CAACtD,cAAc,CAACG,QAAQ,CAAC;EACnD;EAEA,IAAIuB,OAAO,GAAG;IACZ9D,MAAM,EAAEqK,OAAO,CAACrK,MAAM;IACtB+B,MAAM,EAAE,KAAK;IACbpB,QAAQ,EAAE0J,OAAO,CAACC,KAAK,CAAC;IACxBA,KAAK,EAAEA,KAAK;IACZD,OAAO,EAAEA,OAAO;IAChB1D,UAAU,EAAEA,UAAU;IACtBjE,IAAI,EAAEA,IAAI;IACVsE,OAAO,EAAEA,OAAO;IAChBP,EAAE,EAAEA,EAAE;IACNU,MAAM,EAAEA,MAAM;IACdC,SAAS,EAAEA,SAAS;IACpBwD,KAAK,EAAEA,KAAK;IACZlD,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA;EACV,CAAC;EACD,OAAO9D,OAAO;AAChB;AAEA,SAASQ,oBAAoB,EAAEqE,iBAAiB,EAAEqB,mBAAmB,EAAEpJ,cAAc,EAAEQ,iBAAiB,EAAEhB,SAAS,EAAEM,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}