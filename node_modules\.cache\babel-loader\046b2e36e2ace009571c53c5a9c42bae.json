{"ast": null, "code": "const safeThis = require('./utils/safeThis');\nif (process.env.NODE_ENV !== 'production' && typeof safeThis !== 'undefined') {\n  // Only inject the runtime if it hasn't been injected\n  if (!safeThis.__reactRefreshInjected) {\n    const RefreshRuntime = require('react-refresh/runtime');\n    // Inject refresh runtime into global scope\n    RefreshRuntime.injectIntoGlobalHook(safeThis);\n\n    // Mark the runtime as injected to prevent double-injection\n    safeThis.__reactRefreshInjected = true;\n  }\n}", "map": {"version": 3, "names": ["safeThis", "require", "process", "env", "NODE_ENV", "__reactRefreshInjected", "RefreshRuntime", "injectIntoGlobalHook"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/@pmmmwh/react-refresh-webpack-plugin/client/ReactRefreshEntry.js"], "sourcesContent": ["const safeThis = require('./utils/safeThis');\n\nif (process.env.NODE_ENV !== 'production' && typeof safeThis !== 'undefined') {\n  // Only inject the runtime if it hasn't been injected\n  if (!safeThis.__reactRefreshInjected) {\n    const RefreshRuntime = require('react-refresh/runtime');\n    // Inject refresh runtime into global scope\n    RefreshRuntime.injectIntoGlobalHook(safeThis);\n\n    // Mark the runtime as injected to prevent double-injection\n    safeThis.__reactRefreshInjected = true;\n  }\n}\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE5C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAOJ,QAAQ,KAAK,WAAW,EAAE;EAC5E;EACA,IAAI,CAACA,QAAQ,CAACK,sBAAsB,EAAE;IACpC,MAAMC,cAAc,GAAGL,OAAO,CAAC,uBAAuB,CAAC;IACvD;IACAK,cAAc,CAACC,oBAAoB,CAACP,QAAQ,CAAC;;IAE7C;IACAA,QAAQ,CAACK,sBAAsB,GAAG,IAAI;EACxC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script"}