{"ast": null, "code": "import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\nvar _socialIcons_dribbble = register(\"dribbble\", {\n  \"color\": \"#ea4c89\",\n  \"path\": \"M34.3 34.3c-7.7 2.7-10.5 8-10.7 8.5 2.3 1.8 5.2 2.9 8.4 2.9 1.9 0 3.7-.4 5.3-1.1-.2-1.2-1-5.4-3-10.3.1-.1.1 0 0 0m-3-6.7c-2.3-4-4.7-7.4-5.1-7.9-3.8 1.8-6.7 5.3-7.6 9.6.6-.1 6.3 0 12.7-1.7m1.7 4.5c.2-.1.4-.1.5-.2-.3-.8-.7-1.6-1.1-2.3-6.8 2-13.4 2-14 1.9v.4c0 3.5 1.3 6.7 3.5 9.1.3-.4 4-6.6 11.1-8.9m8.1-10.3c-2.4-2.1-5.6-3.4-9.1-3.4-1.1 0-2.2.1-3.2.4.4.5 2.9 3.9 5.1 8 4.9-1.9 6.9-4.7 7.2-5m-6.2 7c.3.7.6 1.3.9 2 .1.2.2.5.3.7 4.5-.6 9.1.3 9.5.4 0-3.2-1.2-6.2-3.1-8.5-.2.4-2.5 3.3-7.6 5.4m2.1 4.8c1.8 4.9 2.5 8.9 2.7 9.7 3.1-2.1 5.2-5.4 5.9-9.2-.6-.1-4.3-1.2-8.6-.5M0 0v64h64V0zm32 48c-8.8 0-16-7.2-16-16s7.2-16 16-16 16 7.2 16 16-7.2 16-16 16\"\n});\nexport { _socialIcons_dribbble as default };", "map": {"version": 3, "names": ["register", "_socialIcons_dribbble", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/react-social-icons/dist/icons/dribbble.js"], "sourcesContent": ["import { register } from '../component.js';\nimport 'react';\nimport 'react/jsx-runtime';\n\nvar _socialIcons_dribbble = register(\"dribbble\", {\"color\":\"#ea4c89\",\"path\":\"M34.3 34.3c-7.7 2.7-10.5 8-10.7 8.5 2.3 1.8 5.2 2.9 8.4 2.9 1.9 0 3.7-.4 5.3-1.1-.2-1.2-1-5.4-3-10.3.1-.1.1 0 0 0m-3-6.7c-2.3-4-4.7-7.4-5.1-7.9-3.8 1.8-6.7 5.3-7.6 9.6.6-.1 6.3 0 12.7-1.7m1.7 4.5c.2-.1.4-.1.5-.2-.3-.8-.7-1.6-1.1-2.3-6.8 2-13.4 2-14 1.9v.4c0 3.5 1.3 6.7 3.5 9.1.3-.4 4-6.6 11.1-8.9m8.1-10.3c-2.4-2.1-5.6-3.4-9.1-3.4-1.1 0-2.2.1-3.2.4.4.5 2.9 3.9 5.1 8 4.9-1.9 6.9-4.7 7.2-5m-6.2 7c.3.7.6 1.3.9 2 .1.2.2.5.3.7 4.5-.6 9.1.3 9.5.4 0-3.2-1.2-6.2-3.1-8.5-.2.4-2.5 3.3-7.6 5.4m2.1 4.8c1.8 4.9 2.5 8.9 2.7 9.7 3.1-2.1 5.2-5.4 5.9-9.2-.6-.1-4.3-1.2-8.6-.5M0 0v64h64V0zm32 48c-8.8 0-16-7.2-16-16s7.2-16 16-16 16 7.2 16 16-7.2 16-16 16\"});\n\nexport { _socialIcons_dribbble as default };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,OAAO;AACd,OAAO,mBAAmB;AAE1B,IAAIC,qBAAqB,GAAGD,QAAQ,CAAC,UAAU,EAAE;EAAC,OAAO,EAAC,SAAS;EAAC,MAAM,EAAC;AAAmoB,CAAC,CAAC;AAEhtB,SAASC,qBAAqB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}