{"ast": null, "code": "const _excluded = [\"as\", \"onSelect\", \"activeKey\", \"role\", \"onKeyDown\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport qsa from 'dom-helpers/querySelectorAll';\nimport * as React from 'react';\nimport { useContext, useEffect, useRef } from 'react';\nimport useForceUpdate from '@restart/hooks/useForceUpdate';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport NavContext from './NavContext';\nimport SelectableContext, { makeEventKey } from './SelectableContext';\nimport TabContext from './TabContext';\nimport { dataAttr, dataProp } from './DataKey';\nimport NavItem from './NavItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nconst noop = () => {};\nconst EVENT_KEY_ATTR = dataAttr('event-key');\nconst Nav = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n      as: Component = 'div',\n      onSelect,\n      activeKey,\n      role,\n      onKeyDown\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  // A ref and forceUpdate for refocus, b/c we only want to trigger when needed\n  // and don't want to reset the set in the effect\n  const forceUpdate = useForceUpdate();\n  const needsRefocusRef = useRef(false);\n  const parentOnSelect = useContext(SelectableContext);\n  const tabContext = useContext(TabContext);\n  let getControlledId, getControllerId;\n  if (tabContext) {\n    role = role || 'tablist';\n    activeKey = tabContext.activeKey;\n    // TODO: do we need to duplicate these?\n    getControlledId = tabContext.getControlledId;\n    getControllerId = tabContext.getControllerId;\n  }\n  const listNode = useRef(null);\n  const getNextActiveTab = offset => {\n    const currentListNode = listNode.current;\n    if (!currentListNode) return null;\n    const items = qsa(currentListNode, `[${EVENT_KEY_ATTR}]:not([aria-disabled=true])`);\n    const activeChild = currentListNode.querySelector('[aria-selected=true]');\n    if (!activeChild || activeChild !== document.activeElement) return null;\n    const index = items.indexOf(activeChild);\n    if (index === -1) return null;\n    let nextIndex = index + offset;\n    if (nextIndex >= items.length) nextIndex = 0;\n    if (nextIndex < 0) nextIndex = items.length - 1;\n    return items[nextIndex];\n  };\n  const handleSelect = (key, event) => {\n    if (key == null) return;\n    onSelect == null ? void 0 : onSelect(key, event);\n    parentOnSelect == null ? void 0 : parentOnSelect(key, event);\n  };\n  const handleKeyDown = event => {\n    onKeyDown == null ? void 0 : onKeyDown(event);\n    if (!tabContext) {\n      return;\n    }\n    let nextActiveChild;\n    switch (event.key) {\n      case 'ArrowLeft':\n      case 'ArrowUp':\n        nextActiveChild = getNextActiveTab(-1);\n        break;\n      case 'ArrowRight':\n      case 'ArrowDown':\n        nextActiveChild = getNextActiveTab(1);\n        break;\n      default:\n        return;\n    }\n    if (!nextActiveChild) return;\n    event.preventDefault();\n    handleSelect(nextActiveChild.dataset[dataProp('EventKey')] || null, event);\n    needsRefocusRef.current = true;\n    forceUpdate();\n  };\n  useEffect(() => {\n    if (listNode.current && needsRefocusRef.current) {\n      const activeChild = listNode.current.querySelector(`[${EVENT_KEY_ATTR}][aria-selected=true]`);\n      activeChild == null ? void 0 : activeChild.focus();\n    }\n    needsRefocusRef.current = false;\n  });\n  const mergedRef = useMergedRefs(ref, listNode);\n  return /*#__PURE__*/_jsx(SelectableContext.Provider, {\n    value: handleSelect,\n    children: /*#__PURE__*/_jsx(NavContext.Provider, {\n      value: {\n        role,\n        // used by NavLink to determine it's role\n        activeKey: makeEventKey(activeKey),\n        getControlledId: getControlledId || noop,\n        getControllerId: getControllerId || noop\n      },\n      children: /*#__PURE__*/_jsx(Component, Object.assign({}, props, {\n        onKeyDown: handleKeyDown,\n        ref: mergedRef,\n        role: role\n      }))\n    })\n  });\n});\nNav.displayName = 'Nav';\nexport default Object.assign(Nav, {\n  Item: NavItem\n});", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "qsa", "React", "useContext", "useEffect", "useRef", "useForceUpdate", "useMergedRefs", "NavContext", "SelectableContext", "makeEventKey", "TabContext", "dataAttr", "dataProp", "NavItem", "jsx", "_jsx", "noop", "EVENT_KEY_ATTR", "Nav", "forwardRef", "_ref", "ref", "as", "Component", "onSelect", "active<PERSON><PERSON>", "role", "onKeyDown", "props", "forceUpdate", "needsRefocusRef", "parentOnSelect", "tabContext", "getControlledId", "getControllerId", "listNode", "getNextActiveTab", "offset", "currentListNode", "current", "items", "activeChild", "querySelector", "document", "activeElement", "index", "nextIndex", "length", "handleSelect", "key", "event", "handleKeyDown", "nextActiveChild", "preventDefault", "dataset", "focus", "mergedRef", "Provider", "value", "children", "Object", "assign", "displayName", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/coding/personal-e-portfolio/node_modules/@restart/ui/esm/Nav.js"], "sourcesContent": ["const _excluded = [\"as\", \"onSelect\", \"activeKey\", \"role\", \"onKeyDown\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nimport qsa from 'dom-helpers/querySelectorAll';\nimport * as React from 'react';\nimport { useContext, useEffect, useRef } from 'react';\nimport useForceUpdate from '@restart/hooks/useForceUpdate';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport NavContext from './NavContext';\nimport SelectableContext, { makeEventKey } from './SelectableContext';\nimport TabContext from './TabContext';\nimport { dataAttr, dataProp } from './DataKey';\nimport NavItem from './NavItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nconst noop = () => {};\nconst EVENT_KEY_ATTR = dataAttr('event-key');\nconst Nav = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n      as: Component = 'div',\n      onSelect,\n      activeKey,\n      role,\n      onKeyDown\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  // A ref and forceUpdate for refocus, b/c we only want to trigger when needed\n  // and don't want to reset the set in the effect\n  const forceUpdate = useForceUpdate();\n  const needsRefocusRef = useRef(false);\n  const parentOnSelect = useContext(SelectableContext);\n  const tabContext = useContext(TabContext);\n  let getControlledId, getControllerId;\n  if (tabContext) {\n    role = role || 'tablist';\n    activeKey = tabContext.activeKey;\n    // TODO: do we need to duplicate these?\n    getControlledId = tabContext.getControlledId;\n    getControllerId = tabContext.getControllerId;\n  }\n  const listNode = useRef(null);\n  const getNextActiveTab = offset => {\n    const currentListNode = listNode.current;\n    if (!currentListNode) return null;\n    const items = qsa(currentListNode, `[${EVENT_KEY_ATTR}]:not([aria-disabled=true])`);\n    const activeChild = currentListNode.querySelector('[aria-selected=true]');\n    if (!activeChild || activeChild !== document.activeElement) return null;\n    const index = items.indexOf(activeChild);\n    if (index === -1) return null;\n    let nextIndex = index + offset;\n    if (nextIndex >= items.length) nextIndex = 0;\n    if (nextIndex < 0) nextIndex = items.length - 1;\n    return items[nextIndex];\n  };\n  const handleSelect = (key, event) => {\n    if (key == null) return;\n    onSelect == null ? void 0 : onSelect(key, event);\n    parentOnSelect == null ? void 0 : parentOnSelect(key, event);\n  };\n  const handleKeyDown = event => {\n    onKeyDown == null ? void 0 : onKeyDown(event);\n    if (!tabContext) {\n      return;\n    }\n    let nextActiveChild;\n    switch (event.key) {\n      case 'ArrowLeft':\n      case 'ArrowUp':\n        nextActiveChild = getNextActiveTab(-1);\n        break;\n      case 'ArrowRight':\n      case 'ArrowDown':\n        nextActiveChild = getNextActiveTab(1);\n        break;\n      default:\n        return;\n    }\n    if (!nextActiveChild) return;\n    event.preventDefault();\n    handleSelect(nextActiveChild.dataset[dataProp('EventKey')] || null, event);\n    needsRefocusRef.current = true;\n    forceUpdate();\n  };\n  useEffect(() => {\n    if (listNode.current && needsRefocusRef.current) {\n      const activeChild = listNode.current.querySelector(`[${EVENT_KEY_ATTR}][aria-selected=true]`);\n      activeChild == null ? void 0 : activeChild.focus();\n    }\n    needsRefocusRef.current = false;\n  });\n  const mergedRef = useMergedRefs(ref, listNode);\n  return /*#__PURE__*/_jsx(SelectableContext.Provider, {\n    value: handleSelect,\n    children: /*#__PURE__*/_jsx(NavContext.Provider, {\n      value: {\n        role,\n        // used by NavLink to determine it's role\n        activeKey: makeEventKey(activeKey),\n        getControlledId: getControlledId || noop,\n        getControllerId: getControllerId || noop\n      },\n      children: /*#__PURE__*/_jsx(Component, Object.assign({}, props, {\n        onKeyDown: handleKeyDown,\n        ref: mergedRef,\n        role: role\n      }))\n    })\n  });\n});\nNav.displayName = 'Nav';\nexport default Object.assign(Nav, {\n  Item: NavItem\n});"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,CAAC;AACtE,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IAAE,IAAIF,CAAC,CAACK,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,EAAE;IAAUD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACpM,OAAOK,GAAG,MAAM,8BAA8B;AAC9C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACrD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,iBAAiB,IAAIC,YAAY,QAAQ,qBAAqB;AACrE,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,WAAW;AAC9C,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB,MAAMC,cAAc,GAAGN,QAAQ,CAAC,WAAW,CAAC;AAC5C,MAAMO,GAAG,GAAG,aAAajB,KAAK,CAACkB,UAAU,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;EACvD,IAAI;MACA;MACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;MACrBC,QAAQ;MACRC,SAAS;MACTC,IAAI;MACJC;IACF,CAAC,GAAGP,IAAI;IACRQ,KAAK,GAAGpC,6BAA6B,CAAC4B,IAAI,EAAE7B,SAAS,CAAC;EACxD;EACA;EACA,MAAMsC,WAAW,GAAGxB,cAAc,CAAC,CAAC;EACpC,MAAMyB,eAAe,GAAG1B,MAAM,CAAC,KAAK,CAAC;EACrC,MAAM2B,cAAc,GAAG7B,UAAU,CAACM,iBAAiB,CAAC;EACpD,MAAMwB,UAAU,GAAG9B,UAAU,CAACQ,UAAU,CAAC;EACzC,IAAIuB,eAAe,EAAEC,eAAe;EACpC,IAAIF,UAAU,EAAE;IACdN,IAAI,GAAGA,IAAI,IAAI,SAAS;IACxBD,SAAS,GAAGO,UAAU,CAACP,SAAS;IAChC;IACAQ,eAAe,GAAGD,UAAU,CAACC,eAAe;IAC5CC,eAAe,GAAGF,UAAU,CAACE,eAAe;EAC9C;EACA,MAAMC,QAAQ,GAAG/B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMgC,gBAAgB,GAAGC,MAAM,IAAI;IACjC,MAAMC,eAAe,GAAGH,QAAQ,CAACI,OAAO;IACxC,IAAI,CAACD,eAAe,EAAE,OAAO,IAAI;IACjC,MAAME,KAAK,GAAGxC,GAAG,CAACsC,eAAe,EAAE,IAAIrB,cAAc,6BAA6B,CAAC;IACnF,MAAMwB,WAAW,GAAGH,eAAe,CAACI,aAAa,CAAC,sBAAsB,CAAC;IACzE,IAAI,CAACD,WAAW,IAAIA,WAAW,KAAKE,QAAQ,CAACC,aAAa,EAAE,OAAO,IAAI;IACvE,MAAMC,KAAK,GAAGL,KAAK,CAACzC,OAAO,CAAC0C,WAAW,CAAC;IACxC,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI;IAC7B,IAAIC,SAAS,GAAGD,KAAK,GAAGR,MAAM;IAC9B,IAAIS,SAAS,IAAIN,KAAK,CAACO,MAAM,EAAED,SAAS,GAAG,CAAC;IAC5C,IAAIA,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGN,KAAK,CAACO,MAAM,GAAG,CAAC;IAC/C,OAAOP,KAAK,CAACM,SAAS,CAAC;EACzB,CAAC;EACD,MAAME,YAAY,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACnC,IAAID,GAAG,IAAI,IAAI,EAAE;IACjBzB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACyB,GAAG,EAAEC,KAAK,CAAC;IAChDnB,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACkB,GAAG,EAAEC,KAAK,CAAC;EAC9D,CAAC;EACD,MAAMC,aAAa,GAAGD,KAAK,IAAI;IAC7BvB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACuB,KAAK,CAAC;IAC7C,IAAI,CAAClB,UAAU,EAAE;MACf;IACF;IACA,IAAIoB,eAAe;IACnB,QAAQF,KAAK,CAACD,GAAG;MACf,KAAK,WAAW;MAChB,KAAK,SAAS;QACZG,eAAe,GAAGhB,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtC;MACF,KAAK,YAAY;MACjB,KAAK,WAAW;QACdgB,eAAe,GAAGhB,gBAAgB,CAAC,CAAC,CAAC;QACrC;MACF;QACE;IACJ;IACA,IAAI,CAACgB,eAAe,EAAE;IACtBF,KAAK,CAACG,cAAc,CAAC,CAAC;IACtBL,YAAY,CAACI,eAAe,CAACE,OAAO,CAAC1C,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,IAAI,EAAEsC,KAAK,CAAC;IAC1EpB,eAAe,CAACS,OAAO,GAAG,IAAI;IAC9BV,WAAW,CAAC,CAAC;EACf,CAAC;EACD1B,SAAS,CAAC,MAAM;IACd,IAAIgC,QAAQ,CAACI,OAAO,IAAIT,eAAe,CAACS,OAAO,EAAE;MAC/C,MAAME,WAAW,GAAGN,QAAQ,CAACI,OAAO,CAACG,aAAa,CAAC,IAAIzB,cAAc,uBAAuB,CAAC;MAC7FwB,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACc,KAAK,CAAC,CAAC;IACpD;IACAzB,eAAe,CAACS,OAAO,GAAG,KAAK;EACjC,CAAC,CAAC;EACF,MAAMiB,SAAS,GAAGlD,aAAa,CAACe,GAAG,EAAEc,QAAQ,CAAC;EAC9C,OAAO,aAAapB,IAAI,CAACP,iBAAiB,CAACiD,QAAQ,EAAE;IACnDC,KAAK,EAAEV,YAAY;IACnBW,QAAQ,EAAE,aAAa5C,IAAI,CAACR,UAAU,CAACkD,QAAQ,EAAE;MAC/CC,KAAK,EAAE;QACLhC,IAAI;QACJ;QACAD,SAAS,EAAEhB,YAAY,CAACgB,SAAS,CAAC;QAClCQ,eAAe,EAAEA,eAAe,IAAIjB,IAAI;QACxCkB,eAAe,EAAEA,eAAe,IAAIlB;MACtC,CAAC;MACD2C,QAAQ,EAAE,aAAa5C,IAAI,CAACQ,SAAS,EAAEqC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjC,KAAK,EAAE;QAC9DD,SAAS,EAAEwB,aAAa;QACxB9B,GAAG,EAAEmC,SAAS;QACd9B,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,GAAG,CAAC4C,WAAW,GAAG,KAAK;AACvB,eAAeF,MAAM,CAACC,MAAM,CAAC3C,GAAG,EAAE;EAChC6C,IAAI,EAAElD;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}